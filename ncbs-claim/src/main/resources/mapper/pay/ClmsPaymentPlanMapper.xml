<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentPlanMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentPlan" id="ClmsPaymentPlanMap">
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="createdDate" column="CREATED_DATE" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="UPDATED_BY" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="UPDATED_DATE" jdbcType="TIMESTAMP"/>
        <result property="idClmsPaymentPlan" column="ID_CLMS_PAYMENT_PLAN" jdbcType="VARCHAR"/>
        <result property="idClmPaymentItem" column="ID_CLM_PAYMENT_ITEM" jdbcType="VARCHAR"/>
        <result property="planCode" column="PLAN_CODE" jdbcType="VARCHAR"/>
        <result property="kindCode" column="KIND_CODE" jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
        <result property="productLineCode" column="PRODUCT_LINE_CODE" jdbcType="VARCHAR"/>
        <result property="planPayAmount" column="PLAN_PAY_AMOUNT" jdbcType="NUMERIC"/>
        <result property="noTaxAmount" column="no_tax_amount" jdbcType="NUMERIC"/>
        <result property="taxAmount" column="tax_amount" jdbcType="NUMERIC"/>
        <!-- 关联预估的责任信息-->
        <collection property="clmsPaymentDutyList" column="{idClmsPaymentPlan=ID_CLMS_PAYMENT_PLAN}" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentDuty"
                    select="com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentDutyMapper.getPaymentDutyList"/>
    </resultMap>

    <!--查询多个集合-->
    <select id="getClmsPaymentPlanList" resultMap="ClmsPaymentPlanMap">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_CLMS_PAYMENT_PLAN,
               ID_CLM_PAYMENT_ITEM,
               PLAN_CODE,
               KIND_CODE,
               PRODUCT_CODE,
               PRODUCT_LINE_CODE,
               PLAN_PAY_AMOUNT,
               no_tax_amount,
               tax_amount
        from clms_payment_plan
        where ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem}
        order by PLAN_CODE
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsPaymentPlanMap">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_CLMS_PAYMENT_PLAN,
               ID_CLM_PAYMENT_ITEM,
               PLAN_CODE,
               KIND_CODE,
               PRODUCT_CODE,
               PRODUCT_LINE_CODE,
               PLAN_PAY_AMOUNT,
               no_tax_amount,
               tax_amount
        from clms_payment_plan
        where ID_CLMS_PAYMENT_PLAN = #{idClmsPaymentPlan}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from clms_payment_plan
        <where>
            <if test="createdBy != null and createdBy != ''">
                and CREATED_BY = #{createdBy}
            </if>
            <if test="createdDate != null">
                and CREATED_DATE = #{createdDate}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and UPDATED_BY = #{updatedBy}
            </if>
            <if test="updatedDate != null">
                and UPDATED_DATE = #{updatedDate}
            </if>
            <if test="idClmsPaymentPlan != null and idClmsPaymentPlan != ''">
                and ID_CLMS_PAYMENT_PLAN = #{idClmsPaymentPlan}
            </if>
            <if test="idClmPaymentItem != null and idClmPaymentItem != ''">
                and ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem}
            </if>
            <if test="planCode != null and planCode != ''">
                and PLAN_CODE = #{planCode}
            </if>
            <if test="kindCode != null and kindCode != ''">
                and KIND_CODE = #{kindCode}
            </if>
            <if test="productCode != null and productCode != ''">
                and PRODUCT_CODE = #{productCode}
            </if>
            <if test="productLineCode != null and productLineCode != ''">
                and PRODUCT_LINE_CODE = #{productLineCode}
            </if>
            <if test="planPayAmount != null">
                and PLAN_PAY_AMOUNT = #{planPayAmount}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="idClmsPaymentPlan" useGeneratedKeys="true">
        insert into clms_payment_plan(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLM_PAYMENT_ITEM,
                                      PLAN_CODE, KIND_CODE, PRODUCT_CODE, PRODUCT_LINE_CODE, PLAN_PAY_AMOUNT,ID_CLMS_PAYMENT_PLAN,
                                      no_tax_amount,tax_amount)
        values (#{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate}, #{idClmPaymentItem}, #{planCode},
                #{kindCode}, #{productCode}, #{productLineCode}, #{planPayAmount},#{idClmsPaymentPlan},
                #{noTaxAmount},#{taxAmount})
    </insert>

    <insert id="insertBatch" keyProperty="idClmsPaymentPlan" useGeneratedKeys="true">
        insert into clms_payment_plan(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLM_PAYMENT_ITEM,
        PLAN_CODE, KIND_CODE, PRODUCT_CODE, PRODUCT_LINE_CODE, PLAN_PAY_AMOUNT,ID_CLMS_PAYMENT_PLAN,no_tax_amount,tax_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},
            #{entity.idClmPaymentItem}, #{entity.planCode}, #{entity.kindCode}, #{entity.productCode},
            #{entity.productLineCode}, #{entity.planPayAmount}, #{entity.idClmsPaymentPlan}, #{entity.noTaxAmount}, #{entity.taxAmount})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="idClmsPaymentPlan" useGeneratedKeys="true">
        insert into clms_payment_plan(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLM_PAYMENT_ITEM,
        PLAN_CODE, KIND_CODE, PRODUCT_CODE, PRODUCT_LINE_CODE, PLAN_PAY_AMOUNT,no_tax_amount,tax_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},
            #{entity.idClmPaymentItem}, #{entity.planCode}, #{entity.kindCode}, #{entity.productCode},
            #{entity.productLineCode}, #{entity.planPayAmount}, #{entity.noTaxAmount}, #{entity.taxAmount})
        </foreach>
        on duplicate key update
        CREATED_BY = values(CREATED_BY),
        CREATED_DATE = values(CREATED_DATE),
        UPDATED_BY = values(UPDATED_BY),
        UPDATED_DATE = values(UPDATED_DATE),
        ID_CLM_PAYMENT_ITEM = values(ID_CLM_PAYMENT_ITEM),
        PLAN_CODE = values(PLAN_CODE),
        KIND_CODE = values(KIND_CODE),
        PRODUCT_CODE = values(PRODUCT_CODE),
        PRODUCT_LINE_CODE = values(PRODUCT_LINE_CODE)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_payment_plan
        <set>
            <if test="createdBy != null and createdBy != ''">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate},
            </if>
            <if test="idClmPaymentItem != null and idClmPaymentItem != ''">
                ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem},
            </if>
            <if test="planCode != null and planCode != ''">
                PLAN_CODE = #{planCode},
            </if>
            <if test="kindCode != null and kindCode != ''">
                KIND_CODE = #{kindCode},
            </if>
            <if test="productCode != null and productCode != ''">
                PRODUCT_CODE = #{productCode},
            </if>
            <if test="productLineCode != null and productLineCode != ''">
                PRODUCT_LINE_CODE = #{productLineCode},
            </if>
            <if test="planPayAmount != null">
                PLAN_PAY_AMOUNT = #{planPayAmount},
            </if>
        </set>
        where ID_CLMS_PAYMENT_PLAN = #{idClmsPaymentPlan}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_payment_plan
        where ID_CLMS_PAYMENT_PLAN = #{idClmsPaymentPlan}
    </delete>

    <select id="getProductClass" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.pay.ProductPlanInfo">
        select plan.plan_code,
            plan.product_line_code,
            info.product_class
        from clm_payment_item item ,
            clms_payment_plan plan,
            plan_info info
        where item.id_clm_payment_item = #{paySerialNo}
        and item.id_clm_payment_item = plan.id_clm_payment_item
        and plan.plan_pay_amount <![CDATA[ <> ]]> 0
        and info.plan_code = plan.plan_code
        and info.status = '1'
    </select>
</mapper>

