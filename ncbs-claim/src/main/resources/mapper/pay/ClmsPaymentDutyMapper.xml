<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentDutyMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentDuty" id="ClmsPaymentDutyMap">
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="createdDate" column="CREATED_DATE" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="UPDATED_BY" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="UPDATED_DATE" jdbcType="TIMESTAMP"/>
        <result property="idClmsPaymentDuty" column="ID_CLMS_PAYMENT_DUTY" jdbcType="VARCHAR"/>
        <result property="idClmsPaymentPlan" column="ID_CLMS_PAYMENT_PLAN" jdbcType="VARCHAR"/>
        <result property="dutyCode" column="DUTY_CODE" jdbcType="VARCHAR"/>
        <result property="dutyPayAmount" column="DUTY_PAY_AMOUNT" jdbcType="NUMERIC"/>
        <result property="noTaxAmount" column="no_tax_amount" jdbcType="NUMERIC"/>
        <result property="taxAmount" column="tax_amount" jdbcType="NUMERIC"/>
    </resultMap>


    <!--查询单个-->
    <select id="getPaymentDutyList" resultMap="ClmsPaymentDutyMap">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_CLMS_PAYMENT_DUTY,
               ID_CLMS_PAYMENT_PLAN,
               DUTY_CODE,
               DUTY_PAY_AMOUNT,
               no_tax_amount,
               tax_amount
        from clms_payment_duty
        where ID_CLMS_PAYMENT_PLAN = #{idClmsPaymentPlan}
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsPaymentDutyMap">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_CLMS_PAYMENT_DUTY,
               ID_CLMS_PAYMENT_PLAN,
               DUTY_CODE,
               DUTY_PAY_AMOUNT,
               no_tax_amount,
               tax_amount
        from clms_payment_duty
        where ID_CLMS_PAYMENT_DUTY = #{idClmsPaymentDuty}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from clms_payment_duty
        <where>
            <if test="createdBy != null and createdBy != ''">
                and CREATED_BY = #{createdBy}
            </if>
            <if test="createdDate != null">
                and CREATED_DATE = #{createdDate}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and UPDATED_BY = #{updatedBy}
            </if>
            <if test="updatedDate != null">
                and UPDATED_DATE = #{updatedDate}
            </if>
            <if test="idClmsPaymentDuty != null and idClmsPaymentDuty != ''">
                and ID_CLMS_PAYMENT_DUTY = #{idClmsPaymentDuty}
            </if>
            <if test="idClmsPaymentPlan != null and idClmsPaymentPlan != ''">
                and ID_CLMS_PAYMENT_PLAN = #{idClmsPaymentPlan}
            </if>
            <if test="dutyCode != null and dutyCode != ''">
                and DUTY_CODE = #{dutyCode}
            </if>
            <if test="dutyPayAmount != null">
                and DUTY_PAY_AMOUNT = #{dutyPayAmount}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="idClmsPaymentDuty" useGeneratedKeys="true">
        insert into clms_payment_duty(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLMS_PAYMENT_PLAN,
                                      DUTY_CODE, DUTY_PAY_AMOUNT,ID_CLMS_PAYMENT_DUTY,no_tax_amount,tax_amount)
        values (#{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate}, #{idClmsPaymentPlan}, #{dutyCode},
                #{dutyPayAmount},#{idClmsPaymentDuty},#{noTaxAmount},#{taxAmount})
    </insert>

    <insert id="insertBatch" keyProperty="idClmsPaymentDuty" useGeneratedKeys="true">
        insert into clms_payment_duty(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLMS_PAYMENT_PLAN,
        DUTY_CODE, DUTY_PAY_AMOUNT,ID_CLMS_PAYMENT_DUTY,no_tax_amount,tax_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},
            #{entity.idClmsPaymentPlan}, #{entity.dutyCode}, #{entity.dutyPayAmount}, #{entity.idClmsPaymentDuty},#{entity.noTaxAmount},#{entity.taxAmount})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="idClmsPaymentDuty" useGeneratedKeys="true">
        insert into clms_payment_duty(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLMS_PAYMENT_PLAN,
        DUTY_CODE, DUTY_PAY_AMOUNT,no_tax_amount,tax_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},
            #{entity.idClmsPaymentPlan}, #{entity.dutyCode}, #{entity.dutyPayAmount},#{entity.noTaxAmount},#{entity.taxAmount})
        </foreach>
        on duplicate key update
        CREATED_BY = values(CREATED_BY),
        CREATED_DATE = values(CREATED_DATE),
        UPDATED_BY = values(UPDATED_BY),
        UPDATED_DATE = values(UPDATED_DATE),
        ID_CLMS_PAYMENT_PLAN = values(ID_CLMS_PAYMENT_PLAN),
        DUTY_CODE = values(DUTY_CODE)
    </insert>


    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_payment_duty
        where ID_CLMS_PAYMENT_DUTY = #{idClmsPaymentDuty}
    </delete>

    <select id="getPaymentDutyFee" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentDutyDTO">
        SELECT  d.DUTY_CODE dutyCode,
                d.DUTY_PAY_AMOUNT dutyPayAmount,
                d.no_tax_amount noTaxAmount,
                d.tax_amount taxAmount,
                p.KIND_CODE kindCode
        FROM clm_payment_item i, clms_payment_plan p, clms_payment_duty d
        WHERE i.ID_CLM_PAYMENT_ITEM = p.ID_CLM_PAYMENT_ITEM
        AND p.ID_CLMS_PAYMENT_PLAN = d.ID_CLMS_PAYMENT_PLAN
        AND i.CASE_NO = #{caseNo}
        AND i.CASE_TIMES = #{caseTimes}
        AND i.PAYMENT_TYPE = '1J'
        AND p.PLAN_CODE = #{planCode}
        AND d.DUTY_CODE = #{dutyCode}
    </select>

    <select id="getPaymentDutyByReportNo" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentPlanDutyDTO">
        SELECT
            n.plan_code,
            y.duty_code,
            y.duty_pay_amount,
            m.payment_type
        FROM
            clm_payment_item m,
            clms_payment_plan n,
            clms_payment_duty y
        WHERE
            m.REPORT_NO = #{reportNo}
            AND m.CASE_TIMES = #{caseTimes}
            AND m.ID_CLM_PAYMENT_ITEM = n.ID_CLM_PAYMENT_ITEM
            AND n.ID_CLMS_PAYMENT_PLAN = y.ID_CLMS_PAYMENT_PLAN
    </select>
</mapper>

