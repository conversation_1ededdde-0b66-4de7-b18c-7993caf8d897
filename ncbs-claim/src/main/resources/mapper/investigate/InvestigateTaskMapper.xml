<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO" id="InvestigateTaskMap">
		<id property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="investigatorUm" column="INVESTIGATOR_UM" />
		<result property="dispatchUm" column="DISPATCH_UM" />
		<result property="taskStatus" column="TASK_STATUS" />
		<result property="investigateDepartment" column="INVESTIGATE_DEPARTMENT" />
		<result property="isPrimaryTask" column="IS_PRIMARY_TASK" />
		<result property="isOffsiteTask" column="IS_OFFSITE_TASK" />
		<result property="dispatchOpinion" column="DISPATCH_OPINION" />
		<result property="finishDate" column="FINISH_DATE" />
		<result property="investigateConclusion" column="INVESTIGATE_CONCLUSION" />
		<result property="investigateQualitative" column="INVESTIGATE_QUALITATIVE" />
		<result property="abnormalDetail" column="ABNORMAL_DETAIL" />
		<result property="hasEvidence" column="HAS_EVIDENCE" />
		<result property="evidenceDetail" column="EVIDENCE_DETAIL" />
		<result property="idAhcsInvestigateAudit" column="ID_AHCS_INVESTIGATE_AUDIT" />
		<result property="fileId" column="file_id" />
		<result property="adjusterName" column="ADJUSTER_NAME" />
		<result property="adjusterPhone" column="ADJUSTER_PHONE" />
		<result property="lossReductionAmount" column="LOSS_REDUCTION_AMOUNT" />
	</resultMap>
	
	

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO" id="InvestigateTaskSimpleMap">
		<id property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="investigatorUm" column="INVESTIGATOR_UM" />
		<result property="dispatchUm" column="DISPATCH_UM" />
		<result property="taskStatus" column="TASK_STATUS" />
		<result property="investigateDepartment" column="INVESTIGATE_DEPARTMENT" />
		<result property="isPrimaryTask" column="IS_PRIMARY_TASK" />
		<result property="isOffsiteTask" column="IS_OFFSITE_TASK" />
		<result property="dispatchOpinion" column="DISPATCH_OPINION" />
		<result property="finishDate" column="FINISH_DATE" />
		<result property="investigateConclusion" column="INVESTIGATE_CONCLUSION" />
		<result property="investigateQualitative" column="INVESTIGATE_QUALITATIVE" />
		<result property="abnormalDetail" column="ABNORMAL_DETAIL" />
		<result property="hasEvidence" column="HAS_EVIDENCE" />
		<result property="evidenceDetail" column="EVIDENCE_DETAIL" />
		<result property="idAhcsInvestigateAudit" column="ID_AHCS_INVESTIGATE_AUDIT" />
		<result property="adjusterName" column="ADJUSTER_NAME" />
		<result property="adjusterPhone" column="ADJUSTER_PHONE" />
		<result property="lossReductionAmount" column="LOSS_REDUCTION_AMOUNT" />
	</resultMap>
	
	

	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO" id="InvestigateTaskLinkedMap">
	   <id property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="investigatorUm" column="INVESTIGATOR_UM" />
		<result property="dispatchUm" column="DISPATCH_UM" />
		<result property="taskStatus" column="TASK_STATUS" />
		<result property="investigateDepartment" column="INVESTIGATE_DEPARTMENT" />
		<result property="isPrimaryTask" column="IS_PRIMARY_TASK" />
		<result property="isOffsiteTask" column="IS_OFFSITE_TASK" />
		<result property="dispatchOpinion" column="DISPATCH_OPINION" />
		<result property="finishDate" column="FINISH_DATE" />
		<result property="investigateConclusion" column="INVESTIGATE_CONCLUSION" />
		<result property="investigateQualitative" column="INVESTIGATE_QUALITATIVE" />
		<result property="abnormalDetail" column="ABNORMAL_DETAIL" />
		<result property="hasEvidence" column="HAS_EVIDENCE" />
		<result property="evidenceDetail" column="EVIDENCE_DETAIL" />
		<result property="idAhcsInvestigateAudit" column="ID_AHCS_INVESTIGATE_AUDIT" />
		
	    <result property="investigatorUmName" column="INVESTIGATOR_UM_NAME" />
		<result property="dispatchUmName" column="DISPATCH_UM_NAME" />
		<result property="investigateQualitativeName" column="INVESTIGATE_QUALITATIVE_NAME" />
		<result property="abnormalDetailName" column="ABNORMAL_DETAIL_NAME" />
		<result property="investigateDepartmentName" column="INVESTIGATE_DEPARTMENT_NAME" />

	   <collection property="investigateAssistVOs" column="ID_AHCS_INVESTIGATE_TASK" ofType="com.paic.ncbs.claim.model.vo.investigate.InvestigateAssistVO"
	        select="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateAssistMapper.getInvestigateAssistByTaskId">
	    </collection>
	    
	     <collection property="investigateTaskAuditVOs" column="ID_AHCS_INVESTIGATE_TASK" ofType="com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO"
	        select="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditMapper.getInvestigateTaskAuditByTaskId">
	    </collection>
	    
	     <collection property="investigateProcessVOs" column="ID_AHCS_INVESTIGATE_TASK" ofType="com.paic.ncbs.claim.model.vo.investigate.InvestigateProcessVO"
	        select="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateProcessMapper.getInvestigateProcessByTaskId">
	    </collection>

	</resultMap>
	

	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO" id="InvestigateTaskVOMap">
	   <id property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="investigatorUm" column="INVESTIGATOR_UM" />
		<result property="dispatchUm" column="DISPATCH_UM" />
		<result property="taskStatus" column="TASK_STATUS" />
		<result property="investigateDepartment" column="INVESTIGATE_DEPARTMENT" />
		<result property="isPrimaryTask" column="IS_PRIMARY_TASK" />
		<result property="isOffsiteTask" column="IS_OFFSITE_TASK" />
		<result property="dispatchOpinion" column="DISPATCH_OPINION" />
		<result property="finishDate" column="FINISH_DATE" />
		<result property="investigateConclusion" column="INVESTIGATE_CONCLUSION" />
		<result property="investigateQualitative" column="INVESTIGATE_QUALITATIVE" />
		<result property="abnormalDetail" column="ABNORMAL_DETAIL" />
		<result property="hasEvidence" column="HAS_EVIDENCE" />
		<result property="evidenceDetail" column="EVIDENCE_DETAIL" />
		<result property="idAhcsInvestigateAudit" column="ID_AHCS_INVESTIGATE_AUDIT" />
		
		<result property="investigatorUmName" column="INVESTIGATOR_UM_NAME" />
		<result property="dispatchUmName" column="DISPATCH_UM_NAME" />
		<result property="investigateDepartmentName" column="INVESTIGATE_DEPARTMENT_NAME" />
		<result property="companyName" column="company_name" />
		<result property="surveyType" column="survey_type" />
		
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.user.UserDTO" id="userDTO">
		<id property="idAhcsUserInfo" column="ID_AHCS_USER_INFO" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="userId" column="USER_ID" />
		<result property="userName" column="USER_NAME" />
		<result property="userStatus" column="USER_STATUS" />
		<result property="email" column="EMAIL" />
		<result property="mobile" column="MOBILE" />
		<result property="isUseSoftphone" column="IS_USE_SOFTPHONE" />
		<result property="softphoneNo" column="SOFTPHONE_NO" />
		<result property="idAhcsCooperationCompany" column="ID_AHCS_COOPERATION_COMPANY" />	
		<result property="userType" column="USER_TYPE" />	
		<result property="userSubdivision" column="USER_SUBDIVISION" />	
		<result property="label" column="LABEL" />	
	</resultMap>
	
	

	<insert id="addInvestigateTask" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO" >
		INSERT INTO CLMS_INVESTIGATE_TASK (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_TASK,
			ID_AHCS_INVESTIGATE,
			REPORT_NO,
			CASE_TIMES,
			INVESTIGATOR_UM,
			DISPATCH_UM,
			TASK_STATUS,
			INVESTIGATE_DEPARTMENT,
			IS_PRIMARY_TASK,
			IS_OFFSITE_TASK,
			DISPATCH_OPINION,
			FINISH_DATE,
			INVESTIGATE_CONCLUSION,
			INVESTIGATE_QUALITATIVE,
			ABNORMAL_DETAIL,
			HAS_EVIDENCE,
			EVIDENCE_DETAIL,
			ID_AHCS_INVESTIGATE_AUDIT,
			INVESTIGATOR_UM_name,
			DISPATCH_UM_name,
			INVESTIGATE_DEPARTMENT_name,
			ARCHIVE_TIME,
			TASK_TYPE
		)
	VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigateTask ,jdbcType=VARCHAR},
			#{idAhcsInvestigate ,jdbcType=VARCHAR},
			#{reportNo ,jdbcType=VARCHAR},
			#{caseTimes ,jdbcType=NUMERIC},
			#{investigatorUm ,jdbcType=VARCHAR},
			#{dispatchUm ,jdbcType=VARCHAR},
			#{taskStatus ,jdbcType=VARCHAR},
			#{investigateDepartment ,jdbcType=VARCHAR},
			#{isPrimaryTask ,jdbcType=VARCHAR},
			#{isOffsiteTask ,jdbcType=VARCHAR},
			#{dispatchOpinion ,jdbcType=VARCHAR},
			#{finishDate ,jdbcType=DATE},
			#{investigateConclusion ,jdbcType=VARCHAR},
			#{investigateQualitative ,jdbcType=VARCHAR},
			#{abnormalDetail ,jdbcType=VARCHAR},
			#{hasEvidence ,jdbcType=VARCHAR},
			#{evidenceDetail ,jdbcType=VARCHAR},
			#{idAhcsInvestigateAudit ,jdbcType=VARCHAR},
			#{investigatorUmName},
			(select user_name from CLMS_user_info t where user_id=#{dispatchUm ,jdbcType=VARCHAR}),
			#{investigateDepartmentName},
			now(),
			#{taskType}
	)
	</insert>
	

	<insert id="addInvestigateTaskList" parameterType="java.util.List">
		<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">   
			INSERT INTO CLMS_INVESTIGATE_TASK (
				CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				ID_AHCS_INVESTIGATE_TASK,
				ID_AHCS_INVESTIGATE,
				REPORT_NO,
				CASE_TIMES,
				INVESTIGATOR_UM,
				DISPATCH_UM,
				TASK_STATUS,
				INVESTIGATE_DEPARTMENT,
				IS_PRIMARY_TASK,
				IS_OFFSITE_TASK,
				DISPATCH_OPINION,
				FINISH_DATE,
				INVESTIGATE_CONCLUSION,
				INVESTIGATE_QUALITATIVE,
				ABNORMAL_DETAIL,
				HAS_EVIDENCE,
				EVIDENCE_DETAIL,
				ID_AHCS_INVESTIGATE_AUDIT,
				INVESTIGATOR_UM_name,
				DISPATCH_UM_name,
				INVESTIGATE_DEPARTMENT_name,
				ARCHIVE_TIME
			)
		VALUES (
			#{item.createdBy ,jdbcType=VARCHAR},
			now(),
			#{item.updatedBy ,jdbcType=VARCHAR},
			now(),
			#{item.idAhcsInvestigateTask ,jdbcType=VARCHAR},
			#{item.idAhcsInvestigate ,jdbcType=VARCHAR},
			#{item.reportNo ,jdbcType=VARCHAR},
			#{item.caseTimes ,jdbcType=NUMERIC},
			#{item.investigatorUm ,jdbcType=VARCHAR},
			#{item.dispatchUm ,jdbcType=VARCHAR},
			#{item.taskStatus ,jdbcType=VARCHAR},
			#{item.investigateDepartment ,jdbcType=VARCHAR},
			#{item.isPrimaryTask ,jdbcType=VARCHAR},
			#{item.isOffsiteTask ,jdbcType=VARCHAR},
			#{item.dispatchOpinion ,jdbcType=VARCHAR},
			#{item.finishDate ,jdbcType=DATE},
			#{item.investigateConclusion ,jdbcType=VARCHAR},
			#{item.investigateQualitative ,jdbcType=VARCHAR},
			#{item.abnormalDetail ,jdbcType=VARCHAR},
			#{item.hasEvidence ,jdbcType=VARCHAR},
			#{item.evidenceDetail ,jdbcType=VARCHAR},
			#{item.idAhcsInvestigateAudit ,jdbcType=VARCHAR},
			(select user_name from CLMS_user_info t where user_id=#{item.investigatorUm ,jdbcType=VARCHAR}),
			(select user_name from CLMS_user_info t where user_id=#{item.dispatchUm ,jdbcType=VARCHAR}),
			(select department_abbr_name from department_define  where department_code=#{item.investigateDepartment ,jdbcType=VARCHAR}),
			now()
		)
		</foreach>
	</insert>
	

	<update id="modifyInvestigateTask" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO">
		UPDATE CLMS_INVESTIGATE_TASK
		<set>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy}, 
			</if>
			UPDATED_DATE=now(),
					
			<if test="investigatorUm != null and investigatorUm != '' ">
				INVESTIGATOR_UM  = #{investigatorUm},
			</if>	
			<if test="investigatorUmName != null and investigatorUmName != '' ">
				INVESTIGATOR_UM_NAME = #{investigatorUmName},
			</if>				
			<if test="dispatchUm != null and dispatchUm != '' ">
				DISPATCH_UM  = #{dispatchUm},
			</if>				
			<if test="taskStatus != null and taskStatus != '' ">
				TASK_STATUS  = #{taskStatus}, 
			</if>
					
			<if test="investigateDepartment != null and investigateDepartment != '' ">
				INVESTIGATE_DEPARTMENT  = #{investigateDepartment},
				INVESTIGATE_DEPARTMENT_name  = (select department_abbr_name from department_define  where department_code=#{investigateDepartment}),
			</if>
					
			<if test="isPrimaryTask != null and isPrimaryTask != '' ">
				IS_PRIMARY_TASK  = #{isPrimaryTask}, 
			</if>
					
			<if test="isOffsiteTask != null and isOffsiteTask != '' ">
				IS_OFFSITE_TASK  = #{isOffsiteTask}, 
			</if>
					
			<if test="dispatchOpinion != null and dispatchOpinion != '' ">
				DISPATCH_OPINION  = #{dispatchOpinion}, 
			</if>
					
			<if test="finishDate != null ">
				FINISH_DATE  = #{finishDate}, 
			</if>
					
			<if test="investigateConclusion != null and investigateConclusion != '' ">
				INVESTIGATE_CONCLUSION  = #{investigateConclusion}, 
			</if>
					
			<if test="investigateQualitative != null and investigateQualitative != '' ">
				INVESTIGATE_QUALITATIVE  = #{investigateQualitative}, 
			</if>
					
			<if test="abnormalDetail != null and abnormalDetail != '' ">
				ABNORMAL_DETAIL  = #{abnormalDetail}, 
			</if>
					
			<if test="hasEvidence != null and hasEvidence != '' ">
				HAS_EVIDENCE  = #{hasEvidence}, 
			</if>
					
			<if test="evidenceDetail != null and evidenceDetail != '' ">
				EVIDENCE_DETAIL  = #{evidenceDetail}, 
			</if>

			<if test="fileId != null and fileId != '' ">
				FILE_ID = #{fileId},
			</if>
			<if test="taskType != null and taskType != '' ">
				TASK_TYPE  = #{taskType}
			</if>
			
			<if test="adjusterName != null and adjusterName != '' ">
				ADJUSTER_NAME  = #{adjusterName},
			</if>
			
			<if test="adjusterPhone != null and adjusterPhone != '' ">
				ADJUSTER_PHONE  = #{adjusterPhone},
			</if>
			
			<if test="lossReductionAmount != null">
				LOSS_REDUCTION_AMOUNT  = #{lossReductionAmount},
			</if>
			
		</set>
		WHERE ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	</update>


	<update id="modifyInvestigateTaskWiThoutUpdatetime" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO">
		UPDATE CLMS_INVESTIGATE_TASK
		<set>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy},
			</if>
			<if test="investigatorUm != null and investigatorUm != '' ">
				INVESTIGATOR_UM  = #{investigatorUm},
			</if>

			<if test="dispatchUm != null and dispatchUm != '' ">
				DISPATCH_UM  = #{dispatchUm},
			</if>

			<if test="taskStatus != null and taskStatus != '' ">
				TASK_STATUS  = #{taskStatus},
			</if>

			<if test="investigateDepartment != null and investigateDepartment != '' ">
				INVESTIGATE_DEPARTMENT  = #{investigateDepartment},
				INVESTIGATE_DEPARTMENT_name  = (select department_abbr_name from department_define  where department_code=#{investigateDepartment}),
			</if>

			<if test="isPrimaryTask != null and isPrimaryTask != '' ">
				IS_PRIMARY_TASK  = #{isPrimaryTask},
			</if>

			<if test="isOffsiteTask != null and isOffsiteTask != '' ">
				IS_OFFSITE_TASK  = #{isOffsiteTask},
			</if>

			<if test="dispatchOpinion != null and dispatchOpinion != '' ">
				DISPATCH_OPINION  = #{dispatchOpinion},
			</if>

			<if test="finishDate != null ">
				FINISH_DATE  = #{finishDate},
			</if>

			<if test="investigateConclusion != null and investigateConclusion != '' ">
				INVESTIGATE_CONCLUSION  = #{investigateConclusion},
			</if>

			<if test="investigateQualitative != null and investigateQualitative != '' ">
				INVESTIGATE_QUALITATIVE  = #{investigateQualitative},
			</if>

			<if test="abnormalDetail != null and abnormalDetail != '' ">
				ABNORMAL_DETAIL  = #{abnormalDetail},
			</if>

			<if test="hasEvidence != null and hasEvidence != '' ">
				HAS_EVIDENCE  = #{hasEvidence},
			</if>

			<if test="evidenceDetail != null and evidenceDetail != '' ">
				EVIDENCE_DETAIL  = #{evidenceDetail},
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask}
	</update>

	<select id="getInvestigateTaskById" resultMap="InvestigateTaskMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_TASK,
			ID_AHCS_INVESTIGATE,
			REPORT_NO,
			CASE_TIMES,
			INVESTIGATOR_UM,
			DISPATCH_UM,
			TASK_STATUS,
			TASK_TYPE,
			INVESTIGATE_DEPARTMENT,
			IS_PRIMARY_TASK,
			IS_OFFSITE_TASK,
			DISPATCH_OPINION,
			FINISH_DATE,
			INVESTIGATE_CONCLUSION,
			INVESTIGATE_QUALITATIVE,
			ABNORMAL_DETAIL,
			HAS_EVIDENCE,
			EVIDENCE_DETAIL,
			ID_AHCS_INVESTIGATE_AUDIT
		from CLMS_INVESTIGATE_TASK
		where  ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	</select>	

	<select id="getInvestigateTaskLinkedByTaskId" resultMap="InvestigateTaskLinkedMap" >  
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INVESTIGATOR_UM,
			t.INVESTIGATOR_UM_NAME,
			t.DISPATCH_UM,
			t.TASK_STATUS,
			t.INVESTIGATE_DEPARTMENT,
			t.IS_PRIMARY_TASK,
			t.IS_OFFSITE_TASK,
			t.DISPATCH_OPINION,
			t.FINISH_DATE,
			t.INVESTIGATE_CONCLUSION,
			t.INVESTIGATE_QUALITATIVE,
			t.ABNORMAL_DETAIL,
			t.HAS_EVIDENCE,
			t.EVIDENCE_DETAIL,
			ID_AHCS_INVESTIGATE_AUDIT,
			t.ADJUSTER_NAME,
			t.ADJUSTER_PHONE,
			t.LOSS_REDUCTION_AMOUNT,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME
		 from CLMS_INVESTIGATE_TASK t
		where  t.ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	</select> 
	
	
	

	<select id="getInvestigateTaskByInvestigateId" resultMap="InvestigateTaskVOMap">
		select 
		    CREATED_BY,
		    CREATED_DATE,
		    UPDATED_BY,
		    UPDATED_DATE,
		    ID_AHCS_INVESTIGATE_TASK,
		    ID_AHCS_INVESTIGATE,
		    REPORT_NO,
		    CASE_TIMES,
		    INVESTIGATOR_UM,
		    DISPATCH_UM,
		    TASK_STATUS,
		    INVESTIGATE_DEPARTMENT,
		    IS_PRIMARY_TASK,
		    IS_OFFSITE_TASK,
		    DISPATCH_OPINION,
		    FINISH_DATE,
		    INVESTIGATE_CONCLUSION,
		    INVESTIGATE_QUALITATIVE,
		    ABNORMAL_DETAIL,
		    HAS_EVIDENCE,
		    EVIDENCE_DETAIL,
		    ID_AHCS_INVESTIGATE_AUDIT,
		    IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.DISPATCH_UM limit 1),t.DISPATCH_UM) as DISPATCH_UM_NAME,
		    IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INVESTIGATOR_UM limit 1),t.INVESTIGATOR_UM) as INVESTIGATOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME,
			IFNULL((select a.company_name
             from CLMS_investigate_task_audit a
            where a.id_ahcs_investigate_task = t.id_ahcs_investigate_task
              and a.review_opinion = '1'
              limit 1),
           (select a.company_name
              from CLMS_investigate_audit a
             where a.id_ahcs_investigate_audit = t.id_ahcs_investigate_audit
               and a.audit_opinion = '分配调查'
               limit 1)) company_name,
			IFNULL((select a.survey_type
				 from CLMS_investigate_task_audit a
				 where a.id_ahcs_investigate_task = t.id_ahcs_investigate_task
				   and a.review_opinion = '1'
				   limit 1),
				(select a.survey_type
				 from CLMS_investigate_audit a
				 where a.id_ahcs_investigate_audit = t.id_ahcs_investigate_audit
				   and a.audit_opinion = '分配调查'
				   limit 1)) survey_type
		from CLMS_INVESTIGATE_TASK t
		where t.ID_AHCS_INVESTIGATE=#{idAhcsInvestigate}  
	    order by t.IS_PRIMARY_TASK desc,t.IS_OFFSITE_TASK  desc
	</select>
	
	

	<select id="getMajorInvestigateTaskLinkedByInvestigateId" resultMap="InvestigateTaskLinkedMap" > 
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INVESTIGATOR_UM,
			t.DISPATCH_UM,
			t.TASK_STATUS,
			t.INVESTIGATE_DEPARTMENT,
			t.IS_PRIMARY_TASK,
			t.IS_OFFSITE_TASK,
			t.DISPATCH_OPINION,
			t.FINISH_DATE,
			t.INVESTIGATE_CONCLUSION,
			t.INVESTIGATE_QUALITATIVE,
			t.ABNORMAL_DETAIL,
			t.HAS_EVIDENCE,
			t.EVIDENCE_DETAIL,
			ID_AHCS_INVESTIGATE_AUDIT,
			
			IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INVESTIGATOR_UM limit 1),t.INVESTIGATOR_UM) as INVESTIGATOR_UM_NAME,
		    IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.DISPATCH_UM limit 1),t.DISPATCH_UM) as DISPATCH_UM_NAME,
	        (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_QUALITATIVE limit 1) as INVESTIGATE_QUALITATIVE_NAME,
	        (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.ABNORMAL_DETAIL limit 1) as ABNORMAL_DETAIL_NAME,
	        (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME
	    
		from CLMS_INVESTIGATE_TASK t
		where  t.IS_PRIMARY_TASK='Y' 
		and t.id_ahcs_investigate=#{idAhcsInvestigate}
	</select>
	

	<select id="getMajorInvestigateTaskByInvestigateId" resultMap="InvestigateTaskMap" > 
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INVESTIGATOR_UM,
			t.DISPATCH_UM,
			t.TASK_STATUS,
			t.INVESTIGATE_DEPARTMENT,
			t.IS_PRIMARY_TASK,
			t.IS_OFFSITE_TASK,
			t.DISPATCH_OPINION,
			t.FINISH_DATE,
			t.INVESTIGATE_CONCLUSION,
			t.INVESTIGATE_QUALITATIVE,
			t.ABNORMAL_DETAIL,
			t.HAS_EVIDENCE,
			t.EVIDENCE_DETAIL,
			t.ID_AHCS_INVESTIGATE_AUDIT,
			t.file_id
		from CLMS_INVESTIGATE_TASK t
		where  t.IS_PRIMARY_TASK='Y' 
		and t.id_ahcs_investigate=#{idAhcsInvestigate}
	</select>
	

	<select id="getAssistInvestigateTaskAllByMajorTaskId" resultMap="InvestigateTaskLinkedMap" > 
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INVESTIGATOR_UM,
			t.DISPATCH_UM,
			t.TASK_STATUS,
			t.INVESTIGATE_DEPARTMENT,
			t.IS_PRIMARY_TASK,
			t.IS_OFFSITE_TASK,
			t.DISPATCH_OPINION,
			t.FINISH_DATE,
			t.INVESTIGATE_CONCLUSION,
			t.INVESTIGATE_QUALITATIVE,
			t.ABNORMAL_DETAIL,
			t.HAS_EVIDENCE,
			t.EVIDENCE_DETAIL,
			ID_AHCS_INVESTIGATE_AUDIT,

		(select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INVESTIGATOR_UM limit 1) as INVESTIGATOR_UM_NAME,
		(select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.DISPATCH_UM limit 1) as DISPATCH_UM_NAME,

		(select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_QUALITATIVE limit 1) as INVESTIGATE_QUALITATIVE_NAME,
	        (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.ABNORMAL_DETAIL limit 1) as ABNORMAL_DETAIL_NAME,
		(select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME

		from CLMS_INVESTIGATE_TASK t
		where  t.IS_PRIMARY_TASK='N' and t.ID_AHCS_INVESTIGATE=(
	   	 select t2.ID_AHCS_INVESTIGATE from CLMS_INVESTIGATE_TASK t2 where t2.id_ahcs_investigate_task=#{idAhcsInvestigateTask}
	    ) 
	    <if test="isOffsiteTask != null and isOffsiteTask != '' ">
			AND IS_OFFSITE_TASK  = #{isOffsiteTask,jdbcType=VARCHAR} 
		</if>
	</select>
	

	<select id="getCountUnfinishedTaskByIdAhcsInvestigate" resultType="int">
		select count(1)
		  from CLMS_INVESTIGATE_TASK
		 where ID_AHCS_INVESTIGATE = #{idAhcsInvestigate}
		   and TASK_STATUS != '5'
	</select>
	

	<select id="getCountNlAuditingByIdAhcsInvestigate" resultType="int">
		select count(1)
		  from CLMS_INVESTIGATE_AUDIT
		 where AUDIT_TYPE = '01'
		   and audit_opinion is null
		   and ID_AHCS_INVESTIGATE = #{idAhcsInvestigate}

	</select>
	

	<select id="getInvestigateTaskDTOList" resultMap="InvestigateTaskMap">
		select f.id_ahcs_investigate_task
		  from CLMS_investigate_task f
		 where f.report_no = #{reportNo,jdbcType=VARCHAR} 
		   and f.case_times = #{caseTimes,jdbcType=VARCHAR} 
		   and f.task_status != '5'
	</select>
	
	
	

	<select id="getLocalMajorManageByInvestigateId" resultMap="userDTO">
		select 
			t.DISPATCH_UM user_id
	      from CLMS_investigate_task t
		 where t.id_ahcs_investigate = #{idAhcsInvestigate}
		   and t.is_primary_task = 'Y'
	</select>
	

	<select id="getInvestigateTask" resultMap="InvestigateTaskMap">
		select f.ID_AHCS_INVESTIGATE_TASK,
		       f.IS_PRIMARY_TASK,
		       f.IS_OFFSITE_TASK,
		       f.INVESTIGATE_DEPARTMENT 
		  from CLMS_INVESTIGATE_TASK f
		 where f.report_no = #{reportNo,jdbcType=VARCHAR} 
		   and f.case_times = #{caseTimes,jdbcType=VARCHAR}
		   order by f.UPDATED_DATE desc
		limit 1
	</select>
	

	<select id="getInvestigationEndDate" resultType="String">
		select date_format(t.UPDATED_DATE,'%Y-%m-%d %T') UPDATED_DATE
		  from CLMS_investigate_task_audit t
		 where t.id_ahcs_investigate_task = #{idAhcsInvestigateTask}
		   limit 1
	</select>
	

	<select id="getInvestigationEndDay" parameterType="string" resultType="string">
		select date_format(t.UPDATED_DATE,'%Y%m%d') UPDATED_DATE
		  from CLMS_investigate_task_audit t
		 where t.id_ahcs_investigate_task = #{idAhcsInvestigateTask}
		   limit 1
	</select>
	

	<update id="modifyForWorkTransfer" parameterType="String">
		update CLMS_investigate_task t
		   set t.INVESTIGATOR_UM = #{newUM ,jdbcType=VARCHAR},INVESTIGATOR_UM_name=(select user_name from CLMS_user_info t where user_id=#{newUM ,jdbcType=VARCHAR}),
		   UPDATED_DATE=now()
		 where t.INVESTIGATOR_UM = #{oldUM ,jdbcType=VARCHAR}
	       and t.task_status != '5'
	</update>
	

	<select id="getMajorTaskIdByInvestigateId" resultType="String">
		select t.id_ahcs_investigate_task
		  from CLMS_investigate_task t
		 where t.is_primary_task = 'Y'
		   and t.id_ahcs_investigate = #{idAhcsInvestigate}
	</select>
	

	<select id="getTaskIdByInvestigateId" resultType="String">
		select t.id_ahcs_investigate_task
		  from CLMS_investigate_task t
		 where t.id_ahcs_investigate = #{idAhcsInvestigate}
	</select>


    <select id="getCompanyByIdAhcsInvestigate"
            resultType="com.paic.ncbs.claim.model.vo.investigate.InvestigateCooperationCompanyVO">
		select socialCreditCode, companyName
		from (select SOCIAL_CREDIT_CODE socialCreditCode,
					 COMPANY_NAME companyName,
					 ROW_NUMBER() OVER(PARTITION BY SOCIAL_CREDIT_CODE ORDER BY updated_date DESC) rn
			  from CLMS_COOPERATION_COMPANY
			  where DEPARTMENT_CODE in
					(select INVESTIGATE_DEPARTMENT from  CLMS_INVESTIGATE  where id_ahcs_investigate = #{idAhcsInvestigate})
				and IS_EFFECTIVE = 'Y'
				and COMPANY_TYPE = 'C')
		where rn = 1
		  and socialCreditCode is not null

	</select>
	<select id="getCompanyByIdAhcsInvestigateTask"
			resultType="com.paic.ncbs.claim.model.vo.investigate.InvestigateCooperationCompanyVO">
		select socialCreditCode, companyName
		from (
				 select SOCIAL_CREDIT_CODE socialCreditCode,
						COMPANY_NAME       companyName,
						ROW_NUMBER() OVER(PARTITION BY SOCIAL_CREDIT_CODE ORDER BY updated_date DESC) rn
				 from CLMS_COOPERATION_COMPANY
				 where DEPARTMENT_CODE in
					   (  select INVESTIGATE_DEPARTMENT from CLMS_INVESTIGATE_TASK where ID_AHCS_INVESTIGATE_TASK   = #{idAhcsInvestigateTask} )
				   and IS_EFFECTIVE = 'Y' and COMPANY_TYPE = 'C'
			 )
		where rn = 1 and socialCreditCode is not null

	</select>
    <select id="getCompanyByIdAhcsInvestigateAudit"
            resultType="com.paic.ncbs.claim.model.vo.investigate.InvestigateCooperationCompanyVO">
		select socialCreditCode, companyName
		from (
				 select SOCIAL_CREDIT_CODE socialCreditCode,
						COMPANY_NAME       companyName,
						ROW_NUMBER() OVER(PARTITION BY SOCIAL_CREDIT_CODE ORDER BY updated_date DESC) rn
				 from CLMS_COOPERATION_COMPANY
				 where DEPARTMENT_CODE in
					   (select INVESTIGATE_DEPARTMENT
						from CLMS_INVESTIGATE_AUDIT
						where ID_AHCS_INVESTIGATE_AUDIT =
							  #{idAhcsInvestigateAudit} )
				   and IS_EFFECTIVE = 'Y' and COMPANY_TYPE = 'C'
			 )
		where rn = 1 and socialCreditCode is not null
	</select>

	<select id="checkExist" resultType="java.lang.Integer">
		select count(*)  from  CLMS_investigate_task where id_ahcs_investigate = #{investigateAudit.idAhcsInvestigate}  and DISPATCH_UM =#{investigateAudit.auditorUm}
	</select>

</mapper>
