<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO" id="InvestigateTaskAuditMap">
		<id property="idAhcsInvestigateTaskAudit" column="ID_AHCS_INVESTIGATE_TASK_AUDIT" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="reviewUserUm" column="REVIEW_USER_UM" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="reviewOpinion" column="REVIEW_OPINION" />
		<result property="rejectReason" column="REJECT_REASON" />
		<result property="description" column="DESCRIPTION" />
		<result property="commonEstimateFee" column="common_estimate_fee" />
		<result property="isHasAdjustingFee" column="is_has_adjusting_fee" />
		<result property="adjusterName" column="ADJUSTER_NAME" />
		<result property="adjusterPhone" column="ADJUSTER_PHONE" />
		<result property="lossReductionAmount" column="LOSS_REDUCTION_AMOUNT" />
		<result property="transferToUser" column="TRANSFER_TO_USER" />
		<result property="transferReason" column="TRANSFER_REASON" />
		<result property="transferBy" column="TRANSFER_BY" />
		<result property="transferDate" column="TRANSFER_DATE" />
	</resultMap>


	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO" extends="InvestigateTaskAuditMap" id="InvestigateTaskAuditVOMap">
		
		<result property="reviewUserUmName" column="REVIEW_USER_UM_NAME" />
		<result property="initiatorUmName" column="INITIATOR_UM_NAME" />
		<result property="rejectReasonName" column="REJECT_REASON_NAME" />
		<result property="isHasAdjustingFee" column="IS_HAS_ADJUSTING_FEE" />
		
		
	</resultMap>


	<insert id="addTaskAudit" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO" >
		INSERT INTO CLMS_investigate_task_audit (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_TASK_AUDIT,
			ID_AHCS_INVESTIGATE_TASK,
			REVIEW_USER_UM,
			INITIATOR_UM,
			REVIEW_OPINION,
			REJECT_REASON,
			DESCRIPTION,
			REVIEW_USER_UM_name,
			INITIATOR_UM_name,
			ARCHIVE_TIME
		)
		VALUES (
				#{createdBy ,jdbcType=VARCHAR},
				now(),
				#{updatedBy ,jdbcType=VARCHAR},
				now(),
				#{idAhcsInvestigateTaskAudit ,jdbcType=VARCHAR},
				#{idAhcsInvestigateTask ,jdbcType=VARCHAR},
				#{reviewUserUm ,jdbcType=VARCHAR},
				#{initiatorUm ,jdbcType=VARCHAR},
				#{reviewOpinion ,jdbcType=VARCHAR},
				#{rejectReason ,jdbcType=VARCHAR},
				#{description ,jdbcType=VARCHAR},
				(select user_name from CLMS_user_info t where user_id=#{reviewUserUm ,jdbcType=VARCHAR}),
				(select user_name from CLMS_user_info t where user_id=#{initiatorUm ,jdbcType=VARCHAR}),
				now()
		)
	</insert>


	<update id="modifyTaskAudit" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO">
		UPDATE CLMS_investigate_task_audit
		<set>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy}, 
			</if>
			UPDATED_DATE=now(),

			<if test="reviewUserUm != null and reviewUserUm != '' ">
				REVIEW_USER_UM  = #{reviewUserUm},
				REVIEW_USER_UM_name  = #{reviewUserUm},
			</if>

			<if test="reviewOpinion != null and reviewOpinion != '' ">
				REVIEW_OPINION  = #{reviewOpinion}, 
			</if>
					
			<if test="rejectReason != null and rejectReason != '' ">
				REJECT_REASON  = #{rejectReason}, 
			</if>
					
			<if test="description != null and description != '' ">
				DESCRIPTION  = #{description}, 
			</if>

			<if test="surveyType != null and surveyType != '' ">
				SURVEY_TYPE  = #{surveyType},
			</if>

			<if test="socialCreditCode != null and socialCreditCode != '' ">
				SOCIAL_CREDIT_CODE  = #{socialCreditCode},
			</if>

			<if test="companyName != null and companyName != '' ">
				COMPANY_NAME  = #{companyName},
			</if>
			<if test="isHasAdjustingFee != null and isHasAdjustingFee != '' ">
				is_has_adjusting_fee  = #{isHasAdjustingFee},
			</if>
			<if test="commonEstimateFee != null and commonEstimateFee != '' ">
				common_estimate_fee  = #{commonEstimateFee},
			</if>
			<if test="initiatorUm != null and initiatorUm != '' ">
				INITIATOR_UM  = #{initiatorUm},
			</if>
			
			<if test="adjusterName != null and adjusterName != '' ">
				ADJUSTER_NAME  = #{adjusterName},
			</if>
			
			<if test="adjusterPhone != null and adjusterPhone != '' ">
				ADJUSTER_PHONE  = #{adjusterPhone},
			</if>
			
			<if test="lossReductionAmount != null">
				LOSS_REDUCTION_AMOUNT  = #{lossReductionAmount},
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE_TASK_AUDIT=#{idAhcsInvestigateTaskAudit} 
	</update>
	
	
	

	<update id="modifyTaskAuditByTaskId" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO">
		UPDATE CLMS_investigate_task_audit
		<set>

			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy}, 
			</if>
			UPDATED_DATE=now(),
					
			<if test="reviewUserUm != null and reviewUserUm != '' ">
				REVIEW_USER_UM  = #{reviewUserUm}, 
			</if>

			<if test="reviewOpinion != null and reviewOpinion != '' ">
				REVIEW_OPINION  = #{reviewOpinion}, 
			</if>
					
			<if test="rejectReason != null and rejectReason != '' ">
				REJECT_REASON  = #{rejectReason}, 
			</if>
					
			<if test="description != null and description != '' ">
				DESCRIPTION  = #{description}, 
			</if>

			<if test="isHasAdjustingFee != null and isHasAdjustingFee != '' ">
				is_has_adjusting_fee  = #{isHasAdjustingFee},
			</if>

			<if test="commonEstimateFee != null and commonEstimateFee != '' ">
				common_estimate_fee  = #{commonEstimateFee},
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
		and UPDATED_DATE=(select max(UPDATED_DATE) from CLMS_investigate_task_audit where ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} )
	</update>



	<select id="getInvestigateTaskAuditByTaskId" resultMap="InvestigateTaskAuditVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_TASK_AUDIT,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.REVIEW_USER_UM,
			t.INITIATOR_UM,
			t.REVIEW_OPINION,
			t.REJECT_REASON,
			t.DESCRIPTION,
		    t.common_estimate_fee,
		    t.is_has_adjusting_fee,
			(select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.REVIEW_USER_UM limit 1) as REVIEW_USER_UM_NAME,
		    (select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1) as INITIATOR_UM_NAME,
		    (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.REJECT_REASON limit 1) as REJECT_REASON_NAME
	  from CLMS_investigate_task_audit t
	 where ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	   and t.review_opinion is not null	
	</select>


	<select id="getInvestigateTaskAuditForReportByInvestigateId" resultMap="InvestigateTaskAuditVOMap">
		select * from (
			select 
				t.CREATED_BY,
				t.CREATED_DATE,
				t.UPDATED_BY,
				t.UPDATED_DATE,
				t.ID_AHCS_INVESTIGATE_TASK_AUDIT,
				t.ID_AHCS_INVESTIGATE_TASK,
				t.REVIEW_USER_UM,
				t.INITIATOR_UM,
				t.REVIEW_OPINION,
				t.REJECT_REASON,
				t.DESCRIPTION,
				t.is_has_adjusting_fee ,t.common_estimate_fee ,
				IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.REVIEW_USER_UM limit 1),t.REVIEW_USER_UM) as REVIEW_USER_UM_NAME,
			    IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1),t.INITIATOR_UM) as INITIATOR_UM_NAME,
		    (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.REJECT_REASON limit 1) as REJECT_REASON_NAME
			from CLMS_investigate_task_audit t
			where t.id_ahcs_investigate_task=(
			       select a.ID_AHCS_INVESTIGATE_TASK from CLMS_investigate_task a where a.id_ahcs_investigate=#{idAhcsInvestigate}  and a.IS_PRIMARY_TASK= 'Y'
			) order by t.updated_date desc 
		) aa limit 1
	</select>
	
	

    <select id="getTaskAuditIdByTaskId" resultType="String">
        select t.id_ahcs_investigate_task_audit
          from CLMS_investigate_task_audit t
        where t.id_ahcs_investigate_task = #{idAhcsInvestigateTask}
          and t.review_opinion is null
		limit 1
    </select>


	<update id="modifyForWorkTransfer" parameterType="String">
		update CLMS_investigate_task_audit t
		   set t.review_user_um = #{newUM ,jdbcType=VARCHAR},
		   UPDATED_DATE=now()
		 where t.review_user_um = #{oldUM ,jdbcType=VARCHAR}
		   and t.REVIEW_OPINION is null
	</update>


	<select id="listInvestigateTaskAuditsByTaskId" resultMap="InvestigateTaskAuditVOMap">
		select
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_TASK_AUDIT,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.REVIEW_USER_UM,
			t.INITIATOR_UM,
			t.REVIEW_OPINION,
			t.REJECT_REASON,
			t.DESCRIPTION,

			(select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.REVIEW_USER_UM limit 1) as REVIEW_USER_UM_NAME,
		    (select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1) as INITIATOR_UM_NAME,
		    (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.REJECT_REASON limit 1) as REJECT_REASON_NAME
	  from CLMS_investigate_task_audit t
	 where ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask}
	</select>

	<select id="getApproveInfoByIdAhcsInvestigateTask"
			resultType="com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO">
		select survey_type surveyType,social_credit_code socialCreditCode,company_name  companyName
		from CLMS_investigate_audit
		where id_ahcs_investigate_audit in
			  (select id_ahcs_investigate_audit
			   from CLMS_investigate_task
			   where id_ahcs_investigate_task = #{idAhcsInvestigateTask})
	</select>

	<select id="getTaskAuditIdByTaskId2" resultType="String">
		select t.id_ahcs_investigate_task
		from CLMS_investigate_task_audit t
		where t.id_ahcs_investigate_task_audit = #{idAhcsInvestigateTaskAudit}
	</select>

</mapper>