<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pet.PetInjureMapper">

    <insert id="addPetInjure" parameterType="com.paic.ncbs.claim.model.dto.pet.PetInjureDTO">
        INSERT INTO CLMS_PET_INJURE(
          CREATED_BY,
          CREATED_DATE,
          UPDATED_BY,
          UPDATED_DATE,
          ID_CLMS_PET_INJURE,
          REPORT_NO,
          CASE_TIMES,
          TYPE_AND_AMOUNT,
          ACCIDENT_PET,
          ACCIDENT_DATE,
          ACCIDENT_OVERSEAS,
          ACCIDENT_PROVINCE_CODE,
          ACCIDENT_CITY_CODE,
          ACCIDENT_COUNTRY_CODE,
          ACCIDENT_CONTINENT_CODE,
          ACCIDENT_PLACE,
          TASK_CODE,
          STATUS,
          TARGET_JUDGE_FLAG,
          DISEASE_CLASS,
          TREATMENT_DETAIL,
          DISEASE_DIAGNOSIS,
          DIAGNOSIS_DETAIL,
          DETAIL_DESC)
        VALUES(
          #{createdBy,jdbcType=VARCHAR},
          now(),
          #{createdBy,jdbcType=VARCHAR},
          now(),
          #{idClmsPetInjure,jdbcType=VARCHAR},
          #{reportNo,jdbcType=VARCHAR},
          #{caseTimes,jdbcType=INTEGER},
          #{typeAndAmount,jdbcType=VARCHAR},
          #{accidentPet,jdbcType=TIMESTAMP},
          #{accidentDate,jdbcType=TIMESTAMP},
          #{accidentOverseas,jdbcType=VARCHAR},
          #{accidentProvinceCode,jdbcType=VARCHAR},
          #{accidentCityCode,jdbcType=VARCHAR},
          #{accidentCountryCode,jdbcType=VARCHAR},
          #{accidentContinentCode,jdbcType=VARCHAR},
          #{accidentPlace,jdbcType=VARCHAR},
          #{taskCode,jdbcType=VARCHAR},
          #{status,jdbcType=VARCHAR},
          #{targetJudgeFlag,jdbcType=VARCHAR},
          #{diseaseClass,jdbcType=VARCHAR},
          #{treatmentDetail,jdbcType=VARCHAR},
          #{diseaseDiagnosis,jdbcType=VARCHAR},
          #{diagnosisDetail,jdbcType=VARCHAR},
          #{detailDesc,jdbcType=VARCHAR}
          )
    </insert>

    <select id="getPetInjure" resultType="com.paic.ncbs.claim.model.dto.pet.PetInjureDTO" parameterType="com.paic.ncbs.claim.model.dto.pet.PetInjureDTO">
        SELECT ID_CLMS_PET_INJURE idClmsPetInjure,
               REPORT_NO reportNo,
               CASE_TIMES caseTimes,
               TYPE_AND_AMOUNT typeAndAmount,
               ACCIDENT_PET accidentPet,
               ACCIDENT_DATE accidentDate,
               ACCIDENT_OVERSEAS accidentOverseas,
               ACCIDENT_PROVINCE_CODE accidentProvinceCode,
               ACCIDENT_CITY_CODE accidentCityCode,
               ACCIDENT_COUNTRY_CODE accidentCountryCode,
               ACCIDENT_CONTINENT_CODE accidentContinentCode,
               ACCIDENT_PLACE accidentPlace,
               TASK_CODE taskCode,
               STATUS status,
               TARGET_JUDGE_FLAG targetJudgeFlag,
               DISEASE_CLASS diseaseClass,
               TREATMENT_DETAIL treatmentDetail,
               DISEASE_DIAGNOSIS diseaseDiagnosis,
               DIAGNOSIS_DETAIL diagnosisDetail,
               DETAIL_DESC detailDesc
        FROM CLMS_PET_INJURE
        WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        AND STATUS = #{status,jdbcType=VARCHAR}
        AND IS_EFFECTIVE = 'Y'
        <if test="taskCode != null and taskCode != ''">
            AND TASK_CODE = #{taskCode,jdbcType=VARCHAR}
        </if>
        ORDER BY CREATED_DATE DESC
        LIMIT 1
    </select>

    <update id="delPetInjure" parameterType="com.paic.ncbs.claim.model.dto.pet.PetInjureDTO">
        UPDATE CLMS_PET_INJURE
        SET UPDATED_DATE   = now(),
            UPDATED_BY     = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE   = 'N'
        WHERE REPORT_NO    = #{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES   = #{caseTimes,jdbcType=INTEGER}
        AND TASK_CODE    = #{taskCode,jdbcType=VARCHAR}
        AND IS_EFFECTIVE = 'Y'
    </update>

    <update id="delPetInjureById" parameterType="com.paic.ncbs.claim.model.dto.pet.PetInjureDTO">
        UPDATE CLMS_PET_INJURE
        SET UPDATED_DATE   = now(),
            UPDATED_BY     = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE   = 'N'
        WHERE ID_CLMS_PET_INJURE = #{idClmsPetInjure,jdbcType=VARCHAR}
    </update>

</mapper>