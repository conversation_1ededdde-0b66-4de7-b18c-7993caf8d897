<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pet.PetInjureExMapper">
    <insert id="addPetInjureExList" parameterType="java.util.List">
        INSERT INTO CLMS_PET_INJURE_EX (
          CREATED_BY,
          CREATED_DATE,
          UPDATED_BY,
          UPDATED_DATE,
          ID_CLMS_PET_INJURE_EX,
          ID_CLMS_PET_INJURE,
          TREATMENT_SCHEME,
          TREATMENT_OPERATION,
          TREATMENT_START_DATE,
          TREATMENT_END_DATE,
          TREATMENT_DAYS,
          PET_HOS_PROVINCE_CODE,
          PET_HOS_CITY_CODE,
          PET_HOS_COUNTRY_CODE,
          PET_HOS_CODE,
          PET_HOS_NAME,
          TREATMENT_AMOUNT)
        VALUES
        <foreach collection="petInjureExList" separator="," index="index" item="item">
          (
           #{item.createdBy,jdbcType=VARCHAR},
           now(),
           #{item.createdBy,jdbcType=VARCHAR},
           now(),
           #{item.idClmsPetInjureEx, jdbcType=VARCHAR},
           #{item.idClmsPetInjure,jdbcType=VARCHAR},
           #{item.treatmentScheme,jdbcType=VARCHAR},
           #{item.treatmentOperation,jdbcType=VARCHAR},
           #{item.treatmentStartDate,jdbcType=TIMESTAMP},
           #{item.treatmentEndDate,jdbcType=TIMESTAMP},
           #{item.treatmentDays,jdbcType=DECIMAL},
           #{item.petHosProvinceCode,jdbcType=VARCHAR},
           #{item.petHosCityCode,jdbcType=VARCHAR},
           #{item.petHosCountryCode,jdbcType=VARCHAR},
           #{item.petHosCode,jdbcType=VARCHAR},
           #{item.petHosName,jdbcType=VARCHAR},
           #{item.treatmentAmount,jdbcType=DECIMAL}
          )
        </foreach>
    </insert>

    <select id="getPetInjureExList" resultType="com.paic.ncbs.claim.model.dto.pet.PetInjureExDTO">
        SELECT ID_CLMS_PET_INJURE idClmsPetInjure,
               TREATMENT_SCHEME treatmentScheme,
               TREATMENT_OPERATION treatmentOperation,
               TREATMENT_START_DATE treatmentStartDate,
               TREATMENT_END_DATE treatmentEndDate,
               TREATMENT_DAYS treatmentDays,
               PET_HOS_PROVINCE_CODE petHosProvinceCode,
               PET_HOS_CITY_CODE petHosCityCode,
               PET_HOS_COUNTRY_CODE petHosCountryCode,
               PET_HOS_CODE petHosCode,
               PET_HOS_NAME petHosName,
               TREATMENT_AMOUNT treatmentAmount
        FROM CLMS_PET_INJURE_EX
        WHERE ID_CLMS_PET_INJURE = #{idClmsPetInjure,jdbcType=VARCHAR}
        AND IS_EFFECTIVE = 'Y'
    </select>

    <update id="delPetInjureExList">
        UPDATE CLMS_PET_INJURE_EX
        SET UPDATED_DATE = now(),
            UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE = 'N'
        WHERE ID_CLMS_PET_INJURE = #{idClmsPetInjure,jdbcType=VARCHAR}
    </update>

</mapper>