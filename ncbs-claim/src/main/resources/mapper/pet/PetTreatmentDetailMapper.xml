<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pet.PetTreatmentDetailMapper">

    <insert id="addPetTreatmentDetailList" parameterType="java.util.List">
        INSERT INTO CLMS_PET_TREATMENT_DETAIL (
          CREATED_BY ,
          CREATED_DATE ,
          UPDATED_BY ,
          UPDATED_DATE ,
          ID_CLMS_PET_TREATMENT_DETAIL,
          ID_CLMS_PET_INJURE ,
          TREATMENT_DATE ,
          TREATMENT_ITEM ,
          TREATMENT_DETAIL ,
          QUANTITY ,
          UNIT_PRICE ,
          TOTAL_PRICE ,
          CUSTOMER_AMOUNT ,
          PAY_AMOUNT ,
          PAY_DETAIL,
          LOSS_DESCRIBE,
          ESTIMATE_AMOUNT,
          REMARK)
        VALUES
        <foreach collection="treatmentDetailList" separator="," index="index" item="item">
        (
          #{item.createdBy,jdbcType=VARCHAR},
          now(),
          #{item.createdBy,jdbcType=VARCHAR},
          now(),
          #{item.idClmsPetTreatmentDetail, jdbcType=VARCHAR},
          #{item.idClmsPetInjure,jdbcType=VARCHAR},
          #{item.treatmentDate,jdbcType=TIMESTAMP},
          #{item.treatmentItem,jdbcType=VARCHAR},
          #{item.treatmentDetail,jdbcType=VARCHAR},
          #{item.quantity,jdbcType=DECIMAL},
          #{item.unitPrice,jdbcType=DECIMAL},
          #{item.totalPrice,jdbcType=DECIMAL},
          #{item.customerAmount,jdbcType=DECIMAL},
          #{item.payAmount,jdbcType=DECIMAL},
          #{item.payDetail,jdbcType=VARCHAR},
          #{item.lossDescribe,jdbcType=VARCHAR},
          #{item.estimateAmount,jdbcType=DECIMAL},
          #{item.remark,jdbcType=VARCHAR}
         )
        </foreach>
    </insert>

    <select id="getPetTreatmentDetailList" resultType="com.paic.ncbs.claim.model.dto.pet.PetTreatmentDetailDTO" parameterType="java.lang.String">
        SELECT ID_CLMS_PET_INJURE idClmsPetInjure,
               TREATMENT_DATE treatmentDate,
               TREATMENT_ITEM treatmentItem,
               TREATMENT_DETAIL treatmentDetail,
               QUANTITY quantity,
               UNIT_PRICE unitPrice,
               TOTAL_PRICE totalPrice,
               CUSTOMER_AMOUNT customerAmount,
               PAY_AMOUNT payAmount,
               PAY_DETAIL payDetail,
               LOSS_DESCRIBE lossDescribe,
               ESTIMATE_AMOUNT estimateAmount,
               REMARK remark
        FROM CLMS_PET_TREATMENT_DETAIL
        WHERE ID_CLMS_PET_INJURE = #{idClmsPetInjure,jdbcType=VARCHAR}
        AND IS_EFFECTIVE = 'Y'
    </select>

    <update id="delPetTreatmentDetailList" parameterType="java.lang.String">
        UPDATE CLMS_PET_TREATMENT_DETAIL
        SET UPDATED_DATE = now(),
            UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE = 'N'
        WHERE ID_CLMS_PET_INJURE = #{idClmsPetInjure,jdbcType=VARCHAR}
    </update>

</mapper>