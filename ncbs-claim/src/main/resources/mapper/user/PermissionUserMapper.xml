<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper">

	<insert id="addPermissionUser" parameterType="com.paic.ncbs.claim.model.dto.user.PermissionUserDTO">
		insert into clms_permission_user (
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_CLMS_PERMISSION_USER,
		COM_CODE,
		TYPE_CODE,
		GRADE,
		USER_ID,
		USER_NAME)
		values
		(#{createdBy,jdbcType=VARCHAR},
		now(),
		#{createdBy,jdbcType=VARCHAR},
		now(),
		#{idClmsPermissionUser,jdbcType=VARCHAR},
		#{comCode,jdbcType=VARCHAR},
		#{typeCode,jdbcType=VARCHAR},
		#{grade,jdbcType=INTEGER},
		#{userId,jdbcType=VARCHAR},
		#{userName,jdbcType=VARCHAR})
	</insert>

	<select id="getUserGrade" parameterType="java.lang.String" resultType="java.lang.Integer">
		select
		max(a.GRADE)
		from clms_permission_user a
		where a.USER_ID = #{userId,jdbcType=VARCHAR}
		and a.COM_CODE = #{comCode,jdbcType=VARCHAR}
		and a.TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
	</select>

	<select id="getUserList" resultType="java.lang.String">
		select distinct a.USER_ID
		from clms_permission_user a
		where a.TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
		and a.COM_CODE = #{comCode,jdbcType=VARCHAR}
		<if test="grade != null">
			and a.GRADE = #{grade,jdbcType=INTEGER}
		</if>
		order by a.GRADE
	</select>

	<select id="getPermissionUserList" parameterType="com.paic.ncbs.claim.model.vo.user.PermissionUserVO"
			resultType="com.paic.ncbs.claim.model.dto.user.PermissionUserDTO">
		select
		a.ID_CLMS_PERMISSION_USER idClmsPermissionUser,
		a.GRADE grade,
		a.USER_ID userId,
		a.USER_NAME userName,
		a.COM_CODE comCode,
		(select DEPARTMENT_ABBR_NAME  from department_define where DEPARTMENT_CODE = a.COM_CODE limit 1) deptName,
		A.TYPE_CODE typeCode,
		b.MAX_AMOUNT maxAmount
		from clms_permission_user a
		 left join clms_permission b on a.TYPE_CODE = b.TYPE_CODE and a.GRADE = b.GRADE
		where 1=1
		<if test="comCode != null and comCode != ''">
			and a.COM_CODE = #{comCode,jdbcType=VARCHAR}
		</if>
		<if test="typeCode != null and typeCode != ''">
			and a.TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
		</if>
		<if test="grade != null">
			and a.GRADE = #{grade,jdbcType=INTEGER}
		</if>
		<if test="userId != null and userId != ''">
			and a.USER_ID = #{userId,jdbcType=VARCHAR}
		</if>
		<if test="userName != null and userName != ''">
			and a.USER_NAME = #{userName,jdbcType=VARCHAR}
		</if>
		<if test="queryDeptCodeList != null ">
			and a.COM_CODE in
			<foreach collection="queryDeptCodeList" item="item" open="(" close=")" separator=",">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
		order by a.TYPE_CODE,a.COM_CODE,a.GRADE
	</select>

	<update id="updatePermissionUser" parameterType="com.paic.ncbs.claim.model.dto.user.PermissionUserDTO">
		update clms_permission_user
		set UPDATED_DATE = now(),
			UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
			GRADE = #{grade,jdbcType=INTEGER}
		where ID_CLMS_PERMISSION_USER = #{idClmsPermissionUser,jdbcType=VARCHAR}
	</update>

	<delete id="removePermissionUser" parameterType="java.lang.String">
		delete from clms_permission_user
		where ID_CLMS_PERMISSION_USER = #{idClmsPermissionUser,jdbcType=VARCHAR}
	</delete>

	<select id="getUserGradeCount"  resultType="java.lang.Integer">
		select count(1)
		from clms_permission_user a
		where a.USER_ID = #{userId,jdbcType=VARCHAR}
		and a.COM_CODE = #{comCode,jdbcType=VARCHAR}
		and a.TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
		limit 1

	</select>

	<select id="getGradeByComCode" resultType="java.lang.Integer">
		select GRADE  from clms_permission_user
		where TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
		and COM_CODE = #{comCode,jdbcType=VARCHAR}
		and GRADE <![CDATA[ >= ]]> #{grade,jdbcType=INTEGER}
		order by GRADE
		limit 1
	</select>

	<select id="getParentComCode" resultType="java.lang.String">
		select UPPER_DEPARTMENT_CODE
		from department_define
		where DEPARTMENT_CODE = #{comCode,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getSamePermissionUserCount" resultType="java.lang.Integer">
		select count(1)
		from clms_permission_user a
		where a.GRADE = #{grade,jdbcType=INTEGER}
		and a.ID_CLMS_PERMISSION_USER != #{idClmsPermissionUser,jdbcType=VARCHAR}
		and exists (select 1 from clms_permission_user b
		  where b.ID_CLMS_PERMISSION_USER = #{idClmsPermissionUser,jdbcType=VARCHAR}
		  and a.COM_CODE  = b.COM_CODE
		  and a.USER_ID   = b.USER_ID
		  and a.TYPE_CODE = b.TYPE_CODE)
	</select>

	<select id="getVerifyUserList" resultType="com.paic.ncbs.claim.model.dto.user.PermissionUserDTO">
		select distinct a.USER_ID as userId,a.USER_NAME as userName,a.COM_CODE as comCode
		from clms_permission_user a
		where
		a.USER_ID <![CDATA[ <> ]]> #{userId,jdbcType=VARCHAR}
		and a.COM_CODE in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item,jdbcType=VARCHAR}
		</foreach>
		and a.TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
		order by a.GRADE
	</select>

	<select id="getComCodeByUserId" resultType="java.lang.String">
		select
			a.COM_CODE
		from clms_permission_user a
		where a.USER_ID = #{userId,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getUserPermission" resultType="com.paic.ncbs.claim.model.vo.user.PermissionUserVO"
			parameterType="com.paic.ncbs.claim.model.dto.user.PermissionUserDTO">
		select
			a.USER_ID,
			a.COM_CODE,
			a.TYPE_CODE,
			ifnull(b.MAX_AMOUNT,0) maxAmount
		from clms_permission_user a
		left join clms_permission b on a.TYPE_CODE = b.TYPE_CODE and a.GRADE = b.GRADE
		where 1=1
		<if test="comCode != null and comCode != ''">
			and a.COM_CODE = #{comCode,jdbcType=VARCHAR}
		</if>
		<if test="typeCode != null and typeCode != ''">
			and a.TYPE_CODE = #{typeCode,jdbcType=VARCHAR}
		</if>
		<if test="userId != null and userId != ''">
			and a.USER_ID = #{userId,jdbcType=VARCHAR}
		</if>
	</select>

</mapper>