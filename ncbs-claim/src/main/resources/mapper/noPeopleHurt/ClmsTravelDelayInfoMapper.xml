<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsTravelDelayInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsTravelDelayInfo" id="ClmsTravelDelayInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="delayType" column="delay_type" jdbcType="VARCHAR"/>
        <result property="otherTypeDescription" column="other_type_description" jdbcType="VARCHAR"/>
        <result property="delayStartDate" column="delay_start_date" jdbcType="TIMESTAMP"/>
        <result property="delayEndDate" column="delay_end_date" jdbcType="TIMESTAMP"/>
        <result property="delayDays" column="delay_days" jdbcType="INTEGER"/>
        <result property="airCompany" column="air_company" jdbcType="VARCHAR"/>
        <result property="flightNo" column="flight_no" jdbcType="VARCHAR"/>
        <result property="departPlace" column="depart_place" jdbcType="VARCHAR"/>
        <result property="arrivalPlace" column="arrival_place" jdbcType="VARCHAR"/>
        <result property="expectTakeOffTime" column="expect_take_off_time" jdbcType="TIMESTAMP"/>
        <result property="expectArrivalTime" column="expect_arrival_time" jdbcType="TIMESTAMP"/>
        <result property="realArrivalTime" column="real_arrival_time" jdbcType="TIMESTAMP"/>
        <result property="amount" column="amount" jdbcType="NUMERIC"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsTravelDelayInfoMap">
        select id,
               report_no,
               case_times,
               delay_type,
               other_type_description,
               delay_start_date,
               delay_end_date,
               delay_days,
               air_company,
               flight_no,
               depart_place,
               arrival_place,
               expect_take_off_time,
               expect_arrival_time,
               real_arrival_time,
               amount,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_travel_delay_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsTravelDelayInfoMap">
        select id,
               report_no,
               case_times,
               delay_type,
               other_type_description,
               delay_start_date,
               delay_end_date,
               delay_days,
               air_company,
               flight_no,
               depart_place,
               arrival_place,
               expect_take_off_time,
               expect_arrival_time,
               real_arrival_time,
               amount,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_travel_delay_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_travel_delay_info(id ,report_no, case_times, delay_type,other_type_description, delay_start_date, delay_end_date,
                                           delay_days, air_company, flight_no, depart_place, arrival_place,
                                           expect_take_off_time, expect_arrival_time, real_arrival_time, amount,
                                           created_by, created_date, updated_by, updated_date)
        values (#{id},#{reportNo}, #{caseTimes}, #{delayType},#{otherTypeDescription}, #{delayStartDate}, #{delayEndDate}, #{delayDays},
                #{airCompany}, #{flightNo}, #{departPlace}, #{arrivalPlace}, #{expectTakeOffTime}, #{expectArrivalTime},
                #{realArrivalTime}, #{amount}, #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_travel_delay_info(id ,report_no, case_times, delay_type,other_type_description, delay_start_date, delay_end_date,
        delay_days, air_company, flight_no, depart_place, arrival_place, expect_take_off_time, expect_arrival_time,
        real_arrival_time, amount, created_by, created_date, updated_by, updated_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.delayType},#{entity.otherTypeDescription}, #{entity.delayStartDate},
            #{entity.delayEndDate}, #{entity.delayDays}, #{entity.airCompany}, #{entity.flightNo},
            #{entity.departPlace}, #{entity.arrivalPlace}, #{entity.expectTakeOffTime}, #{entity.expectArrivalTime},
            #{entity.realArrivalTime}, #{entity.amount}, #{entity.createdBy}, #{entity.createdDate},
            #{entity.updatedBy}, #{entity.updatedDate})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_travel_delay_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="delayType != null and delayType != ''">
                delay_type = #{delayType},
            </if>
            <if test="otherTypeDescription != null and otherTypeDescription != ''">
                other_type_description = #{otherTypeDescription},
            </if>
            <if test="delayStartDate != null">
                delay_start_date = #{delayStartDate},
            </if>
            <if test="delayEndDate != null">
                delay_end_date = #{delayEndDate},
            </if>
            <if test="delayDays != null">
                delay_days = #{delayDays},
            </if>
            <if test="airCompany != null and airCompany != ''">
                air_company = #{airCompany},
            </if>
            <if test="flightNo != null and flightNo != ''">
                flight_no = #{flightNo},
            </if>
            <if test="departPlace != null and departPlace != ''">
                depart_place = #{departPlace},
            </if>
            <if test="arrivalPlace != null and arrivalPlace != ''">
                arrival_place = #{arrivalPlace},
            </if>
            <if test="expectTakeOffTime != null">
                expect_take_off_time = #{expectTakeOffTime},
            </if>
            <if test="expectArrivalTime != null">
                expect_arrival_time = #{expectArrivalTime},
            </if>
            <if test="realArrivalTime != null">
                real_arrival_time = #{realArrivalTime},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_travel_delay_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_travel_delay_info
            (id,
             report_no,
             case_times,
             delay_type,
             other_type_description,
             delay_start_date,
             delay_end_date,
             delay_days,
             air_company,
             flight_no,
             depart_place,
             arrival_place,
             expect_take_off_time,
             expect_arrival_time,
             real_arrival_time,
             amount,
             created_by,
             created_date,
             updated_by,
             updated_date)
        select
            replace(UUID(), '-', ''),
            report_no,
            #{reopenCaseTimes},
            delay_type,
            other_type_description,
            delay_start_date,
            delay_end_date,
            delay_days,
            air_company,
            flight_no,
            depart_place,
            arrival_place,
            expect_take_off_time,
            expect_arrival_time,
            real_arrival_time,
            amount,
            #{userId},
            NOW(),
            #{userId},
            NOW()
        from
            clms_travel_delay_info
        where
          REPORT_NO = #{reportNo}
          AND CASE_TIMES=#{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_travel_delay_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(amount), 0)
        FROM clms_travel_delay_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

