<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper">
	<!-- 定义EstimateDutyRecord 的复杂关联map -->
	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO" id="estimateDutyRecordMap">
		<id property="idAhcsEstimateDutyRecord" column="ID_AHCS_ESTIMATE_DUTY_RECORD" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="taskId" 	column="TASK_ID" />
		<result property="policyNo" column="POLICY_NO" />
		<result property="caseNo" column="CASE_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="planCode" column="PLAN_CODE" />
		<result property="dutyCode" column="DUTY_CODE" />
		<result property="dutyName" column="DUTY_NAME" />
		<result property="orgDutyCode" column="ORG_DUTY_CODE"/>
		<result property="orgDutyName" column="ORG_DUTY_NAME"/>
		<result property="baseAmountPay" column="BASE_AMOUNT_PAY" />
		<result property="estimateAmount" column="ESTIMATE_AMOUNT" />
		<result property="arbitrageFee" column="ARBITRAGE_FEE" />
		<result property="lawsuitFee" column="LAWSUIT_FEE" />
		<result property="commonEstimateFee" column="COMMON_ESTIMATE_FEE" />
		<result property="lawyerFee" column="LAWYER_FEE" />
		<result property="executeFee" column="EXECUTE_FEE" />
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result property="estimateType" column="ESTIMATE_TYPE" />
		<result property="isShareAmount" column="IS_SHARE_AMOUNT"/>
		<result property="shareDutyGroup" column="SHARE_DUTY_GROUP"/>
		<result property="lastPayAmount" column="ESTIMATE_AMOUNT"/>
		<result property="idPlyRiskProperty" column="ID_PLY_RISK_PROPERTY"/>
		<result column="risk_group_no" property="riskGroupNo"/>
		<result column="risk_group_name" property="riskGroupName"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO" id="estimateDutyRecordForRestartMap">
		<id property="idAhcsEstimateDutyRecord" column="ID_AHCS_ESTIMATE_DUTY_RECORD" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="taskId" 	column="TASK_ID" />
		<result property="policyNo" column="POLICY_NO" />
		<result property="caseNo" column="CASE_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="planCode" column="PLAN_CODE" />
		<result property="dutyCode" column="DUTY_CODE" />
		<result property="dutyName" column="DUTY_NAME" />
		<result property="orgDutyCode" column="ORG_DUTY_CODE"/>
		<result property="orgDutyName" column="ORG_DUTY_NAME"/>
		<result property="baseAmountPay" column="BASE_AMOUNT_PAY" />
		<result property="estimateAmount" column="ESTIMATE_AMOUNT" />
		<result property="arbitrageFee" column="ARBITRAGE_FEE" />
		<result property="lawsuitFee" column="LAWSUIT_FEE" />
		<result property="commonEstimateFee" column="COMMON_ESTIMATE_FEE" />
		<result property="lawyerFee" column="LAWYER_FEE" />
		<result property="executeFee" column="EXECUTE_FEE" />
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result property="estimateType" column="ESTIMATE_TYPE" />
		<result property="isShareAmount" column="IS_SHARE_AMOUNT"/>
		<result property="shareDutyGroup" column="SHARE_DUTY_GROUP"/>
		<result property="lastPayAmount" column="LAST_PAY_AMOUNT"/>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO" id="recordDTO">
		<id property="idAhcsEstimateDutyRecord" column="ID_AHCS_ESTIMATE_DUTY_RECORD" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="taskId" 	column="TASK_ID" />
		<result property="policyNo" column="POLICY_NO" />
		<result property="caseNo" column="CASE_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="planCode" column="PLAN_CODE" />
		<result property="dutyCode" column="DUTY_CODE" />
		<result property="dutyName" column="DUTY_NAME" />
		<result property="orgDutyCode" column="ORG_DUTY_CODE"/>
		<result property="orgDutyName" column="ORG_DUTY_NAME"/>
		<result property="baseAmountPay" column="BASE_AMOUNT_PAY" />
		<result property="estimateAmount" column="ESTIMATE_AMOUNT" />
		<result property="arbitrageFee" column="ARBITRAGE_FEE" />
		<result property="lawsuitFee" column="LAWSUIT_FEE" />
		<result property="commonEstimateFee" column="COMMON_ESTIMATE_FEE" />
		<result property="lawyerFee" column="LAWYER_FEE" />
		<result property="executeFee" column="EXECUTE_FEE" />
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result property="estimateType" column="ESTIMATE_TYPE" />
		<result property="ssCoinsRate" column="ss_coins_rate" />
	</resultMap>
		
    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO" id="resultPlan">
		<result property="planCode" column="PLAN_CODE" />
		<result property="orgPlanCode" column="ORG_PLAN_CODE" />
		<result property="dutyCode" column="DUTY_CODE" />
		<result property="estimateAmount" column="ESTIMATE_AMOUNT" />
	</resultMap>
	
	<!--  插入    -->
	<insert id="addEstimateDutyRecordList" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO" >
				INSERT INTO CLMS_ESTIMATE_DUTY_RECORD (
					ID_AHCS_ESTIMATE_DUTY_RECORD,
					CREATED_BY,
					CREATED_DATE,
					UPDATED_BY,
					UPDATED_DATE,
					TASK_ID,
					POLICY_NO,
					CASE_NO,
					CASE_TIMES,
					PLAN_CODE,
					DUTY_CODE,
					DUTY_NAME,
					BASE_AMOUNT_PAY,
					ESTIMATE_AMOUNT,
					ARBITRAGE_FEE,
					LAWSUIT_FEE,
					COMMON_ESTIMATE_FEE,
					LAWYER_FEE,
					EXECUTE_FEE,
					VERIFY_APPRAISE_FEE,INQUIRE_FEE,OTHER_FEE,SPECIAL_SURVEY_FEE,
					ESTIMATE_TYPE,
					ARCHIVE_TIME,
					ID_PLY_RISK_PROPERTY,
					chg_pay_value,
					risk_group_no,
					risk_group_name,
					ss_coins_pay_value,
					ss_coins_rate,
					ss_coins_chg_pay_value
					)
		<foreach collection="paramList" index="index" item="item" open="(" separator="union all" close=")">
					select
					md5(uuid()),
					#{item.createdBy,jdbcType=VARCHAR},
					now(),
					#{item.updatedBy,jdbcType=VARCHAR},
					now(),
					#{item.taskId,jdbcType=VARCHAR},
					#{item.policyNo,jdbcType=VARCHAR},
					#{item.caseNo,jdbcType=VARCHAR},
					#{item.caseTimes,jdbcType=NUMERIC},
					#{item.planCode,jdbcType=VARCHAR},
					#{item.dutyCode,jdbcType=VARCHAR},
					#{item.dutyName,jdbcType=VARCHAR},
					#{item.baseAmountPay,jdbcType=NUMERIC},
					#{item.estimateAmount,jdbcType=NUMERIC},
					#{item.arbitrageFee,jdbcType=NUMERIC},
					#{item.lawsuitFee,jdbcType=NUMERIC},
					#{item.commonEstimateFee,jdbcType=NUMERIC},
					#{item.lawyerFee,jdbcType=NUMERIC},
					#{item.executeFee,jdbcType=NUMERIC},
					#{item.verifyAppraiseFee,jdbcType=NUMERIC},
					#{item.inquireFee,jdbcType=NUMERIC},
					#{item.otherFee,jdbcType=NUMERIC},
					#{item.specialSurveyFee,jdbcType=NUMERIC},
					#{item.estimateType,jdbcType=VARCHAR},
			        ifnull(#{item.archiveTime},sysdate()),
					#{item.idPlyRiskProperty,jdbcType=VARCHAR},
					#{item.chgPayValue},
					#{item.riskGroupNo},
					#{item.riskGroupName},
			#{item.ssCoinsPayValue},
			#{item.ssCoinsRate},
			#{item.ssCoinsChgPayValue}
					FROM DUAL
		</foreach>
	</insert>

	<insert id="addDutyRecordList" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO" >
			INSERT INTO CLMS_ESTIMATE_DUTY_RECORD (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_ESTIMATE_DUTY_RECORD,
			TASK_ID,
			POLICY_NO,
			CASE_NO,
			CASE_TIMES,
			PLAN_CODE,
			DUTY_CODE,
			DUTY_NAME,
			BASE_AMOUNT_PAY,
			ESTIMATE_AMOUNT,
			ARBITRAGE_FEE,
			LAWSUIT_FEE,
			COMMON_ESTIMATE_FEE,
			LAWYER_FEE,
			EXECUTE_FEE,
		    VERIFY_APPRAISE_FEE,INQUIRE_FEE,OTHER_FEE,SPECIAL_SURVEY_FEE,
		    ESTIMATE_TYPE,
			ARCHIVE_TIME,
			ID_PLY_RISK_PROPERTY,
			chg_pay_value,
			risk_group_no,
			risk_group_name,
			ss_coins_pay_value,
			ss_coins_rate,
			ss_coins_chg_pay_value
			)VALUES
		<foreach collection="paramList" separator="," index="i" item="item">
			(#{item.createdBy,jdbcType=VARCHAR},
			sysdate(),
			#{item.updatedBy,jdbcType=VARCHAR},
			sysdate(),
			#{item.idAhcsEstimateDutyRecord, jdbcType=VARCHAR},
			#{item.taskId,jdbcType=VARCHAR},
			#{item.policyNo,jdbcType=VARCHAR},
			#{item.caseNo,jdbcType=VARCHAR},
			#{item.caseTimes,jdbcType=NUMERIC},
			#{item.planCode,jdbcType=VARCHAR},
			#{item.dutyCode,jdbcType=VARCHAR},
			#{item.dutyName,jdbcType=VARCHAR},
			#{item.baseAmountPay,jdbcType=NUMERIC},
			#{item.estimateAmount,jdbcType=NUMERIC},
			#{item.arbitrageFee,jdbcType=NUMERIC},
			#{item.lawsuitFee,jdbcType=NUMERIC},
			#{item.commonEstimateFee,jdbcType=NUMERIC},
			#{item.lawyerFee,jdbcType=NUMERIC},
			#{item.executeFee,jdbcType=NUMERIC},
			#{item.verifyAppraiseFee,jdbcType=NUMERIC},
			#{item.inquireFee,jdbcType=NUMERIC},
			#{item.otherFee,jdbcType=NUMERIC},
			#{item.specialSurveyFee,jdbcType=NUMERIC},
			#{item.estimateType,jdbcType=VARCHAR},
			sysdate(),
			#{item.idPlyRiskProperty,jdbcType=VARCHAR},
			#{item.chgPayValue},
			#{item.riskGroupNo},
			#{item.riskGroupName},
			#{item.ssCoinsPayValue},
			#{item.ssCoinsRate},
			#{item.ssCoinsChgPayValue})
		</foreach>
	</insert>
	
	<!-- 获取预估责任金额表 -->   
     <select id="getEstimateDutyRecordList" parameterType="java.util.Map" resultMap="estimateDutyRecordMap">
          select
         		d.CREATED_BY,
				d.UPDATED_BY,
				d.ID_AHCS_ESTIMATE_DUTY_RECORD,
				d.TASK_ID,
				d.POLICY_NO,
				d.CASE_NO,
				d.CASE_TIMES,
				d.PLAN_CODE,
				d.DUTY_CODE,
				d.DUTY_NAME,
				d.BASE_AMOUNT_PAY,
				d.ESTIMATE_AMOUNT,
				d.ARBITRAGE_FEE,
				d.LAWSUIT_FEE,
				d.COMMON_ESTIMATE_FEE,
				d.LAWYER_FEE,
				d.EXECUTE_FEE,
				d.VERIFY_APPRAISE_FEE,d.INQUIRE_FEE,d.OTHER_FEE,d.SPECIAL_SURVEY_FEE,
				d.ESTIMATE_TYPE,
				d.id_ply_risk_property,
		 		d.risk_group_no,
		 		d.risk_group_name,
				if((select pd.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
				where pi.CASE_NO = #{caseNo,jdbcType = VARCHAR} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
				and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=d.duty_code limit 1) = '1',TRUE,FALSE) IS_SHARE_AMOUNT,
				(select pd.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
				 where pi.CASE_NO = #{caseNo,jdbcType = VARCHAR} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
				   and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=d.duty_code limit 1) SHARE_DUTY_GROUP
          from CLMS_ESTIMATE_DUTY_RECORD d
          where d.case_times = #{caseTimes,jdbcType = INTEGER}
       		and d.case_no = #{caseNo,jdbcType = VARCHAR}
      		and d.plan_code = #{planCode,jdbcType = VARCHAR}
      		and d.IS_EFFECTIVE = 'Y'
            order by d.DUTY_CODE
    </select>





	<select id="getDutyRecordListByApplyId" resultMap="estimateDutyRecordMap">
		select
			d.CREATED_BY,
			d.UPDATED_BY,
			d.ID_AHCS_ESTIMATE_DUTY_RECORD,
			d.TASK_ID,
			d.POLICY_NO,
			d.CASE_NO,
			d.CASE_TIMES,
			d.PLAN_CODE,
			d.DUTY_CODE,
			d.DUTY_NAME,
			(select t2.org_duty_code from CLMS_policy_info t, CLMS_policy_plan t1, CLMS_policy_duty t2
			where t.id_ahcs_policy_info=t1.id_ahcs_policy_info and t1.id_ahcs_policy_plan=t2.id_ahcs_policy_plan
			and t.case_no=d.CASE_NO and t2.duty_code=d.DUTY_CODE and t1.PLAN_CODE=d.PLAN_CODE) as ORG_DUTY_CODE,
			(select t2.org_duty_name from CLMS_policy_info t, CLMS_policy_plan t1, CLMS_policy_duty t2
			where t.id_ahcs_policy_info=t1.id_ahcs_policy_info and t1.id_ahcs_policy_plan=t2.id_ahcs_policy_plan
			and t.case_no=d.CASE_NO and t2.duty_code=d.DUTY_CODE and t1.PLAN_CODE=d.PLAN_CODE) as ORG_DUTY_NAME,
			d.BASE_AMOUNT_PAY,
			d.ESTIMATE_AMOUNT,
			d.ARBITRAGE_FEE,
			d.LAWSUIT_FEE,
			d.COMMON_ESTIMATE_FEE,
			d.LAWYER_FEE,
			d.EXECUTE_FEE,
			d.VERIFY_APPRAISE_FEE,d.INQUIRE_FEE,d.OTHER_FEE,d.SPECIAL_SURVEY_FEE,
			d.ESTIMATE_TYPE
		from CLMS_ESTIMATE_DUTY_RECORD d, CLMS_REGISTER_AMOUNT_REL t1
		where t1.ID_REGISTER_CASE_APPLY=#{applyId, jdbcType = VARCHAR}
		  and t1.ID_ESTIMATE_DUTY_RECORD=d.ID_AHCS_ESTIMATE_DUTY_RECORD
	</select>
    <!-- 获取预估责任金额表  废弃代码，未被引用
     <select id="getDutyRecordListByTaskId" parameterType="java.lang.String" resultMap="estimateDutyRecordMap">
          select
         		d.CREATED_BY,
				d.UPDATED_BY,
				d.ID_AHCS_ESTIMATE_DUTY_RECORD,
				d.TASK_ID,
				d.POLICY_NO,
				d.CASE_NO,
				d.CASE_TIMES,
				d.PLAN_CODE,
				d.DUTY_CODE,
				d.DUTY_NAME,
				d.BASE_AMOUNT_PAY,
				d.ESTIMATE_AMOUNT,
				d.ARBITRAGE_FEE,
				d.LAWSUIT_FEE,
				d.COMMON_ESTIMATE_FEE,
				d.LAWYER_FEE,
				d.EXECUTE_FEE,
				d.VERIFY_FEE,
				d.AWARD_FEE,
				d.ESTIMATE_TYPE        
          from CLMS_ESTIMATE_DUTY_RECORD d
          where d.TASK_ID = #{taskId,jdbcType = VARCHAR} and d.IS_EFFECTIVE = 'Y'
    </select>   -->
		
	 <!-- 更新预估责任record金额表相关费用金额 -->
     <update id="modifyBatchEstimateDutyRecord">
	     	update CLMS_ESTIMATE_DUTY_RECORD d
			   set d.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
			   	   d.ESTIMATE_AMOUNT = #{estimateAmount,jdbcType=NUMERIC},
				   d.ARBITRAGE_FEE = #{arbitrageFee,jdbcType=NUMERIC},
				   d.LAWSUIT_FEE = #{lawsuitFee,jdbcType=NUMERIC},
			       d.COMMON_ESTIMATE_FEE = #{commonEstimateFee,jdbcType=NUMERIC},
				   d.LAWYER_FEE = #{lawyerFee,jdbcType=NUMERIC},
			       d.EXECUTE_FEE = #{executeFee,jdbcType=NUMERIC},
			       d.VERIFY_APPRAISE_FEE = #{verifyAppraiseFee,jdbcType=NUMERIC},
			       d.INQUIRE_FEE = #{inquireFee,jdbcType=NUMERIC},
				   d.OTHER_FEE = #{otherFee,jdbcType=NUMERIC},
				   d.SPECIAL_SURVEY_FEE = #{specialSurveyFee,jdbcType=NUMERIC},
		           d.UPDATED_DATE = #{updatedDate, jdbcType=TIMESTAMP},
		           d.ESTIMATE_TYPE = #{estimateType,jdbcType = VARCHAR}
		      where d.CASE_TIMES = #{caseTimes,jdbcType = INTEGER}
          	    and d.CASE_NO = #{caseNo, jdbcType = VARCHAR}
          		and d.PLAN_CODE = #{planCode,jdbcType = VARCHAR}
          		and d.DUTY_CODE = #{dutyCode,jdbcType = VARCHAR}
          		and d.ESTIMATE_TYPE = #{estimateType,jdbcType = VARCHAR}
			  	and d.IS_EFFECTIVE = 'Y'
	     	    <if test="idPlyRiskProperty != null">
					and id_ply_risk_property = #{idPlyRiskProperty,jdbcType = VARCHAR}
				</if>
     </update>
     
     <select id="getDutyRecordDTO" resultMap="recordDTO">
     	select  d.CREATED_BY,
				d.CREATED_DATE,
				d.UPDATED_BY,
				d.UPDATED_DATE,
				d.ID_AHCS_ESTIMATE_DUTY_RECORD,
				d.TASK_ID,
				d.POLICY_NO,
				d.CASE_NO,
				d.CASE_TIMES,
				d.PLAN_CODE,
				d.DUTY_CODE,
				d.DUTY_NAME,
				(select t2.org_duty_code from CLMS_policy_info t, CLMS_policy_plan t1, CLMS_policy_duty t2
					where t.id_ahcs_policy_info=t1.id_ahcs_policy_info and t1.id_ahcs_policy_plan=t2.id_ahcs_policy_plan
					and t.case_no=d.CASE_NO and t2.duty_code=d.DUTY_CODE and t1.PLAN_CODE=d.PLAN_CODE limit 1) as ORG_DUTY_CODE,
				(select t2.org_duty_name from CLMS_policy_info t, CLMS_policy_plan t1, CLMS_policy_duty t2
					where t.id_ahcs_policy_info=t1.id_ahcs_policy_info and t1.id_ahcs_policy_plan=t2.id_ahcs_policy_plan
					and t.case_no=d.CASE_NO and t2.duty_code=d.DUTY_CODE and t1.PLAN_CODE=d.PLAN_CODE limit 1) as ORG_DUTY_NAME,
				d.BASE_AMOUNT_PAY,
				d.ESTIMATE_AMOUNT,
				d.ARBITRAGE_FEE,
				d.LAWSUIT_FEE,
				d.COMMON_ESTIMATE_FEE,
				d.LAWYER_FEE,
				d.EXECUTE_FEE,
				d.VERIFY_APPRAISE_FEE,d.INQUIRE_FEE,d.OTHER_FEE,d.SPECIAL_SURVEY_FEE,

				d.ESTIMATE_TYPE        
          from CLMS_ESTIMATE_DUTY_RECORD d
          where d.POLICY_NO = #{policyNo,jdbcType = VARCHAR}
          	and d.CASE_TIMES = #{caseTimes,jdbcType = INTEGER}
          	and d.CASE_NO = #{caseNo, jdbcType=VARCHAR}
          	and d.PLAN_CODE = #{planCode,jdbcType = VARCHAR}
          	and d.DUTY_CODE = #{dutyCode,jdbcType = VARCHAR}
          	and d.ESTIMATE_TYPE ='01'
          	and d.IS_EFFECTIVE = 'Y'
     </select>

    <!-- 获取暂存表中立案的数据 -->
    <select id="getRecordsOfRegistCase" resultMap="recordDTO">
    	select  d.CREATED_BY,
				d.CREATED_DATE,
				d.UPDATED_BY,
				d.UPDATED_DATE,
				d.ID_AHCS_ESTIMATE_DUTY_RECORD,
				d.TASK_ID,
				d.POLICY_NO,
				d.CASE_NO,
				d.CASE_TIMES,
				d.PLAN_CODE,
				d.DUTY_CODE,
				d.DUTY_NAME,
				d.BASE_AMOUNT_PAY,
				d.ESTIMATE_AMOUNT,
				d.ARBITRAGE_FEE,
				d.LAWSUIT_FEE,
				d.COMMON_ESTIMATE_FEE,
				d.LAWYER_FEE,
				d.EXECUTE_FEE,
				d.VERIFY_APPRAISE_FEE,d.INQUIRE_FEE,d.OTHER_FEE,d.SPECIAL_SURVEY_FEE,
				d.ESTIMATE_TYPE
		   from CLMS_estimate_duty_record d
		  where d.CASE_NO=#{caseNo, jdbcType=VARCHAR}
		    and d.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
		    and d.ESTIMATE_TYPE=#{estimateType, jdbcType=VARCHAR}
		    and d.IS_EFFECTIVE = 'Y'
    </select>
    
    <!-- 获取类型（立案/预估）金额大于0的保单、险种、责任 -->
    <select id="getEstimateEutyList" resultMap="recordDTO">
    	 select d.ID_AHCS_ESTIMATE_DUTY_RECORD,
				d.POLICY_NO,
				d.CASE_NO,
				d.CASE_TIMES,
				d.PLAN_CODE,
				d.DUTY_CODE,
				d.ESTIMATE_AMOUNT,
				d.ESTIMATE_TYPE  
		   from CLMS_estimate_duty_record d
		  where d.CASE_NO = #{caseNo, jdbcType=VARCHAR}
		    and d.CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
		    and d.ESTIMATE_TYPE = #{estimateType, jdbcType=VARCHAR}
		    and d.ESTIMATE_AMOUNT is not null
		    and d.ESTIMATE_AMOUNT > 0
		    and d.IS_EFFECTIVE = 'Y'
    </select>
    
    <!-- 根据报案号、赔付次数、机构获取立案金额大于等于0的保单、险种 -->
    <select id="getEstimatePolicyPlanList" resultMap="resultPlan">
    	select d.DUTY_CODE,
		       d.PLAN_CODE, 
		       ifnull(d.ESTIMATE_AMOUNT,0) ESTIMATE_AMOUNT,
		       (select pp.ORG_PLAN_CODE
		          from CLMS_policy_plan pp
		         where pp.ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo}
		           and pp.PLAN_CODE = d.PLAN_CODE
		           limit 1) as ORG_PLAN_CODE
		  from CLMS_estimate_duty_record d
		 where d.CASE_NO = #{caseNo} 
			   and d.CASE_TIMES = #{caseTimes} 
			   and d.ESTIMATE_TYPE = '02' 
			   and d.ESTIMATE_AMOUNT is not null
			   and d.IS_EFFECTIVE = 'Y'
	 <![CDATA[ and d.ESTIMATE_AMOUNT >= 0  ]]>
    </select>
    
    <delete id="deleteEstimateRecordList"  parameterType="java.util.List">
    	 <foreach collection="caseNoList" item="item" open="begin" separator=";" close=";end;">
	    	delete from CLMS_ESTIMATE_DUTY_RECORD d
	    	where d.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
          	  and d.CASE_NO = #{item, jdbcType=VARCHAR}
	    </foreach>   
    </delete>

	<update id="updateEffectiveByCaseNos" parameterType="java.util.List">
		UPDATE
			CLMS_ESTIMATE_DUTY_RECORD
		SET
			UPDATED_BY = #{updatedBy},
			UPDATED_DATE = sysdate(),
			IS_EFFECTIVE = 'N'
		WHERE
			IS_EFFECTIVE = 'Y'
			AND CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
			AND CASE_NO IN
			<foreach collection="caseNoList" index="index" item="item" open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
	</update>

	<update id="updateEffectiveByIds" parameterType="java.util.List">
		UPDATE
			CLMS_ESTIMATE_DUTY_RECORD
		SET
			UPDATED_BY = #{updatedBy},
			UPDATED_DATE = sysdate(),
		IS_EFFECTIVE = 'N'
		WHERE ID_AHCS_ESTIMATE_DUTY_RECORD in
		<foreach collection="paramList" index="index" item="item" open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
		</foreach>
	</update>

	<!--删除立案数据 紧小微案件-->
	<delete id="delEstimateRecordDataByCaseNo">
		DELETE FROM CLMS_ESTIMATE_DUTY_RECORD T
		WHERE T.CASE_NO=#{caseNo, jdbcType=VARCHAR}
		AND T.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
	</delete>
    
    <!-- 获取立案的未决类型 -->
    <select id="getEstimateTypeByReportNo" resultType="string">
        select edr.ESTIMATE_TYPE 
          from CLMS_ESTIMATE_DUTY_RECORD edr
         where edr.CASE_NO in (select pi.CASE_NO from CLMS_POLICY_INFO pi where pi.REPORT_NO = #{reportNo})
               and edr.CASE_TIMES = #{caseTimes}
               and edr.ESTIMATE_TYPE in ('02','03')
               and edr.IS_EFFECTIVE = 'Y'
    </select>
    
    <!-- 获取立案金额不等于0的赔案 -->
    <select id="getRegisterAmountCaseNo" resultType="string">
        select distinct edr.CASE_NO 
          from CLMS_ESTIMATE_DUTY_RECORD edr
         where edr.CASE_NO in (select pi.CASE_NO from CLMS_POLICY_INFO pi where pi.REPORT_NO = #{reportNo})
               and edr.CASE_TIMES = #{caseTimes}
               and edr.ESTIMATE_TYPE = #{estimateType} 
               and edr.ESTIMATE_AMOUNT is not null
               and edr.IS_EFFECTIVE = 'Y'
     <![CDATA[ and edr.ESTIMATE_AMOUNT != 0  ]]>
    </select>

	<select id="getDutyRecordInfoByApplyId" resultMap="recordDTO">
		select  t1.ID_AHCS_ESTIMATE_DUTY_RECORD,
				t1.TASK_ID,
				t1.POLICY_NO,
				t1.CASE_NO,
				t1.CASE_TIMES,
				t1.PLAN_CODE,
				t1.DUTY_CODE,
				t1.DUTY_NAME,
				t1.BASE_AMOUNT_PAY,
				t1.ESTIMATE_AMOUNT,
				t1.ARBITRAGE_FEE,
				t1.LAWSUIT_FEE,
				t1.COMMON_ESTIMATE_FEE,
				t1.LAWYER_FEE,
				t1.EXECUTE_FEE,
				t1.VERIFY_APPRAISE_FEE,t1.INQUIRE_FEE,t1.OTHER_FEE,t1.SPECIAL_SURVEY_FEE,
				t1.ESTIMATE_TYPE
		from CLMS_ESTIMATE_DUTY_RECORD t1, CLMS_REGISTER_AMOUNT_REL t2
		where t2.ID_REGISTER_CASE_APPLY=#{applyId, jdbcType=VARCHAR}
		  and t1.ID_AHCS_ESTIMATE_DUTY_RECORD=t2.ID_ESTIMATE_DUTY_RECORD
	</select>

	<select id="getRecordsOfRegisterCaseByReportNo" resultMap="recordDTO">
		select  d.CREATED_BY,
		d.CREATED_DATE,
		d.UPDATED_BY,
		d.UPDATED_DATE,
		d.ID_AHCS_ESTIMATE_DUTY_RECORD,
		d.TASK_ID,
		d.POLICY_NO,
		d.CASE_NO,
		d.CASE_TIMES,
		d.PLAN_CODE,
		d.DUTY_CODE,
		d.DUTY_NAME,
		d.BASE_AMOUNT_PAY,
		d.ESTIMATE_AMOUNT,
		d.ARBITRAGE_FEE,
		d.LAWSUIT_FEE,
		d.COMMON_ESTIMATE_FEE,
		d.LAWYER_FEE,
		d.EXECUTE_FEE,
		d.VERIFY_APPRAISE_FEE,
		d.INQUIRE_FEE,
		d.OTHER_FEE,
		d.SPECIAL_SURVEY_FEE,
		d.ESTIMATE_TYPE,
		d.archive_time,
		d.ss_coins_rate
		from CLMS_estimate_duty_record d,
		     CLMS_estimate_policy t
		where t.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
		and d.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
		and t.CASE_TIMES=d.CASE_TIMES
		and t.CASE_NO=d.CASE_NO
		and d.ESTIMATE_TYPE=#{estimateType, jdbcType=VARCHAR}
		and d.IS_EFFECTIVE = 'Y'
		order by d.CREATED_DATE desc
	</select>

	<select id="getTotalRegistAmount" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO">
		SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
			ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
			ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
			ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0)+ifnull(sum(d.OTHER_FEE), 0)+ifnull(sum(d.SPECIAL_SURVEY_FEE), 0) estimateAmount  ,
		concat(PLAN_CODE,DUTY_CODE) dutyCode
		from   CLMS_ESTIMATE_DUTY_RECORD d
		where d.case_times = 1
		and `POLICY_NO` = #{policyNo, jdbcType=VARCHAR}
		and d.IS_EFFECTIVE = 'Y'

		  <if test=" caseNos != null and caseNos.size > 0 ">
			  and `CASE_NO`  in
			  <foreach collection="caseNos" item="item" separator="," open="(" close=")">
				  #{item}
			  </foreach>
		  </if>
		and d.`ESTIMATE_TYPE`  = '02'  group  by PLAN_CODE,duty_Code
	</select>

	<select id="getTotalRegistAmountForRiskProperty" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO">
		SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
		ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
		ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
		ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0)+ifnull(sum(d.OTHER_FEE), 0)+ifnull(sum(d.SPECIAL_SURVEY_FEE), 0) estimateAmount  ,
		concat(risk_group_no,plan_code,duty_code) dutyCode
		from   CLMS_ESTIMATE_DUTY_RECORD d
		where d.case_times = 1
		and d.policy_no = #{policyNo, jdbcType=VARCHAR}
		and d.is_effective = 'Y'
		and d.estimate_type = '02'
		and d.risk_group_no is not null
		group by risk_group_no,plan_code,duty_code
	</select>

	<!-- 获取预估责任金额表 -->
	<select id="getEstimateDutyRecordForRestartList" parameterType="java.util.Map" resultMap="estimateDutyRecordForRestartMap">
		select
		d.CREATED_BY,
		d.UPDATED_BY,
		d.ID_AHCS_ESTIMATE_DUTY_RECORD,
		d.TASK_ID,
		d.POLICY_NO,
		d.CASE_NO,
		d.CASE_TIMES,
		d.PLAN_CODE,
		d.DUTY_CODE,
		d.DUTY_NAME,
		d.BASE_AMOUNT_PAY,
		d.ESTIMATE_AMOUNT,
		d.ARBITRAGE_FEE,
		d.LAWSUIT_FEE,
		d.COMMON_ESTIMATE_FEE,
		d.LAWYER_FEE,
		d.EXECUTE_FEE,
		d.VERIFY_APPRAISE_FEE,d.INQUIRE_FEE,d.OTHER_FEE,d.SPECIAL_SURVEY_FEE,
		(select edr.ESTIMATE_AMOUNT from CLMS_ESTIMATE_DUTY_RECORD edr
		where edr.case_times = #{caseTimes,jdbcType = INTEGER}
		and edr.case_no = #{caseNo,jdbcType = VARCHAR}
		and edr.plan_code = #{planCode,jdbcType = VARCHAR}
		and edr.IS_EFFECTIVE = 'Y'
		and edr.ESTIMATE_TYPE = '02'
		and edr.DUTY_CODE = d.DUTY_CODE) LAST_PAY_AMOUNT,
		d.ESTIMATE_TYPE,
		if((select pd.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
		where pi.CASE_NO = #{caseNo,jdbcType = VARCHAR} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
		and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=d.duty_code limit 1) = '1',TRUE,FALSE) IS_SHARE_AMOUNT,
		(select pd.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
		where pi.CASE_NO = #{caseNo,jdbcType = VARCHAR} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
		and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=d.duty_code limit 1) SHARE_DUTY_GROUP
		from CLMS_ESTIMATE_DUTY_RECORD d
		where d.case_times = #{caseTimes,jdbcType = INTEGER}
		and d.case_no = #{caseNo,jdbcType = VARCHAR}
		and d.plan_code = #{planCode,jdbcType = VARCHAR}
		and d.IS_EFFECTIVE = 'Y'
		and d.ESTIMATE_TYPE = '05'
	</select>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_ESTIMATE_DUTY_RECORD (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_ESTIMATE_DUTY_RECORD,
			TASK_ID,
			POLICY_NO,
			CASE_NO,
			CASE_TIMES,
			PLAN_CODE,
			DUTY_CODE,
			DUTY_NAME,
			BASE_AMOUNT_PAY,
			ESTIMATE_AMOUNT,
			ARBITRAGE_FEE,
			LAWSUIT_FEE,
			COMMON_ESTIMATE_FEE,
			LAWYER_FEE,
			EXECUTE_FEE,
			VERIFY_APPRAISE_FEE,
			ESTIMATE_TYPE,
			ARCHIVE_TIME,
			IS_EFFECTIVE,
			INQUIRE_FEE,
			OTHER_FEE,
			SPECIAL_SURVEY_FEE,
		    chg_pay_value,
			ss_coins_pay_value,
			ss_coins_rate,
			ss_coins_chg_pay_value
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			TASK_ID,
			POLICY_NO,
			CASE_NO,
			#{reopenCaseTimes},
			PLAN_CODE,
			DUTY_CODE,
			DUTY_NAME,
			BASE_AMOUNT_PAY,
			ESTIMATE_AMOUNT,
			ARBITRAGE_FEE,
			LAWSUIT_FEE,
			COMMON_ESTIMATE_FEE,
			LAWYER_FEE,
			EXECUTE_FEE,
			VERIFY_APPRAISE_FEE,
			'02',
			NOW(),
			IS_EFFECTIVE,
			INQUIRE_FEE,
			OTHER_FEE,
			SPECIAL_SURVEY_FEE,
		    chg_pay_value,
			ss_coins_pay_value,
			ss_coins_rate,
			ss_coins_chg_pay_value
		FROM CLMS_ESTIMATE_DUTY_RECORD
		WHERE CASE_TIMES=#{caseTimes}
		AND IS_EFFECTIVE='Y'
		AND ESTIMATE_TYPE='05'
		AND CASE_NO IN
		<foreach collection="caseNoList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</insert>

	<update id="updateEffectiveByCaseNo">
		update clms_estimate_duty_record
		set IS_EFFECTIVE = 'N',
		    UPDATED_DATE= NOW()
		where CASE_NO in (
			<foreach collection="caseNoList" item="item" separator=",">
				#{item}
			</foreach>
			)
		  and case_times = #{caseTimes,jdbcType=INTEGER}
		  and TASK_ID = 'reportTrack'
		  and estimate_type = '02'
		  and IS_EFFECTIVE = 'Y'
	</update>

	<select id="getTotalRegistAmountByIdRiskProperty" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO">
		SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
		ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
		ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
		ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0)+ifnull(sum(d.OTHER_FEE), 0)+ifnull(sum(d.SPECIAL_SURVEY_FEE), 0) estimateAmount  ,
		concat(PLAN_CODE,DUTY_CODE) dutyCode
		from  CLMS_ESTIMATE_DUTY_RECORD d
		where d.case_times = 1
		and d.id_ply_risk_property = #{idPlyRiskProperty, jdbcType=VARCHAR}
		and d.IS_EFFECTIVE = 'Y'
		and d.ESTIMATE_TYPE = '02'
		and exists(select 1 from clms_policy_claim_case b where b.CASE_NO = d.CASE_NO and d.CASE_TIMES=1 and b.INSURED_CODE=#{insuredCode, jdbcType=VARCHAR})
		group  by PLAN_CODE,duty_Code
	</select>

	<select id="getPolicyAndCaseNoByReportNo" resultMap="recordDTO">
		select
		t.POLICY_NO,
		t.CASE_NO
		from
		CLMS_estimate_policy t
		where t.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
		and t.case_times=#{caseTimes, jdbcType=INTEGER}
	</select>

	<update id="updateReportTrackEffectiveByCaseNo">
		update clms_estimate_duty_record
		set IS_EFFECTIVE = 'N',
		UPDATED_DATE= NOW()
		where CASE_NO in (
		<foreach collection="caseNoList" item="item" separator=",">
			#{item}
		</foreach>
		)
		and case_times = #{caseTimes,jdbcType=INTEGER}
		and TASK_ID = 'reportTrack'
		and estimate_type = '02'
		and IS_EFFECTIVE = 'Y'
	</update>

	<update id="batchUpdateEstimateDutyRecord" parameterType="java.util.List">
		<foreach collection="paramList" index="index" item="item" separator=";" >
			update clms_estimate_duty_record d
			set
			d.UPDATED_BY = #{item.updatedBy},
			d.ESTIMATE_AMOUNT = #{item.estimateAmount},
			d.ARBITRAGE_FEE = #{item.arbitrageFee},
			d.LAWSUIT_FEE = #{item.lawsuitFee},
			d.COMMON_ESTIMATE_FEE = #{item.commonEstimateFee},
			d.LAWYER_FEE = #{item.lawyerFee},
			d.EXECUTE_FEE = #{item.executeFee},
			d.VERIFY_APPRAISE_FEE = #{item.verifyAppraiseFee},
			d.INQUIRE_FEE = #{item.inquireFee},
			d.OTHER_FEE = #{item.otherFee},
			d.SPECIAL_SURVEY_FEE = #{item.specialSurveyFee},
			d.UPDATED_DATE = now(),
			d.ARCHIVE_TIME = #{item.archiveTime},
			d.chg_pay_value = #{item.chgPayValue},
			d.ss_coins_pay_value = #{item.ssCoinsPayValue},
			d.ss_coins_chg_pay_value = #{item.ssCoinsChgPayValue}
			where d.ID_AHCS_ESTIMATE_DUTY_RECORD = #{item.idAhcsEstimateDutyRecord}
		</foreach>
	</update>

	<select id="getCaseNoByReportNo" resultMap="recordDTO">
		SELECT
			T1.CASE_NO
		FROM
			CLMS_ESTIMATE_DUTY_RECORD T1,
			CLMS_ESTIMATE_POLICY T2
		WHERE
			T1.CASE_NO = T2.CASE_NO
			AND T1.CASE_TIMES = T2.CASE_TIMES
			AND T1.ESTIMATE_TYPE = '02'
			AND T1.IS_EFFECTIVE = 'Y'
			AND T2.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
		LIMIT 1
	</select>

	<update id="updateEstimateDateByCaseNo">
		UPDATE CLMS_ESTIMATE_DUTY_RECORD
			SET UPDATED_DATE= NOW()
		WHERE
			CASE_NO = #{caseNo, jdbcType=VARCHAR}
			AND ESTIMATE_TYPE = '02'
			AND IS_EFFECTIVE = 'Y'
	</update>

	<!--或者去赔款立案金额-->
	<select id="getestimateAmount" resultType="java.math.BigDecimal">
		select edr.ESTIMATE_AMOUNT
		from CLMS_ESTIMATE_DUTY_RECORD edr
		where edr.CASE_NO in (select pi.CASE_NO from CLMS_POLICY_INFO pi
		where
		<if test="reportNo != null and reportNo != '' " >
			pi.REPORT_NO = #{reportNo})
		</if>
		<if test="dutyCode != null and dutyCode != '' " >
			and edr.DUTY_CODE = #{dutyCode}
		</if>
		<if test="caseTimes != null " >
			and edr.CASE_TIMES = #{caseTimes}
		</if>
		and edr.IS_EFFECTIVE = 'Y'
		order by edr.UPDATED_DATE desc
		limit 1
	</select>

</mapper>