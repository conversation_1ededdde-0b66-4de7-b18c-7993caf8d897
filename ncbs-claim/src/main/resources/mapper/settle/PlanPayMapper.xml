<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.EpcisPlanDTO" id="epcisPolicy">
        <id property="planCode" column="plan_code"/>
        <result property="planName" column="plan_name"/>
        <collection property="dutyInfo"
                    ofType="com.paic.ncbs.claim.model.dto.settle.EpcisDutyDTO">
            <id property="dutyCode" column="duty_code"/>
            <result property="dutyName" column="duty_name"/>
            <result property="dutyHistoryIndemnityAmount" column="duty_history_pay"/>
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PlanPayDTO" id="result">
        <id property="idAhcsPlanPay" column="ID_CLM_PLAN_PAY"/>
        <result property="idAhcsPolicyPlan" column="id_ahcs_policy_plan"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="planName" column="PLAN_NAME"/>
        <result property="settleAmount" column="PLAN_PAY_AMOUNT"/>
        <result property="arbitrageFee" column="ARBITRATE_FEE"/>
        <result property="lawsuitFee" column="LAWSUIT_FEE"/>
        <result property="commonEstimateFee" column="EVALUATION_FEE"/>
        <result property="lawyerFee" column="LAWYER_FEE"/>
        <result property="executeFee" column="EXECUTE_FEE"/>
        <result property="verifyFee" column="CHECK_FEE"/>
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="idAhcsBatch" column="ID_CLM_BATCH"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="idPlyRiskProperty" column="id_ply_risk_property"/>
        <!-- 关联责任赔付信息 -->
        <collection property="dutyPayArr"
                    ofType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO"
                    select="com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper.selectByAhcsPlanPayId"
                    column="{caseNo=CASE_NO,caseTimes=CASE_TIMES,planCode=PLAN_CODE,subTimes=SUB_TIMES,claimType=CLAIM_TYPE,idAhcsBatch=ID_CLM_BATCH,idAhcsPolicyPlan=id_ahcs_policy_plan}">
        </collection>

    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PlanPayDTO" id="planResult">
        <id property="idAhcsPolicyPlan" column="ID_AHCS_POLICY_PLAN"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="planName" column="PLAN_NAME"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PlanPayDTO" id="resultPlanPayInfo">
        <id property="idAhcsPlanPay" column="ID_CLM_PLAN_PAY"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="settleAmount" column="PLAN_PAY_AMOUNT"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="claimType" column="CLAIM_TYPE"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PlanPayDTO" id="resultIdPlanInfo">
        <id property="idAhcsPlanPay" column="ID_AHCS_POLICY_PLAN"/>
        <result property="settleAmount" column="PLAN_PAY_AMOUNT"/>
        <result property="planCode" column="PLAN_CODE"/>
    </resultMap>

    <!--根据保单查询险种信息-->
    <select id="getByPolicy" resultMap="result" parameterType="com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO">
        select CREATED_BY ,
        CREATED_DATE ,
        UPDATED_BY ,
        UPDATED_DATE ,
        ID_CLM_PLAN_PAY ,
        (select id_ahcs_policy_plan from CLMS_policy_plan a where
        a.id_ahcs_policy_info=#{idAhcsPolicyInfo} and a.plan_code=CLM_PLAN_PAY.PLAN_CODE limit 1 )
        id_ahcs_policy_plan,
        CASE_NO ,
        CASE_TIMES ,
        CLAIM_TYPE ,
        PLAN_CODE ,
        (select ORG_PLAN_CODE from CLMS_policy_plan a where
        a.id_ahcs_policy_info=#{idAhcsPolicyInfo} and a.plan_code=CLM_PLAN_PAY.PLAN_CODE LIMIT 1 ) ORG_PLAN_CODE,
        (select ORG_PLAN_NAME from CLMS_policy_plan a where
        a.id_ahcs_policy_info=#{idAhcsPolicyInfo} and a.plan_code=CLM_PLAN_PAY.PLAN_CODE LIMIT 1 ) ORG_PLAN_NAME,
        (select PLAN_NAME from CLMS_policy_plan a where
        a.id_ahcs_policy_info=#{idAhcsPolicyInfo} and a.plan_code=CLM_PLAN_PAY.PLAN_CODE LIMIT 1 ) PLAN_NAME,
        PLAN_PAY_AMOUNT ,
        ARBITRATE_FEE ,
        LAWSUIT_FEE ,
        EVALUATION_FEE ,
        LAWYER_FEE ,
        EXECUTE_FEE ,
        CHECK_FEE ,
        DECREASE_FEE ,
        SUB_TIMES ,
        (select distinct a.group_code from CLMS_policy_plan a where
        a.id_ahcs_policy_info=#{idAhcsPolicyInfo} and a.plan_code=CLM_PLAN_PAY.PLAN_CODE) GROUP_CODE,
        ID_CLM_BATCH,
        id_ply_risk_property
        from CLM_PLAN_PAY where CASE_TIMES =#{caseTimes} and CASE_NO=#{caseNo} and CLAIM_TYPE =
        #{claimType}
        ORDER BY  PLAN_CODE
    </select>

    <!--根据保单查询险种信息-->
    <select id="getPlanByIdPolicy" resultMap="planResult" parameterType="string">
        select pp.ID_AHCS_POLICY_PLAN,
        pp.PLAN_CODE,
        pp.PLAN_NAME,
        pp.ORG_PLAN_CODE,
        pp.ORG_PLAN_NAME,
        PP.RESCUE_COMPANY
        from CLMS_POLICY_PLAN pp
        where pp.ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo}
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PlanPayDTO" id="planPayInfo">
        <id property="idAhcsPlanPay" column="ID_AHCS_POLICY_PLAN"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="planName" column="PLAN_NAME"/>
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="idPlyRiskGroup" column="id_ply_risk_group"/>
        <!-- 关联责任信息 -->
        <collection property="dutyPayArr" ofType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO"
                    select="com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper.selectPolicyDuty"
                    column="{idAhcsPlanPay = ID_AHCS_POLICY_PLAN }"/>
    </resultMap>

    <sql id="select">
        SELECT
        p.ID_CLM_PLAN_PAY,
        p.CASE_NO,
        p.PLAN_CODE,
        (select b.id_ahcs_policy_plan from CLMS_policy_info a ,clms_policy_plan b where
        a.id_ahcs_policy_info=b.id_ahcs_policy_info and
        a.case_no = p.case_no and b.plan_code = p.plan_code
        ) id_ahcs_policy_plan,
        (select PLAN_NAME from CLMS_policy_plan where CLMS_policy_plan.plan_code=p.plan_code LIMIT 1) PLAN_NAME ,
        p.PLAN_PAY_AMOUNT,
        p.ARBITRATE_FEE,
        p.LAWSUIT_FEE,
        p.EVALUATION_FEE,
        p.LAWYER_FEE,
        p.EXECUTE_FEE,
        p.CHECK_FEE,
        p.DECREASE_FEE,
        p.CLAIM_TYPE
        FROM CLM_PLAN_PAY p
    </sql>

    <!-- 险种赔付 批量插入 -->
    <insert id="insertPlanPayInfo" parameterType="java.util.List">
        insert into CLM_PLAN_PAY (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLM_PLAN_PAY,
        PLAN_CODE,
        PLAN_PAY_AMOUNT,
        ARBITRATE_FEE,
        LAWSUIT_FEE,
        EVALUATION_FEE,
        LAWYER_FEE,
        EXECUTE_FEE,
        CHECK_FEE,
        CLAIM_TYPE,
        CASE_NO,
        CASE_TIMES,
        ID_CLM_BATCH,
        MIGRATE_FROM,
        CURRENCY_CODE,
        ARCHIVE_DATE,
        SUB_TIMES,
        id_ply_risk_property
        )
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.createdBy},
            now(),
            #{item.updatedBy},
            now(),
            #{item.idAhcsPlanPay},
            #{item.planCode},
            #{item.settleAmount},
            #{item.arbitrageFee},
            #{item.lawsuitFee},
            #{item.commonEstimateFee},
            #{item.lawyerFee},
            #{item.executeFee},
            #{item.verifyFee},
            #{item.claimType},
            #{item.caseNo},
            #{item.caseTimes},
            #{item.idAhcsBatch},
            'na',
            '01',
            now(),
            #{item.subTimes,jdbcType=INTEGER},
            #{item.idPlyRiskProperty,jdbcType=VARCHAR}
            )
        </foreach>

    </insert>

    <!-- 根据赔付信息查询赔付险种 -->
    <select id="getById" parameterType="java.lang.String" resultMap="result">
        SELECT CREATED_BY ,
        CREATED_DATE ,
        UPDATED_BY ,
        UPDATED_DATE ,
        ID_CLM_PLAN_PAY ,
        (select b.id_ahcs_policy_plan from CLMS_policy_info a ,clms_policy_plan b where
        a.id_ahcs_policy_info=b.id_ahcs_policy_info and
        a.case_no = CLM_PLAN_PAY.case_no and b.plan_code = CLM_PLAN_PAY.plan_code
        ) id_ahcs_policy_plan,
        (select POLICY_NO from clm_case_base where clm_case_base.CASE_NO=CLM_PLAN_PAY.CASE_NO and
        clm_case_base.CASE_TIMES=CLM_PLAN_PAY.CASE_TIMES LIMIT 1) POLICY_NO,
        CASE_NO ,
        CASE_TIMES ,
        CLAIM_TYPE ,
        PLAN_CODE ,
        (select PLAN_NAME from CLMS_policy_plan where CLMS_POLICY_PLAN.plan_code=CLM_PLAN_PAY.plan_code LIMIT 1)
        PLAN_NAME ,
        PLAN_PAY_AMOUNT ,
        ARBITRATE_FEE ,
        LAWSUIT_FEE ,
        EVALUATION_FEE ,
        LAWYER_FEE ,
        EXECUTE_FEE ,
        CHECK_FEE ,
        DECREASE_FEE ,
        SUB_TIMES ,
        ID_CLM_BATCH FROM CLM_PLAN_PAY where ID_CLM_PLAN_PAY=#{idAhcsPlanPay}
    </select>

    <!-- 批量更新 -->
    <update id="updatePlanPayInfoList">
         update CLM_PLAN_PAY
            set
            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="planCode != null and planCode != '' ">
                PLAN_CODE = #{planCode},
            </if>

            PLAN_PAY_AMOUNT = #{settleAmount},

            ARBITRATE_FEE = #{arbitrageFee},

            LAWSUIT_FEE = #{lawsuitFee},

            EVALUATION_FEE = #{commonEstimateFee},

            LAWYER_FEE = #{lawyerFee},

            EXECUTE_FEE = #{executeFee},

            CHECK_FEE = #{verifyFee},

            <if test="claimType != null ">
                CLAIM_TYPE = #{claimType},
            </if>
            <if test="idAhcsBatch != null ">
                ID_CLM_BATCH = #{idAhcsBatch},
            </if>
            <if test="caseTimes != null ">
                CASE_TIMES = #{caseTimes},
            </if>
            <if test="caseNo != null ">
                CASE_NO = #{caseNo},
            </if>
            UPDATED_DATE = SYSDATE()
            WHERE ID_CLM_PLAN_PAY=#{idAhcsPlanPay}
    </update>

    <!-- 批量更新 -->
    <update id="updatePlanPayInfoListForTPAReSettle" parameterType="java.util.List">

        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            update CLM_PLAN_PAY
            set
            <if test="item.updatedBy != null and item.updatedBy != '' ">
                UPDATED_BY = #{item.updatedBy},
            </if>
            <if test="item.planCode != null and item.planCode != '' ">
                PLAN_CODE = #{item.planCode},
            </if>

            PLAN_PAY_AMOUNT = #{item.settleAmount},

            ARBITRATE_FEE = #{item.arbitrageFee},

            LAWSUIT_FEE = #{item.lawsuitFee},

            EVALUATION_FEE = #{item.commonEstimateFee},

            LAWYER_FEE = #{item.lawyerFee},

            EXECUTE_FEE = #{item.executeFee},

            CHECK_FEE = #{item.verifyFee},

            <if test="item.claimType != null ">
                CLAIM_TYPE = #{item.claimType},
            </if>
            <if test="item.idAhcsBatch != null ">
                ID_CLM_BATCH = #{item.idAhcsBatch},
            </if>
            <if test="item.caseTimes != null ">
                CASE_TIMES = #{item.caseTimes},
            </if>
            <if test="item.caseNo != null ">
                CASE_NO = #{item.caseNo},
            </if>
            UPDATED_DATE = SYSDATE()
            WHERE CASE_NO = #{item.caseNo} and CASE_TIMES = #{item.caseTimes} and CLAIM_TYPE = #{item.claimType}  and PLAN_CODE = #{item.planCode}
        </foreach>
    </update>

    <select id="getChaseSubTimes" resultType="java.lang.Integer">
        SELECT IFNULL(max(pp.sub_times),0)+1 FROM CLM_PLAN_PAY PP WHERE PP.CLAIM_TYPE='4'
        AND PP.CASE_NO IN(
        SELECT DISTINCT CLM_POLICY_PAY.CASE_NO FROM CLM_POLICY_PAY
        WHERE CLM_POLICY_PAY.REPORT_NO=#{reportNo})
    </select>

    <select id="getFromCopyByPolicy" resultMap="planPayInfo">
        select pp.created_by,
        pp.updated_by,
        pp.plan_code,
        pp.plan_name,
        pp.id_ahcs_policy_plan,
        0 plan_no,
        PP.GROUP_CODE
        from CLMS_policy_plan pp
        where pp.ID_AHCS_POLICY_INFO = #{idAhcsPolicyPay}
    </select>

    <delete id="deleteByBatchId" parameterType="java.lang.String">
        delete from CLM_PLAN_PAY where ID_CLM_BATCH = #{idAhcsBatch} and
        CLAIM_TYPE=#{claimType}
    </delete>

    <select id="getPlanPayInfoByReportNoList" resultMap="result">
        <include refid="select"/>
        ,CLM_POLICY_PAY po
        WHERE po.CASE_NO = p.CASE_NO
        and po.CASE_TIMES = p.CASE_TIMES
        and po.REPORT_NO = #{reportNo}
        and po.CASE_TIMES= #{caseTimes}
    </select>

    <select id="getPlanPayInfoList" resultMap="result">
        <include refid="select"/>
        WHERE p.CASE_TIMES=#{caseTimes}
        AND p.CLAIM_TYPE=#{claimType}
        AND p.CASE_NO=#{caseNo}
        <if test="idAhcsBatch != null ">
            and p.ID_CLM_BATCH=#{idAhcsBatch}
        </if>

    </select>

    <select id="getPlanDutyDetailByPolicyNo" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultMap="epcisPolicy">
        SELECT
        IFNULL(PP.ORG_PLAN_CODE,PP.PLAN_CODE) PLAN_CODE,
        PP.Plan_Name,
        IFNULL(PD.ORG_DUTY_CODE,PD.DUTY_CODE) DUTY_CODE,
        PD.Duty_Name,
        IFNULL(PDD.ORG_DUTY_DETAIL_CODE,PDD.DUTY_DETAIL_CODE) DUTY_DETAIL_CODE,
        PDD.Duty_Detail_Name,
        ((SELECT IFNULL(SUM(IFNULL(A.DUTY_PAY_AMOUNT, 0)), 0) FROM CLM_PLAN_DUTY_PAY A,CLMS_POLICY_CLAIM_CASE
        B,CLMS_CASE_PROCESS C,CLMS_policy_info d,CLMS_policy_plan e
        ,CLMS_policy_duty f
        where
        d.id_ahcs_policy_info=e.id_ahcs_policy_info
        and e.id_ahcs_policy_plan=f.id_ahcs_policy_plan
        and a.case_no=d.case_no
        and e.plan_code=a.plan_code
        and f.duty_code=a.duty_code
        and B.POLICY_NO = #{policyNo}
        AND B.INSURED_CODE = #{insuredCode}
        AND (A.DUTY_CODE = PD.Duty_Code or A.DUTY_CODE = PD.ORG_Duty_Code or f.org_duty_code=PD.Duty_Code )
        AND (A.PLAN_CODE = PP.PLAN_CODE or A.PLAN_CODE = PP.ORG_PLAN_CODE or e.org_plan_code= PP.PLAN_CODE)
        AND A.CLAIM_TYPE = '1'
        AND A.CASE_NO = B.CASE_NO
        AND B.REPORT_NO= C.REPORT_NO
        AND A.CASE_TIMES = C.CASE_TIMES
        AND (C.PROCESS_STATUS = '05' or C.PROCESS_STATUS = '06'))) -
        (SELECT IFNULL(SUM(IFNULL(A.DUTY_PAY_AMOUNT, 0)), 0) FROM CLM_PLAN_DUTY_PAY A,CLMS_POLICY_CLAIM_CASE
        B,CLMS_CASE_PROCESS C,CLMS_policy_info d,CLMS_policy_plan e
        ,CLMS_policy_duty f
        where
        d.id_ahcs_policy_info=e.id_ahcs_policy_info
        and e.id_ahcs_policy_plan=f.id_ahcs_policy_plan
        and a.case_no=d.case_no
        and e.plan_code=a.plan_code
        and f.duty_code=a.duty_code
        and B.POLICY_NO = #{policyNo}
        AND B.INSURED_CODE = #{insuredCode}
        AND (A.DUTY_CODE = PD.Duty_Code or A.DUTY_CODE = PD.ORG_Duty_Code or f.org_duty_code=PD.Duty_Code )
        AND (A.PLAN_CODE = PP.PLAN_CODE or A.PLAN_CODE = PP.ORG_PLAN_CODE or e.org_plan_code= PP.PLAN_CODE)
        AND A.CLAIM_TYPE = '4'
        AND A.CASE_NO = B.CASE_NO
        AND B.REPORT_NO= C.REPORT_NO
        AND A.CASE_TIMES = C.CASE_TIMES
        AND (C.PROCESS_STATUS = '05' or C.PROCESS_STATUS = '06')) duty_history_pay,
        ((SELECT IFNULL(SUM(IFNULL(IF(A.SETTLE_AMOUNT &gt; 0,A.SETTLE_AMOUNT,A.AUTO_SETTLE_AMOUNT), 0)),0)
        FROM CLMS_DUTY_DETAIL_PAY A,clms_POLICY_CLAIM_CASE B,clms_CASE_PROCESS C,clms_policy_info d,clms_policy_plan e
        ,clms_policy_duty f,clms_policy_duty_detail g
        where
        d.id_ahcs_policy_info=e.id_ahcs_policy_info
        and e.id_ahcs_policy_plan=f.id_ahcs_policy_plan
        and f.id_ahcs_policy_duty=g.id_ahcs_policy_duty
        and a.case_no=d.case_no
        AND A.IS_EFFECTIVE = 'Y'
        and e.plan_code=a.plan_code
        and f.duty_code=a.duty_code
        and g.duty_detail_code=a.DUTY_DETAIL_CODE
        and B.POLICY_NO = #{policyNo}
        AND B.INSURED_CODE = #{insuredCode}
        AND (A.DUTY_DETAIL_CODE = PDD.DUTY_DETAIL_CODE or A.DUTY_DETAIL_CODE = PDD.ORG_DUTY_DETAIL_CODE or
        g.org_duty_detail_code= PDD.DUTY_DETAIL_CODE )
        AND (A.DUTY_CODE = PD.Duty_Code or A.DUTY_CODE = PD.ORG_Duty_Code or f.org_duty_code=PD.Duty_Code )
        AND (A.PLAN_CODE = PP.PLAN_CODE or A.PLAN_CODE = PP.ORG_PLAN_CODE or e.org_plan_code= PP.PLAN_CODE)
        AND A.CLAIM_TYPE = '1'
        AND A.CASE_NO = B.CASE_NO
        AND B.REPORT_NO= C.REPORT_NO
        AND A.CASE_TIMES = C.CASE_TIMES
        AND (C.PROCESS_STATUS = '05' or C.PROCESS_STATUS = '06'))-
        (SELECT IFNULL(SUM(IFNULL(IF(A.SETTLE_AMOUNT &gt; 0,A.SETTLE_AMOUNT,A.AUTO_SETTLE_AMOUNT), 0)),0)
        FROM CLMS_DUTY_DETAIL_PAY A,clms_POLICY_CLAIM_CASE B,clms_CASE_PROCESS C,clms_policy_info d,clms_policy_plan e
        ,clms_policy_duty f,clms_policy_duty_detail g
        where
        d.id_ahcs_policy_info=e.id_ahcs_policy_info
        and e.id_ahcs_policy_plan=f.id_ahcs_policy_plan
        and f.id_ahcs_policy_duty=g.id_ahcs_policy_duty
        and a.case_no=d.case_no
        AND A.IS_EFFECTIVE = 'Y'
        and e.plan_code=a.plan_code
        and f.duty_code=a.duty_code
        and g.duty_detail_code=a.DUTY_DETAIL_CODE
        and B.POLICY_NO = #{policyNo}
        AND B.INSURED_CODE = #{insuredCode}
        AND (A.DUTY_DETAIL_CODE = PDD.DUTY_DETAIL_CODE or A.DUTY_DETAIL_CODE = PDD.ORG_DUTY_DETAIL_CODE or
        g.org_duty_detail_code= PDD.DUTY_DETAIL_CODE )
        AND (A.DUTY_CODE = PD.Duty_Code or A.DUTY_CODE = PD.ORG_Duty_Code or f.org_duty_code=PD.Duty_Code )
        AND (A.PLAN_CODE = PP.PLAN_CODE or A.PLAN_CODE = PP.ORG_PLAN_CODE or e.org_plan_code= PP.PLAN_CODE)
        AND A.CLAIM_TYPE = '4'
        AND A.CASE_NO = B.CASE_NO
        AND B.REPORT_NO= C.REPORT_NO
        AND A.CASE_TIMES = C.CASE_TIMES
        AND (C.PROCESS_STATUS = '05' or C.PROCESS_STATUS = '06'))) detail_history_pay
        FROM CLMS_POLICY_DUTY PD,
        CLMS_POLICY_PLAN PP,
        CLMS_POLICY_INFO PI,
        CLMS_POLICY_DUTY_DETAIL PDD
        WHERE PI.ID_AHCS_POLICY_INFO = PP.ID_AHCS_POLICY_INFO
        AND PD.ID_AHCS_POLICY_PLAN = PP.ID_AHCS_POLICY_PLAN
        AND PD.ID_AHCS_POLICY_DUTY = PDD.ID_AHCS_POLICY_DUTY
        AND PI.POLICY_NO= #{policyNo}
        AND PI.REPORT_NO =(select po.report_no
        from CLMS_policy_info po
        where po.policy_no = #{policyNo}
        LIMIT 1)
    </select>

    <!--根据赔案查询险种赔款大于0的赔付信息-->
    <select id="getPlanPayInfoByCaseNo" resultMap="resultPlanPayInfo">
        select ID_CLM_PLAN_PAY,
        CASE_NO,
        CASE_TIMES,
        CLAIM_TYPE,
        PLAN_CODE,
        PLAN_PAY_AMOUNT
        from CLM_PLAN_PAY
        where CASE_NO=#{caseNo}
        and CASE_TIMES =#{caseTimes}
        and CLAIM_TYPE = #{claimType}
        and PLAN_PAY_AMOUNT is not null
        and PLAN_PAY_AMOUNT > 0
    </select>

    <!--根据赔案查询一个险种赔款的赔付信息-->
    <select id="getPlanPayInfoByCaseNoTimes" resultMap="resultPlanPayInfo">
        select ID_CLM_PLAN_PAY,
        CASE_NO,
        CASE_TIMES,
        CLAIM_TYPE,
        PLAN_CODE,
        PLAN_PAY_AMOUNT
        from CLM_PLAN_PAY
        where CASE_NO=#{caseNo}
        and CASE_TIMES =#{caseTimes}
        LIMIT 1
    </select>

    <!--根据赔案查询险种赔款大于等于0的赔付信息-->
    <select id="getCasePlanPayInfoByCaseNo" resultMap="resultPlanPayInfo">
        select p.ID_CLM_PLAN_PAY,
        p.CASE_NO,
        p.CASE_TIMES,
        p.CLAIM_TYPE,
        p.PLAN_CODE,
        (select pp.ORG_PLAN_CODE from CLMS_POLICY_INFO pi, CLMS_POLICY_PLAN pp
        where pi.report_no = #{reportNo}
        and pi.case_no = #{caseNo}
        and pi.ID_AHCS_POLICY_INFO=pp.ID_AHCS_POLICY_INFO
        and p.PLAN_CODE=pp.PLAN_CODE LIMIT 1) as ORG_PLAN_CODE,
        IFNULL(p.PLAN_PAY_AMOUNT,0) PLAN_PAY_AMOUNT
        from CLM_PLAN_PAY p
        where p.CASE_NO = #{caseNo}
        and p.CASE_TIMES = #{caseTimes}
        and p.CLAIM_TYPE = #{claimType}
        and p.PLAN_PAY_AMOUNT is not null
        and p.PLAN_PAY_AMOUNT >= 0
    </select>

    <!--根据赔案查询险种有赔款的赔付信息-->
    <select id="getPlanPayInfoByCase" resultMap="resultPlanPayInfo">
        select p.ID_CLM_PLAN_PAY,
        p.CASE_NO,
        p.CASE_TIMES,
        p.CLAIM_TYPE,
        p.PLAN_CODE,
        (select pp.ORG_PLAN_CODE from CLMS_POLICY_INFO pi, CLMS_POLICY_PLAN pp
        where pi.report_no = #{reportNo}
        and pi.case_no = #{caseNo}
        and pi.ID_AHCS_POLICY_INFO=pp.ID_AHCS_POLICY_INFO
        and p.PLAN_CODE=pp.PLAN_CODE LIMIT 1) as ORG_PLAN_CODE,
        IFNULL(p.PLAN_PAY_AMOUNT,0) PLAN_PAY_AMOUNT
        from CLM_PLAN_PAY p
        where p.CASE_NO = #{caseNo}
        and p.CASE_TIMES = #{caseTimes}
        and p.CLAIM_TYPE = #{claimType}
        and p.PLAN_PAY_AMOUNT is not null
    </select>

    <!-- 根据报案号、赔付次数、机构获取预估或立案金额大于等于0的险种，值都为0 -->
    <select id="getEstimatePlanList" resultMap="resultPlanPayInfo">
        select distinct d.PLAN_CODE,
        (select pp.ORG_PLAN_CODE from CLMS_policy_plan pp
        where pi.ID_AHCS_POLICY_INFO=pp.ID_AHCS_POLICY_INFO
        and d.PLAN_CODE=pp.PLAN_CODE LIMIT 1) as ORG_PLAN_CODE,
        pi.ID_AHCS_POLICY_INFO,
        0 PLAN_PAY_AMOUNT
        from CLMS_estimate_duty_record d, CLMS_policy_info pi
        where pi.CASE_NO = d.CASE_NO
        and pi.REPORT_NO = #{reportNo}
        and d.CASE_TIMES = #{caseTimes}
        and d.CASE_NO = #{caseNo}
        and d.ESTIMATE_TYPE = #{estimateType}
        and d.ESTIMATE_AMOUNT is not null
        and d.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 根据报案号、赔付次数、机构获取预估为空的险种 -->
    <select id="getEstimateNullPlanList" resultMap="resultPlanPayInfo">
        select d.PLAN_CODE,
        (select pp.ORG_PLAN_CODE from CLMS_policy_plan pp
        where pi.ID_AHCS_POLICY_INFO=pp.ID_AHCS_POLICY_INFO
        and d.PLAN_CODE=pp.PLAN_CODE LIMIT 1) as ORG_PLAN_CODE,
        pi.ID_AHCS_POLICY_INFO,
        0 PLAN_PAY_AMOUNT
        from CLMS_estimate_duty_record d, CLMS_policy_info pi
        where pi.CASE_NO = d.CASE_NO
        and pi.REPORT_NO = #{reportNo}
        and d.CASE_TIMES = #{caseTimes}
        and d.CASE_NO = #{caseNo}
        and d.ESTIMATE_TYPE = #{estimateType}
        and d.IS_EFFECTIVE = 'Y'
        LIMIT 1
    </select>

    <!--根据险种id查询险种代码-->
    <select id="getPlanCodeByIdPolicy" resultType="string">
        select pp.PLAN_CODE
        from CLMS_POLICY_PLAN pp
        where pp.ID_AHCS_POLICY_PLAN=#{idAhcsPlanPay}
    </select>

    <!--根据保单id查询险种保额-->
    <select id="getIdPlanByIdPolicy" resultMap="resultIdPlanInfo">
        select pp.ID_AHCS_POLICY_PLAN,
        sum(pu.duty_amount) PLAN_PAY_AMOUNT
        from CLMS_POLICY_PLAN pp, CLMS_POLICY_DUTY pu
        where pp.ID_AHCS_POLICY_PLAN = pu.ID_AHCS_POLICY_PLAN
        and pp.ID_AHCS_POLICY_INFO=#{idAhcsPolicyInfo}
        group by pp.id_ahcs_policy_plan
    </select>

    <!--根据赔案号查询此条赔案号的险种类型-->
    <select id="getTypeOfCaseNo" resultType="java.lang.String">
        select duty_code as dutyCode from clm_plan_duty_pay where case_no=#{caseNo}
    </select>

</mapper>