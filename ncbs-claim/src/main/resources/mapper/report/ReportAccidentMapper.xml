<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity">
        <id column="ID_CLM_REPORT_ACCIDENT" property="idClmReportAccident" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_CAUSE_LEVEL1" property="accidentCauseLevel1" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_CAUSE_LEVEL2" property="accidentCauseLevel2" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_CAUSE_LEVEL3" property="accidentCauseLevel3" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_DETAIL" property="accidentDetail" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_CITY_CODE" property="accidentCityCode" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_COUNTY_CODE" property="accidentCountyCode" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_PLACE" property="accidentPlace" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_DATE" property="accidentDate" jdbcType="TIMESTAMP"/>
        <result column="ACCIDENT_TYPE" property="accidentType" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_TYPE_DETAIL" property="accidentTypeDetail" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_RESPONSIBILITY" property="accidentResponsibility" jdbcType="VARCHAR"/>
        <result column="IS_THIRD_NOT_FOUND" property="isThirdNotFound" jdbcType="VARCHAR"/>
        <result column="IS_VIOLATION_LOADING" property="isViolationLoading" jdbcType="VARCHAR"/>
        <result column="IS_TRAVEL_ACROSS_REGION" property="isTravelAcrossRegion" jdbcType="VARCHAR"/>
        <result column="IS_FIRST_CARGO_LOSS" property="isFirstCargoLoss" jdbcType="VARCHAR"/>
        <result column="IS_DRIVER_INJURED" property="isDriverInjured" jdbcType="VARCHAR"/>
        <result column="IS_PANSSENGE_INJURED" property="isPanssengeInjured" jdbcType="VARCHAR"/>
        <result column="IS_THI_CAR_LOSS" property="isThiCarLoss" jdbcType="VARCHAR"/>
        <result column="IS_IN_THI_CAR_INJURED" property="isInThiCarInjured" jdbcType="VARCHAR"/>
        <result column="IS_OUT_THI_CAR_INJURED" property="isOutThiCarInjured" jdbcType="VARCHAR"/>
        <result column="IS_IN_THI_CARGO_LOSS" property="isInThiCargoLoss" jdbcType="VARCHAR"/>
        <result column="IS_OUT_THI_CARGO_LOSS" property="isOutThiCargoLoss" jdbcType="VARCHAR"/>
        <result column="MIGRATE_FROM" property="migrateFrom" jdbcType="VARCHAR"/>
        <result column="IS_CAN_DRIVING" property="isCanDriving" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_TOWN_CODE" property="accidentTownCode" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_VILLAGE_CODE" property="accidentVillageCode" jdbcType="VARCHAR"/>
        <result column="IS_ON_FREEWAY" property="isOnFreeway" jdbcType="VARCHAR"/>
        <result column="INJURED_NUMBER" property="injuredNumber" jdbcType="DECIMAL"/>
        <result column="DEAT_TOLL" property="deatToll" jdbcType="DECIMAL"/>
        <result column="ONE_CONDITION_FLAG" property="oneConditionFlag" jdbcType="VARCHAR"/>
        <result column="DAMAGE_CLIMATE" property="damageClimate" jdbcType="VARCHAR"/>
        <result column="DISPOSE_STYLE" property="disposeStyle" jdbcType="VARCHAR"/>
        <result column="OCCURRENCE_AREA" property="occurrenceArea" jdbcType="VARCHAR"/>
        <result column="REASON" property="reason" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_PLACE_STRING" property="accidentPlaceString" jdbcType="VARCHAR"/>
        <result column="SHANGHAI_ROAD_NAME" property="shanghaiRoadName" jdbcType="VARCHAR"/>
        <result column="SHANGHAI_ROAD_ID" property="shanghaiRoadId" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_PLACE_CODE" property="accidentPlaceCode" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_MILEAGE" property="accidentMileage" jdbcType="DECIMAL"/>
        <result column="ACCIDENT_PLACE_GPS_X" property="accidentPlaceGpsX" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_PLACE_GPS_Y" property="accidentPlaceGpsY" jdbcType="VARCHAR"/>
        <result column="IS_DUTY_CLEAR" property="isDutyClear" jdbcType="VARCHAR"/>
        <result column="OVERSEAS_OCCUR" property="overseasOccur" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_AREA" property="accidentArea" jdbcType="VARCHAR"/>
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_NAME" property="accidentName" jdbcType="VARCHAR"/>
        <result column="OVERSEA_NATION_CODE" property="overseaNationCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_CLM_REPORT_ACCIDENT, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO,
        ACCIDENT_CAUSE_LEVEL1, ACCIDENT_CAUSE_LEVEL2, ACCIDENT_CAUSE_LEVEL3, ACCIDENT_DETAIL,
        ACCIDENT_CITY_CODE, ACCIDENT_COUNTY_CODE, ACCIDENT_PLACE, ACCIDENT_DATE, ACCIDENT_TYPE,
        ACCIDENT_TYPE_DETAIL, ACCIDENT_RESPONSIBILITY, IS_THIRD_NOT_FOUND, IS_VIOLATION_LOADING,
        IS_TRAVEL_ACROSS_REGION, IS_FIRST_CARGO_LOSS, IS_DRIVER_INJURED, IS_PANSSENGE_INJURED,
        IS_THI_CAR_LOSS, IS_IN_THI_CAR_INJURED, IS_OUT_THI_CAR_INJURED, IS_IN_THI_CARGO_LOSS,
        IS_OUT_THI_CARGO_LOSS, MIGRATE_FROM, IS_CAN_DRIVING, ACCIDENT_TOWN_CODE, ACCIDENT_VILLAGE_CODE,
        IS_ON_FREEWAY, INJURED_NUMBER, DEAT_TOLL, ONE_CONDITION_FLAG, DAMAGE_CLIMATE, DISPOSE_STYLE,
        OCCURRENCE_AREA, REASON, ACCIDENT_PLACE_STRING, SHANGHAI_ROAD_NAME, SHANGHAI_ROAD_ID,
        ACCIDENT_PLACE_CODE, ACCIDENT_MILEAGE, ACCIDENT_PLACE_GPS_X, ACCIDENT_PLACE_GPS_Y,
        IS_DUTY_CLEAR, OVERSEAS_OCCUR, ACCIDENT_AREA, PROVINCE_CODE,ACCIDENT_NAME,OVERSEA_NATION_CODE
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_REPORT_ACCIDENT
        where ID_CLM_REPORT_ACCIDENT = #{idClmReportAccident,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccident" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_REPORT_ACCIDENT
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLM_REPORT_ACCIDENT
        where ID_CLM_REPORT_ACCIDENT = #{idClmReportAccident,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity">
        insert into CLM_REPORT_ACCIDENT (ID_CLM_REPORT_ACCIDENT, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        ACCIDENT_CAUSE_LEVEL1, ACCIDENT_CAUSE_LEVEL2,
        ACCIDENT_CAUSE_LEVEL3, ACCIDENT_DETAIL, ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE, ACCIDENT_PLACE, ACCIDENT_DATE,
        ACCIDENT_TYPE, ACCIDENT_TYPE_DETAIL, ACCIDENT_RESPONSIBILITY,
        IS_THIRD_NOT_FOUND, IS_VIOLATION_LOADING, IS_TRAVEL_ACROSS_REGION,
        IS_FIRST_CARGO_LOSS, IS_DRIVER_INJURED, IS_PANSSENGE_INJURED,
        IS_THI_CAR_LOSS, IS_IN_THI_CAR_INJURED, IS_OUT_THI_CAR_INJURED,
        IS_IN_THI_CARGO_LOSS, IS_OUT_THI_CARGO_LOSS, MIGRATE_FROM,
        IS_CAN_DRIVING, ACCIDENT_TOWN_CODE, ACCIDENT_VILLAGE_CODE,
        IS_ON_FREEWAY, INJURED_NUMBER, DEAT_TOLL,
        ONE_CONDITION_FLAG, DAMAGE_CLIMATE, DISPOSE_STYLE,
        OCCURRENCE_AREA, REASON, ACCIDENT_PLACE_STRING,
        SHANGHAI_ROAD_NAME, SHANGHAI_ROAD_ID, ACCIDENT_PLACE_CODE,
        ACCIDENT_MILEAGE, ACCIDENT_PLACE_GPS_X, ACCIDENT_PLACE_GPS_Y,
        IS_DUTY_CLEAR, OVERSEAS_OCCUR, ACCIDENT_AREA,
        PROVINCE_CODE,ACCIDENT_NAME,OVERSEA_NATION_CODE,ARCHIVE_DATE)
        values (#{idClmReportAccident,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
        #{accidentCauseLevel1,jdbcType=VARCHAR}, #{accidentCauseLevel2,jdbcType=VARCHAR},
        #{accidentCauseLevel3,jdbcType=VARCHAR}, #{accidentDetail,jdbcType=VARCHAR},
        #{accidentCityCode,jdbcType=VARCHAR},
        #{accidentCountyCode,jdbcType=VARCHAR}, #{accidentPlace,jdbcType=VARCHAR}, #{accidentDate,jdbcType=TIMESTAMP},
        #{accidentType,jdbcType=VARCHAR}, #{accidentTypeDetail,jdbcType=VARCHAR},
        #{accidentResponsibility,jdbcType=VARCHAR},
        #{isThirdNotFound,jdbcType=VARCHAR}, #{isViolationLoading,jdbcType=VARCHAR},
        #{isTravelAcrossRegion,jdbcType=VARCHAR},
        #{isFirstCargoLoss,jdbcType=VARCHAR}, #{isDriverInjured,jdbcType=VARCHAR},
        #{isPanssengeInjured,jdbcType=VARCHAR},
        #{isThiCarLoss,jdbcType=VARCHAR}, #{isInThiCarInjured,jdbcType=VARCHAR}, #{isOutThiCarInjured,jdbcType=VARCHAR},
        #{isInThiCargoLoss,jdbcType=VARCHAR}, #{isOutThiCargoLoss,jdbcType=VARCHAR}, #{migrateFrom,jdbcType=VARCHAR},
        #{isCanDriving,jdbcType=VARCHAR}, #{accidentTownCode,jdbcType=VARCHAR}, #{accidentVillageCode,jdbcType=VARCHAR},
        #{isOnFreeway,jdbcType=VARCHAR}, #{injuredNumber,jdbcType=DECIMAL}, #{deatToll,jdbcType=DECIMAL},
        #{oneConditionFlag,jdbcType=VARCHAR}, #{damageClimate,jdbcType=VARCHAR}, #{disposeStyle,jdbcType=VARCHAR},
        #{occurrenceArea,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{accidentPlaceString,jdbcType=VARCHAR},
        #{shanghaiRoadName,jdbcType=VARCHAR}, #{shanghaiRoadId,jdbcType=VARCHAR}, #{accidentPlaceCode,jdbcType=VARCHAR},
        #{accidentMileage,jdbcType=DECIMAL}, #{accidentPlaceGpsX,jdbcType=VARCHAR},
        #{accidentPlaceGpsY,jdbcType=VARCHAR},
        #{isDutyClear,jdbcType=VARCHAR}, #{overseasOccur,jdbcType=VARCHAR}, #{accidentArea,jdbcType=VARCHAR},
        #{provinceCode,jdbcType=VARCHAR},#{accidentName,jdbcType=VARCHAR},#{overseaNationCode,jdbcType=VARCHAR},now())
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity">
        update CLM_REPORT_ACCIDENT
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="accidentCauseLevel1 != null">
                ACCIDENT_CAUSE_LEVEL1 = #{accidentCauseLevel1,jdbcType=VARCHAR},
            </if>
            <if test="accidentCauseLevel2 != null">
                ACCIDENT_CAUSE_LEVEL2 = #{accidentCauseLevel2,jdbcType=VARCHAR},
            </if>
            <if test="accidentCauseLevel3 != null">
                ACCIDENT_CAUSE_LEVEL3 = #{accidentCauseLevel3,jdbcType=VARCHAR},
            </if>
            <if test="accidentDetail != null">
                ACCIDENT_DETAIL = #{accidentDetail,jdbcType=VARCHAR},
            </if>
            <if test="accidentCityCode != null">
                ACCIDENT_CITY_CODE = #{accidentCityCode,jdbcType=VARCHAR},
            </if>
            <if test="accidentCountyCode != null">
                ACCIDENT_COUNTY_CODE = #{accidentCountyCode,jdbcType=VARCHAR},
            </if>
            <if test="accidentPlace != null">
                ACCIDENT_PLACE = #{accidentPlace,jdbcType=VARCHAR},
            </if>
            <if test="accidentDate != null">
                ACCIDENT_DATE = #{accidentDate,jdbcType=TIMESTAMP},
            </if>
            <if test="accidentType != null">
                ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR},
            </if>
            <if test="accidentTypeDetail != null">
                ACCIDENT_TYPE_DETAIL = #{accidentTypeDetail,jdbcType=VARCHAR},
            </if>
            <if test="accidentResponsibility != null">
                ACCIDENT_RESPONSIBILITY = #{accidentResponsibility,jdbcType=VARCHAR},
            </if>
            <if test="isThirdNotFound != null">
                IS_THIRD_NOT_FOUND = #{isThirdNotFound,jdbcType=VARCHAR},
            </if>
            <if test="isViolationLoading != null">
                IS_VIOLATION_LOADING = #{isViolationLoading,jdbcType=VARCHAR},
            </if>
            <if test="isTravelAcrossRegion != null">
                IS_TRAVEL_ACROSS_REGION = #{isTravelAcrossRegion,jdbcType=VARCHAR},
            </if>
            <if test="isFirstCargoLoss != null">
                IS_FIRST_CARGO_LOSS = #{isFirstCargoLoss,jdbcType=VARCHAR},
            </if>
            <if test="isDriverInjured != null">
                IS_DRIVER_INJURED = #{isDriverInjured,jdbcType=VARCHAR},
            </if>
            <if test="isPanssengeInjured != null">
                IS_PANSSENGE_INJURED = #{isPanssengeInjured,jdbcType=VARCHAR},
            </if>
            <if test="isThiCarLoss != null">
                IS_THI_CAR_LOSS = #{isThiCarLoss,jdbcType=VARCHAR},
            </if>
            <if test="isInThiCarInjured != null">
                IS_IN_THI_CAR_INJURED = #{isInThiCarInjured,jdbcType=VARCHAR},
            </if>
            <if test="isOutThiCarInjured != null">
                IS_OUT_THI_CAR_INJURED = #{isOutThiCarInjured,jdbcType=VARCHAR},
            </if>
            <if test="isInThiCargoLoss != null">
                IS_IN_THI_CARGO_LOSS = #{isInThiCargoLoss,jdbcType=VARCHAR},
            </if>
            <if test="isOutThiCargoLoss != null">
                IS_OUT_THI_CARGO_LOSS = #{isOutThiCargoLoss,jdbcType=VARCHAR},
            </if>
            <if test="migrateFrom != null">
                MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
            </if>
            <if test="isCanDriving != null">
                IS_CAN_DRIVING = #{isCanDriving,jdbcType=VARCHAR},
            </if>
            <if test="accidentTownCode != null">
                ACCIDENT_TOWN_CODE = #{accidentTownCode,jdbcType=VARCHAR},
            </if>
            <if test="accidentVillageCode != null">
                ACCIDENT_VILLAGE_CODE = #{accidentVillageCode,jdbcType=VARCHAR},
            </if>
            <if test="isOnFreeway != null">
                IS_ON_FREEWAY = #{isOnFreeway,jdbcType=VARCHAR},
            </if>
            <if test="injuredNumber != null">
                INJURED_NUMBER = #{injuredNumber,jdbcType=DECIMAL},
            </if>
            <if test="deatToll != null">
                DEAT_TOLL = #{deatToll,jdbcType=DECIMAL},
            </if>
            <if test="oneConditionFlag != null">
                ONE_CONDITION_FLAG = #{oneConditionFlag,jdbcType=VARCHAR},
            </if>
            <if test="damageClimate != null">
                DAMAGE_CLIMATE = #{damageClimate,jdbcType=VARCHAR},
            </if>
            <if test="disposeStyle != null">
                DISPOSE_STYLE = #{disposeStyle,jdbcType=VARCHAR},
            </if>
            <if test="occurrenceArea != null">
                OCCURRENCE_AREA = #{occurrenceArea,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                REASON = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="accidentPlaceString != null">
                ACCIDENT_PLACE_STRING = #{accidentPlaceString,jdbcType=VARCHAR},
            </if>
            <if test="shanghaiRoadName != null">
                SHANGHAI_ROAD_NAME = #{shanghaiRoadName,jdbcType=VARCHAR},
            </if>
            <if test="shanghaiRoadId != null">
                SHANGHAI_ROAD_ID = #{shanghaiRoadId,jdbcType=VARCHAR},
            </if>
            <if test="accidentPlaceCode != null">
                ACCIDENT_PLACE_CODE = #{accidentPlaceCode,jdbcType=VARCHAR},
            </if>
            <if test="accidentMileage != null">
                ACCIDENT_MILEAGE = #{accidentMileage,jdbcType=DECIMAL},
            </if>
            <if test="accidentPlaceGpsX != null">
                ACCIDENT_PLACE_GPS_X = #{accidentPlaceGpsX,jdbcType=VARCHAR},
            </if>
            <if test="accidentPlaceGpsY != null">
                ACCIDENT_PLACE_GPS_Y = #{accidentPlaceGpsY,jdbcType=VARCHAR},
            </if>
            <if test="isDutyClear != null">
                IS_DUTY_CLEAR = #{isDutyClear,jdbcType=VARCHAR},
            </if>
            <if test="overseasOccur != null">
                OVERSEAS_OCCUR = #{overseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="accidentArea != null">
                ACCIDENT_AREA = #{accidentArea,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="accidentName != null">
                ACCIDENT_NAME = #{accidentName,jdbcType=VARCHAR},
            </if>
            <if test="overseaNationCode != null">
                OVERSEA_NATION_CODE = #{overseaNationCode,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_CLM_REPORT_ACCIDENT = #{idClmReportAccident,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity">
        update CLM_REPORT_ACCIDENT
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        ACCIDENT_CAUSE_LEVEL1 = #{accidentCauseLevel1,jdbcType=VARCHAR},
        ACCIDENT_CAUSE_LEVEL2 = #{accidentCauseLevel2,jdbcType=VARCHAR},
        ACCIDENT_CAUSE_LEVEL3 = #{accidentCauseLevel3,jdbcType=VARCHAR},
        ACCIDENT_DETAIL = #{accidentDetail,jdbcType=VARCHAR},
        ACCIDENT_CITY_CODE = #{accidentCityCode,jdbcType=VARCHAR},
        ACCIDENT_COUNTY_CODE = #{accidentCountyCode,jdbcType=VARCHAR},
        ACCIDENT_PLACE = #{accidentPlace,jdbcType=VARCHAR},
        ACCIDENT_DATE = #{accidentDate,jdbcType=TIMESTAMP},
        ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR},
        ACCIDENT_TYPE_DETAIL = #{accidentTypeDetail,jdbcType=VARCHAR},
        ACCIDENT_RESPONSIBILITY = #{accidentResponsibility,jdbcType=VARCHAR},
        IS_THIRD_NOT_FOUND = #{isThirdNotFound,jdbcType=VARCHAR},
        IS_VIOLATION_LOADING = #{isViolationLoading,jdbcType=VARCHAR},
        IS_TRAVEL_ACROSS_REGION = #{isTravelAcrossRegion,jdbcType=VARCHAR},
        IS_FIRST_CARGO_LOSS = #{isFirstCargoLoss,jdbcType=VARCHAR},
        IS_DRIVER_INJURED = #{isDriverInjured,jdbcType=VARCHAR},
        IS_PANSSENGE_INJURED = #{isPanssengeInjured,jdbcType=VARCHAR},
        IS_THI_CAR_LOSS = #{isThiCarLoss,jdbcType=VARCHAR},
        IS_IN_THI_CAR_INJURED = #{isInThiCarInjured,jdbcType=VARCHAR},
        IS_OUT_THI_CAR_INJURED = #{isOutThiCarInjured,jdbcType=VARCHAR},
        IS_IN_THI_CARGO_LOSS = #{isInThiCargoLoss,jdbcType=VARCHAR},
        IS_OUT_THI_CARGO_LOSS = #{isOutThiCargoLoss,jdbcType=VARCHAR},
        MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
        IS_CAN_DRIVING = #{isCanDriving,jdbcType=VARCHAR},
        ACCIDENT_TOWN_CODE = #{accidentTownCode,jdbcType=VARCHAR},
        ACCIDENT_VILLAGE_CODE = #{accidentVillageCode,jdbcType=VARCHAR},
        IS_ON_FREEWAY = #{isOnFreeway,jdbcType=VARCHAR},
        INJURED_NUMBER = #{injuredNumber,jdbcType=DECIMAL},
        DEAT_TOLL = #{deatToll,jdbcType=DECIMAL},
        ONE_CONDITION_FLAG = #{oneConditionFlag,jdbcType=VARCHAR},
        DAMAGE_CLIMATE = #{damageClimate,jdbcType=VARCHAR},
        DISPOSE_STYLE = #{disposeStyle,jdbcType=VARCHAR},
        OCCURRENCE_AREA = #{occurrenceArea,jdbcType=VARCHAR},
        REASON = #{reason,jdbcType=VARCHAR},
        ACCIDENT_PLACE_STRING = #{accidentPlaceString,jdbcType=VARCHAR},
        SHANGHAI_ROAD_NAME = #{shanghaiRoadName,jdbcType=VARCHAR},
        SHANGHAI_ROAD_ID = #{shanghaiRoadId,jdbcType=VARCHAR},
        ACCIDENT_PLACE_CODE = #{accidentPlaceCode,jdbcType=VARCHAR},
        ACCIDENT_MILEAGE = #{accidentMileage,jdbcType=DECIMAL},
        ACCIDENT_PLACE_GPS_X = #{accidentPlaceGpsX,jdbcType=VARCHAR},
        ACCIDENT_PLACE_GPS_Y = #{accidentPlaceGpsY,jdbcType=VARCHAR},
        IS_DUTY_CLEAR = #{isDutyClear,jdbcType=VARCHAR},
        OVERSEAS_OCCUR = #{overseasOccur,jdbcType=VARCHAR},
        ACCIDENT_AREA = #{accidentArea,jdbcType=VARCHAR},
        PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
        ACCIDENT_NAME = #{accidentName,jdbcType=VARCHAR},
        OVERSEA_NATION_CODE = #{overseaNationCode,jdbcType=VARCHAR}
        where ID_CLM_REPORT_ACCIDENT = #{idClmReportAccident,jdbcType=VARCHAR}
    </update>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLM_REPORT_ACCIDENT (ID_CLM_REPORT_ACCIDENT, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        ACCIDENT_CAUSE_LEVEL1, ACCIDENT_CAUSE_LEVEL2,
        ACCIDENT_CAUSE_LEVEL3, ACCIDENT_DETAIL, ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE, ACCIDENT_PLACE, ACCIDENT_DATE,
        ACCIDENT_TYPE, ACCIDENT_TYPE_DETAIL, ACCIDENT_RESPONSIBILITY,
        IS_THIRD_NOT_FOUND, IS_VIOLATION_LOADING, IS_TRAVEL_ACROSS_REGION,
        IS_FIRST_CARGO_LOSS, IS_DRIVER_INJURED, IS_PANSSENGE_INJURED,
        IS_THI_CAR_LOSS, IS_IN_THI_CAR_INJURED, IS_OUT_THI_CAR_INJURED,
        IS_IN_THI_CARGO_LOSS, IS_OUT_THI_CARGO_LOSS, MIGRATE_FROM,
        IS_CAN_DRIVING, ACCIDENT_TOWN_CODE, ACCIDENT_VILLAGE_CODE,
        IS_ON_FREEWAY, INJURED_NUMBER, DEAT_TOLL,
        ONE_CONDITION_FLAG, DAMAGE_CLIMATE, DISPOSE_STYLE,
        OCCURRENCE_AREA, REASON, ACCIDENT_PLACE_STRING,
        SHANGHAI_ROAD_NAME, SHANGHAI_ROAD_ID, ACCIDENT_PLACE_CODE,
        ACCIDENT_MILEAGE, ACCIDENT_PLACE_GPS_X, ACCIDENT_PLACE_GPS_Y,
        IS_DUTY_CLEAR, OVERSEAS_OCCUR, ACCIDENT_AREA,
        PROVINCE_CODE,ACCIDENT_NAME,OVERSEA_NATION_CODE,ARCHIVE_DATE)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idClmReportAccident,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP}, #{item.reportNo,jdbcType=VARCHAR},
            #{item.accidentCauseLevel1,jdbcType=VARCHAR}, #{item.accidentCauseLevel2,jdbcType=VARCHAR},
            #{item.accidentCauseLevel3,jdbcType=VARCHAR}, #{item.accidentDetail,jdbcType=VARCHAR},
            #{item.accidentCityCode,jdbcType=VARCHAR},
            #{item.accidentCountyCode,jdbcType=VARCHAR}, #{item.accidentPlace,jdbcType=VARCHAR}, #{item.accidentDate,jdbcType=TIMESTAMP},
            #{item.accidentType,jdbcType=VARCHAR}, #{item.accidentTypeDetail,jdbcType=VARCHAR},
            #{item.accidentResponsibility,jdbcType=VARCHAR},
            #{item.isThirdNotFound,jdbcType=VARCHAR}, #{item.isViolationLoading,jdbcType=VARCHAR},
            #{item.isTravelAcrossRegion,jdbcType=VARCHAR},
            #{item.isFirstCargoLoss,jdbcType=VARCHAR}, #{item.isDriverInjured,jdbcType=VARCHAR},
            #{item.isPanssengeInjured,jdbcType=VARCHAR},
            #{item.isThiCarLoss,jdbcType=VARCHAR}, #{item.isInThiCarInjured,jdbcType=VARCHAR}, #{item.isOutThiCarInjured,jdbcType=VARCHAR},
            #{item.isInThiCargoLoss,jdbcType=VARCHAR}, #{item.isOutThiCargoLoss,jdbcType=VARCHAR}, #{item.migrateFrom,jdbcType=VARCHAR},
            #{item.isCanDriving,jdbcType=VARCHAR}, #{item.accidentTownCode,jdbcType=VARCHAR}, #{item.accidentVillageCode,jdbcType=VARCHAR},
            #{item.isOnFreeway,jdbcType=VARCHAR}, #{item.injuredNumber,jdbcType=DECIMAL}, #{item.deatToll,jdbcType=DECIMAL},
            #{item.oneConditionFlag,jdbcType=VARCHAR}, #{item.damageClimate,jdbcType=VARCHAR}, #{item.disposeStyle,jdbcType=VARCHAR},
            #{item.occurrenceArea,jdbcType=VARCHAR}, #{item.reason,jdbcType=VARCHAR}, #{item.accidentPlaceString,jdbcType=VARCHAR},
            #{item.shanghaiRoadName,jdbcType=VARCHAR}, #{item.shanghaiRoadId,jdbcType=VARCHAR}, #{item.accidentPlaceCode,jdbcType=VARCHAR},
            #{item.accidentMileage,jdbcType=DECIMAL}, #{item.accidentPlaceGpsX,jdbcType=VARCHAR},
            #{item.accidentPlaceGpsY,jdbcType=VARCHAR},
            #{item.isDutyClear,jdbcType=VARCHAR}, #{item.overseasOccur,jdbcType=VARCHAR}, #{item.accidentArea,jdbcType=VARCHAR},
            #{item.provinceCode,jdbcType=VARCHAR},#{item.accidentName,jdbcType=VARCHAR},#{item.overseaNationCode,jdbcType=VARCHAR},now())
        </foreach>
    </insert>
    <select id="getAccidentReportInfo1" parameterType="com.paic.ncbs.claim.model.dto.report.QueryAccidentVo" resultType="java.lang.String" >
        select distinct a.report_no
        from clm_report_accident a,clms_policy_claim_case b,clms_case_process  c ,clm_plan_duty_pay d
        where
         b.report_no=a.report_no and a.report_no=c.report_no and c.PROCESS_STATUS='05'
        and d.case_no=b.case_no
        and b.POLICY_NO=#{policyNo}
        <if test="dutyCode != null and dutyCode != '' ">
        and duty_code=#{dutyCode}
        </if>
        and d.DUTY_PAY_AMOUNT>0
        and a.ACCIDENT_DATE <![CDATA[<=]]> #{endDate}
        and a.ACCIDENT_DATE<![CDATA[>=]]>#{startDate}
        and EXISTS(
        select 1 from CLMS_VERIFY_CONCLUSION dd where dd.REPORT_NO=b.REPORT_NO and
        dd.TASK_CODE='checkDuty' and dd.IS_EFFECTIVE='Y' and 	CONLUSION_CAUSE_CODE is null )

    </select>
    <select id="getAccidentReportInfo" parameterType="com.paic.ncbs.claim.model.dto.report.QueryAccidentVo" resultType="java.lang.String">
        select distinct temp.REPORT_NO from (select  a.report_no report_no,b.CASE_TIMES CASE_TIMES,b.CASE_NO CASE_NO
        from clm_report_accident a,clm_case_base b
        where
        a.REPORT_NO=b.REPORT_NO and b.POLICY_NO=#{policyNo}
        and a.ACCIDENT_DATE <![CDATA[<=]]> #{endDate}
        and a.ACCIDENT_DATE <![CDATA[>=]]> #{startDate}) temp,
        clms_case_process cl,clm_plan_duty_pay cdp
        where temp.report_no=cl.REPORT_NO and temp.CASE_TIMES=cl.CASE_TIMES and cl.PROCESS_STATUS='05'
        and temp.CASE_NO=cdp.CASE_NO and temp.CASE_TIMES=cdp.CASE_TIMES
        <if test="dutyCode != null and dutyCode != '' ">
        and cdp.duty_code=#{dutyCode}
        </if>
        and cdp.DUTY_PAY_AMOUNT>0
    </select>
    <update id="updateAccidentReasonByReportNo">
        update clm_report_accident
        set ACCIDENT_CAUSE_LEVEL1 = #{accidentCauseLevel1,jdbcType=VARCHAR},
        ACCIDENT_CAUSE_LEVEL2 = #{accidentCauseLevel2,jdbcType=VARCHAR},
        UPDATED_BY = #{loginUm,jdbcType=VARCHAR},
        UPDATED_DATE = now()
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>
</mapper>