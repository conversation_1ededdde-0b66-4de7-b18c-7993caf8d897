<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO" id="result">
        <id column="ID_AHCS_PERSON_ACCIDENT" property="personAccidentId"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="OVERSEAS_OCCUR" property="overseasOccur"/>
        <result column="ACCIDENT_AREA" property="accidentArea"/>
        <result column="PROVINCE_CODE" property="provinceCode"/>
        <result column="ACCIDENT_CITY_CODE" property="accidentCityCode"/>
        <result column="ACCIDENT_COUNTY_CODE" property="accidentCountyCode"/>
        <result column="ACCIDENT_PLACE" property="accidentPlace"/>
        <result column="INJURY_MECHANISM" property="injuryMechanism"/>
        <result column="INJURY_MECHANISM_DESC" property="injuryMechanismDesc"/>
        <result column="TRAFFIC_TOOL" property="trafficTool"/>
        <result column="CAR_TYPE" property="carType"/>
        <result column="TRAFFIC_ACCIDENT_TYPE" property="trafficAccidentType"/>
        <result column="INSURED_IDENTITY" property="insuredIdentity"/>
        <result column="TRAFFIC_ACCIDENT_NATURE" property="trafficAccidentNature"/>
        <result column="DETAIL_PLACE" property="detailPlace"/>
        <result column="DETAIL_PLACE_DESC" property="detailPlaceDesc"/>
        <result column="PROFESSION_CODE" property="professionCode"/>
        <result column="SUB_PROFESSION_CODE" property="subProfessionCode"/>
        <result column="AMOUNT_RATE" property="amountRate"/>
        <result column="ACCIDENT_TIME" property="accidentTime"/>
        <result column="ACCIDENT_DETAIL" property="accidentDetail"/>
        <result column="INSURED_APPLY_STATUS" property="insuredApplyStatus"/>
        <result column="ACCIDENT_TYPE" property="accidentType"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="HUGE_ACCIDENT_TYPE" property="hugeAccidentType"/>
        <result column="IS_HUGE_ACCIDENT" property="isHugeAccident"/>
        <result column="ID_HUGE_ACCIDENT_INFO" property="idHugeAccidentInfo"/>
        <result column="HIGH_FAIL_TYPE" property="highFailType"/>
        <result column="WORK_INJURY_TYPE" property="workInjuryType"/>
        <result column="IS_CONSTRUCTION" property="isConstruction"/>
        <result column="IS_FULL_AMOUNT" property="isFullAmount"/>
        <result column="CONSTRUCTION_PAY_RATE" property="constructionPayRate"/>
        <result column="IS_POLICY_AREA_ACCIDENT" property="isPolicyAreaAccident"/>
        <result column="IS_SPECIAL_WORK" property="isSpecialWork"/>
        <result column="SPECIAL_WORK_PAPER_NEED" property="specialWorkPaperNeed"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="OVERSEA_NATION_CODE" property="accidentNation"/>
        <result column="HUGE_ACCIDENT_CODE" property="hugeAccidentCode"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.settle.SettleAccidentVO" id="result1">
        <result column="injuryMechanism" property="injuryMechanism"/>
        <result column="profession" property="profession"/>
        <result column="workInjuryTypeName" property="workInjuryTypeName"/>
        <result column="amountRate" property="amountRate"/>
        <result column="isConstruction" property="isConstruction"/>
        <result column="isFullAmount" property="isFullAmount"/>
        <result column="constructionPayRate" property="constructionPayRate"/>
    </resultMap>

    <insert id="savePersonAccident" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO">
        insert into CLMS_person_accident
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_PERSON_ACCIDENT,
        ID_AHCS_CHANNEL_PROCESS,
        OVERSEAS_OCCUR,
        ACCIDENT_AREA,
        PROVINCE_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        INJURY_MECHANISM,
        INJURY_MECHANISM_DESC,
        TRAFFIC_TOOL,
        CAR_TYPE,
        TRAFFIC_ACCIDENT_TYPE,
        INSURED_IDENTITY,
        TRAFFIC_ACCIDENT_NATURE,
        DETAIL_PLACE,
        DETAIL_PLACE_DESC,
        PROFESSION_CODE,
        SUB_PROFESSION_CODE,
        AMOUNT_RATE,
        ACCIDENT_TIME,
        ACCIDENT_DETAIL,
        INSURED_APPLY_STATUS,
        ACCIDENT_TYPE,
        TASK_ID,
        STATUS,
        IS_HUGE_ACCIDENT,
        ID_HUGE_ACCIDENT_INFO,
        HUGE_ACCIDENT_TYPE,
        HIGH_FAIL_TYPE,
        WORK_INJURY_TYPE,
        IS_CONSTRUCTION,
        IS_FULL_AMOUNT,
        CONSTRUCTION_PAY_RATE,
        IS_POLICY_AREA_ACCIDENT,
        IS_SPECIAL_WORK,
        SPECIAL_WORK_PAPER_NEED,
        ARCHIVE_TIME,
        IS_EFFECTIVE,
        OVERSEA_NATION_CODE,
        HUGE_ACCIDENT_CODE
        )
        values
        (#{createdBy},
        SYSDATE(),
        #{createdBy},
        SYSDATE(),
        #{reportNo},
        #{caseTimes},
        #{personAccidentId},
        #{idAhcsChannelProcess},
        #{overseasOccur},
        #{accidentArea},
        #{provinceCode},
        #{accidentCityCode},
        #{accidentCountyCode},
        #{accidentPlace},
        #{injuryMechanism},
        #{injuryMechanismDesc},
        #{trafficTool},
        #{carType},
        #{trafficAccidentType},
        #{insuredIdentity},
        #{trafficAccidentNature},
        #{detailPlace},
        #{detailPlaceDesc},
        #{professionCode},
        #{subProfessionCode},
        #{amountRate},
        #{accidentTime},
        #{accidentDetail},
        #{insuredApplyStatus},
        #{accidentType},
        #{taskId},
        #{status},
        #{isHugeAccident},
        #{idHugeAccidentInfo},
        #{hugeAccidentType},
        #{highFailType},
        #{workInjuryType},
        #{isConstruction},
        #{isFullAmount},
        #{constructionPayRate},
        #{isPolicyAreaAccident},
        #{isSpecialWork},
        #{specialWorkPaperNeed},
        <if test="archiveTime != null ">
            #{archiveTime},
        </if>
        <if test="archiveTime == null ">
            SYSDATE(),
        </if>
        'Y',
        #{accidentNation},
        #{hugeAccidentCode}
        )
    </insert>

    <delete id="removePersonAccident">
        delete from CLMS_person_accident where REPORT_NO=#{reportNo} and case_Times =#{caseTimes}
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO">
        UPDATE
        CLMS_PERSON_ACCIDENT
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 根据通道号、环节号获取最新环节的意外信息 -->
    <select id="getPersonAccident" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_ACCIDENT,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.OVERSEAS_OCCUR,
        t.ACCIDENT_AREA,
        t.PROVINCE_CODE,
        t.ACCIDENT_CITY_CODE,
        t.ACCIDENT_COUNTY_CODE,
        t.ACCIDENT_PLACE,
        t.INJURY_MECHANISM,
        t.INJURY_MECHANISM_DESC,
        t.TRAFFIC_TOOL,
        t.CAR_TYPE,
        t.TRAFFIC_ACCIDENT_TYPE,
        t.INSURED_IDENTITY,
        t.TRAFFIC_ACCIDENT_NATURE,
        t.DETAIL_PLACE,
        t.DETAIL_PLACE_DESC,
        t.PROFESSION_CODE,
        t.SUB_PROFESSION_CODE,
        t.AMOUNT_RATE,
        t.ACCIDENT_TIME,
        t.ACCIDENT_DETAIL,
        t.INSURED_APPLY_STATUS,
        t.ACCIDENT_TYPE,
        t.TASK_ID,
        t.STATUS,
        t.IS_HUGE_ACCIDENT,
        t. ID_HUGE_ACCIDENT_INFO,
        t.HUGE_ACCIDENT_TYPE,
        t.HIGH_FAIL_TYPE,
        t.WORK_INJURY_TYPE,
        t.IS_CONSTRUCTION,
        t.IS_FULL_AMOUNT,
        t.CONSTRUCTION_PAY_RATE,
        t.IS_POLICY_AREA_ACCIDENT,
        t.IS_SPECIAL_WORK,
        t.SPECIAL_WORK_PAPER_NEED,
        t.OVERSEA_NATION_CODE,
        t.HUGE_ACCIDENT_CODE
        from CLMS_person_accident t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_accident t1 where
        t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getPersonAccidentByReportNo" resultMap="result">
        select t.ID_AHCS_PERSON_ACCIDENT,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.OVERSEAS_OCCUR,
        t.ACCIDENT_AREA,
        t.PROVINCE_CODE,
        t.ACCIDENT_CITY_CODE,
        t.ACCIDENT_COUNTY_CODE,
        t.ACCIDENT_PLACE,
        t.INJURY_MECHANISM,
        t.INJURY_MECHANISM_DESC,
        t.TRAFFIC_TOOL,
        t.CAR_TYPE,
        t.TRAFFIC_ACCIDENT_TYPE,
        t.INSURED_IDENTITY,
        t.TRAFFIC_ACCIDENT_NATURE,
        t.DETAIL_PLACE,
        t.DETAIL_PLACE_DESC,
        t.PROFESSION_CODE,
        t.SUB_PROFESSION_CODE,
        t.AMOUNT_RATE,
        t.ACCIDENT_TIME,
        t.ACCIDENT_DETAIL,
        t.HIGH_FAIL_TYPE,
        t.WORK_INJURY_TYPE,
        t.IS_CONSTRUCTION,
        t.IS_FULL_AMOUNT,
        t.CONSTRUCTION_PAY_RATE,
        t.IS_POLICY_AREA_ACCIDENT,
        t.IS_SPECIAL_WORK,
        t.SPECIAL_WORK_PAPER_NEED,
        t.OVERSEA_NATION_CODE,
        t.IS_HUGE_ACCIDENT,
        t.OVERSEA_NATION_CODE,
        t.HUGE_ACCIDENT_CODE
        from CLMS_person_accident t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_accident t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getSettleAccident" parameterType="string" resultMap="result1">
        select case t.injury_mechanism when
        'IM_2904' then
        (select concat(
        (select t2.value_chinese_name
        from clm_common_parameter t2
        where t2.collection_code = 'AHCS_TRAFFIC_ACC'
        and t2.value_code = t1.TRAFFIC_ACCIDENT_TYPE) , ' ' ,
        (select t2.value_chinese_name
        from clm_common_parameter t2
        where t2.collection_code = 'INSURED_IDENTITY'
        and t2.value_code = t1.INSURED_IDENTITY) , ' ' ,
        (select t2.value_chinese_name
        from clm_common_parameter t2
        where t2.collection_code = 'AHCS_CAR_NATURE'
        and t2.value_code = t1.TRAFFIC_ACCIDENT_NATURE) , ' ' ,
        (select t2.value_chinese_name
        from clm_common_parameter t2
        where t2.collection_code = 'AHCS_TRAFFIC'
        and t2.value_code = t1.TRAFFIC_TOOL)) TRAFFIC_TOOL
        from CLMS_person_accident t1
        where t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        and t1.task_id =
        (select * from
        (select a.task_id from CLMS_person_accident a where
        a.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND a.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and a.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and a.STATUS = #{status}
        </if>
        order by a.created_date desc)
        as temp limit 1
        )
        )
        when 'IM_2912' then ifnull(t.INJURY_MECHANISM_DESC, '其他')
        else t3.value_chinese_name end as injuryMechanism ,
        (select CONCAT(t4.profession_chn_name , ' ' , t5.profession_chn_name , ' ' ,t6.profession_chn_name , ' ' ,
        t7.profession_grade_chn_name)
        from CLMS_profession_define t4,
        CLMS_profession_define t5,
        CLMS_profession_define t6,
        CLMS_profession_grade_def t7
        where t4.PARENT_CODE = '0'
        and   t4.PROFESSION_CODE = t5.PARENT_CODE
        and   t5.profession_code = t.profession_code
        and t6.parent_code = t.profession_code
        and t6.profession_code = t.sub_profession_code
        and t6.profession_grade_code = t7.profession_grade_code) profession,
        replace(replace(replace(t.WORK_INJURY_TYPE, '01', '工伤' ),'02','因公致伤'),',','') workInjuryTypeName,
        t.AMOUNT_RATE amountRate,
        t.IS_CONSTRUCTION isConstruction,
        t.IS_FULL_AMOUNT isFullAmount,
        t.CONSTRUCTION_PAY_RATE constructionPayRate
        from CLMS_person_accident t, clm_common_parameter t3
        where t.injury_mechanism = t3.value_code
        and t3.collection_code = 'AHCS_INJURY_MEC'
        and t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_accident t1 where
        t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>
    <!-- 根据报案号、赔付次数、环节号获取最新环节的意外信息 -->
    <select id="getPersonAccidentInfo" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_ACCIDENT,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.OVERSEAS_OCCUR,
        t.ACCIDENT_AREA,
        t.PROVINCE_CODE,
        t.ACCIDENT_CITY_CODE,
        t.ACCIDENT_COUNTY_CODE,
        t.ACCIDENT_PLACE,
        t.INJURY_MECHANISM,
        t.INJURY_MECHANISM_DESC,
        t.TRAFFIC_TOOL,
        t.CAR_TYPE,
        t.TRAFFIC_ACCIDENT_TYPE,
        t.INSURED_IDENTITY,
        t.TRAFFIC_ACCIDENT_NATURE,
        t.DETAIL_PLACE,
        t.DETAIL_PLACE_DESC,
        t.PROFESSION_CODE,
        t.SUB_PROFESSION_CODE,
        t.AMOUNT_RATE,
        t.ACCIDENT_TIME,
        t.ACCIDENT_DETAIL,
        t.INSURED_APPLY_STATUS,
        t.ACCIDENT_TYPE,
        t.TASK_ID,
        t.STATUS,
        t.HIGH_FAIL_TYPE,
        t.WORK_INJURY_TYPE,
        t.IS_CONSTRUCTION,
        t.IS_FULL_AMOUNT,
        t.CONSTRUCTION_PAY_RATE,
        t.IS_POLICY_AREA_ACCIDENT,
        t.IS_SPECIAL_WORK,
        t.IS_HUGE_ACCIDENT,
        t.ID_HUGE_ACCIDENT_INFO,
        t.HUGE_ACCIDENT_TYPE,
        t.SPECIAL_WORK_PAPER_NEED,
        t.HUGE_ACCIDENT_CODE
        from CLMS_person_accident t
        where t.REPORT_NO = #{reportNo} and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_accident t1 where
        t1.REPORT_NO = #{reportNo} and t1.CASE_TIMES = #{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        ) limit 1  <!-- 如果某个环节任务重做了一次（比如理算），那这条sql就会查到两条同样的记录，为避免报错，这里明确rownum只取一条-->
    </select>

    <!-- 根据通道号、环节号获取意外信息 -->
    <select id="getPersonAccidentDTO" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_ACCIDENT,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.OVERSEAS_OCCUR,
        t.ACCIDENT_AREA,
        t.PROVINCE_CODE,
        t.ACCIDENT_CITY_CODE,
        t.ACCIDENT_COUNTY_CODE,
        t.ACCIDENT_PLACE,
        t.INJURY_MECHANISM,
        t.INJURY_MECHANISM_DESC,
        t.TRAFFIC_TOOL,
        t.CAR_TYPE,
        t.TRAFFIC_ACCIDENT_TYPE,
        t.INSURED_IDENTITY,
        t.TRAFFIC_ACCIDENT_NATURE,
        t.DETAIL_PLACE,
        t.DETAIL_PLACE_DESC,
        t.PROFESSION_CODE,
        t.SUB_PROFESSION_CODE,
        t.AMOUNT_RATE,
        t.ACCIDENT_TIME,
        t.ACCIDENT_DETAIL,
        t.INSURED_APPLY_STATUS,
        t.ACCIDENT_TYPE,
        t.TASK_ID,
        t.STATUS,
        t.IS_HUGE_ACCIDENT,
        t.ID_HUGE_ACCIDENT_INFO,
        t.HUGE_ACCIDENT_TYPE,
        t.HIGH_FAIL_TYPE,
        t.WORK_INJURY_TYPE,
        t.IS_CONSTRUCTION,
        t.IS_FULL_AMOUNT,
        t.CONSTRUCTION_PAY_RATE,
        t.IS_POLICY_AREA_ACCIDENT,
        t.IS_SPECIAL_WORK,
        t.SPECIAL_WORK_PAPER_NEED,
        t.ARCHIVE_TIME,
        t.HUGE_ACCIDENT_CODE
        from CLMS_person_accident t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        and t.STATUS = '1'
        and t.TASK_ID = #{taskId}
        AND t.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 根据报案号、赔付次数获取核责环节之前的意外信息，不包括核责、理算 -->
    <select id="getReportPersonAccidentList" resultMap="result">
        select t.ID_AHCS_PERSON_ACCIDENT,
        t.CREATED_BY,
        t.CREATED_DATE,
        t.UPDATED_BY,
        t.UPDATED_DATE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.OVERSEAS_OCCUR,
        t.ACCIDENT_AREA,
        t.PROVINCE_CODE,
        t.ACCIDENT_CITY_CODE,
        t.ACCIDENT_COUNTY_CODE,
        t.ACCIDENT_PLACE,
        t.INJURY_MECHANISM,
        t.INJURY_MECHANISM_DESC,
        t.TRAFFIC_TOOL,
        t.CAR_TYPE,
        t.TRAFFIC_ACCIDENT_TYPE,
        t.INSURED_IDENTITY,
        t.TRAFFIC_ACCIDENT_NATURE,
        t.DETAIL_PLACE,
        t.DETAIL_PLACE_DESC,
        t.PROFESSION_CODE,
        t.SUB_PROFESSION_CODE,
        t.AMOUNT_RATE,
        t.ACCIDENT_TIME,
        t.ACCIDENT_DETAIL,
        t.INSURED_APPLY_STATUS,
        t.ACCIDENT_TYPE,
        t.TASK_ID,
        t.STATUS,
        t.IS_HUGE_ACCIDENT,
        t.ID_HUGE_ACCIDENT_INFO,
        t.HUGE_ACCIDENT_TYPE,
        t.HIGH_FAIL_TYPE,
        t.WORK_INJURY_TYPE,
        t.IS_CONSTRUCTION,
        t.IS_FULL_AMOUNT,
        t.CONSTRUCTION_PAY_RATE,
        t.IS_POLICY_AREA_ACCIDENT,
        t.IS_SPECIAL_WORK,
        t.SPECIAL_WORK_PAPER_NEED,
        t.HUGE_ACCIDENT_CODE
        from CLMS_person_accident t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        and t.TASK_ID in ('report1','report2','telSurvey','fieldSurvey','reportTrack')
    </select>

    <!-- 根据通道获取核责环节之前的意外信息，不包括核责、理算-->
    <select id="getPersonAccidentListByIdChannel" resultMap="result">
        select t.ID_AHCS_PERSON_ACCIDENT,
        t.CREATED_BY,
        t.CREATED_DATE,
        t.UPDATED_BY,
        t.UPDATED_DATE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.OVERSEAS_OCCUR,
        t.ACCIDENT_AREA,
        t.PROVINCE_CODE,
        t.ACCIDENT_CITY_CODE,
        t.ACCIDENT_COUNTY_CODE,
        t.ACCIDENT_PLACE,
        t.INJURY_MECHANISM,
        t.INJURY_MECHANISM_DESC,
        t.TRAFFIC_TOOL,
        t.CAR_TYPE,
        t.TRAFFIC_ACCIDENT_TYPE,
        t.INSURED_IDENTITY,
        t.TRAFFIC_ACCIDENT_NATURE,
        t.DETAIL_PLACE,
        t.DETAIL_PLACE_DESC,
        t.PROFESSION_CODE,
        t.SUB_PROFESSION_CODE,
        t.AMOUNT_RATE,
        t.ACCIDENT_TIME,
        t.ACCIDENT_DETAIL,
        t.INSURED_APPLY_STATUS,
        t.ACCIDENT_TYPE,
        t.TASK_ID,
        t.STATUS,
        t.IS_HUGE_ACCIDENT,
        t.ID_HUGE_ACCIDENT_INFO,
        t.HUGE_ACCIDENT_TYPE,
        t.HIGH_FAIL_TYPE,
        t.WORK_INJURY_TYPE,
        t.IS_CONSTRUCTION,
        t.IS_FULL_AMOUNT,
        t.CONSTRUCTION_PAY_RATE,
        t.IS_POLICY_AREA_ACCIDENT,
        t.IS_SPECIAL_WORK,
        t.SPECIAL_WORK_PAPER_NEED,
        t.ARCHIVE_TIME,
        t.HUGE_ACCIDENT_CODE
        from CLMS_person_accident t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        and t.TASK_ID in ('report1','report2','telSurvey','fieldSurvey','reportTrack')
        and t.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
    </select>

    <!-- 批量新增意外信息 -->
    <insert id="addPersonAccidentList">
        insert into CLMS_person_accident
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        OVERSEAS_OCCUR,
        ACCIDENT_AREA,
        PROVINCE_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        INJURY_MECHANISM,
        INJURY_MECHANISM_DESC,
        TRAFFIC_TOOL,
        CAR_TYPE,
        TRAFFIC_ACCIDENT_TYPE,
        INSURED_IDENTITY,
        TRAFFIC_ACCIDENT_NATURE,
        DETAIL_PLACE,
        DETAIL_PLACE_DESC,
        PROFESSION_CODE,
        SUB_PROFESSION_CODE,
        AMOUNT_RATE,
        ACCIDENT_TIME,
        ACCIDENT_DETAIL,
        INSURED_APPLY_STATUS,
        ACCIDENT_TYPE,
        TASK_ID,
        STATUS,
        IS_HUGE_ACCIDENT,
        ID_HUGE_ACCIDENT_INFO,
        HUGE_ACCIDENT_TYPE,
        HIGH_FAIL_TYPE,
        WORK_INJURY_TYPE,
        IS_CONSTRUCTION,
        IS_FULL_AMOUNT,
        CONSTRUCTION_PAY_RATE,
        IS_POLICY_AREA_ACCIDENT,
        IS_SPECIAL_WORK,
        SPECIAL_WORK_PAPER_NEED,
        HUGE_ACCIDENT_CODE,
        ARCHIVE_TIME)
        <foreach collection="personAccidentList" index="index" item="item" open="(" close=")" separator="union all">
            select #{item.createdBy},
            #{item.createdDate},
            #{item.updatedBy},
            #{item.updatedDate},
            #{item.reportNo},
            #{caseTimes},
            #{idAhcsChannelProcess},
            #{item.overseasOccur},
            #{item.accidentArea},
            #{item.provinceCode},
            #{item.accidentCityCode},
            #{item.accidentCountyCode},
            #{item.accidentPlace},
            #{item.injuryMechanism},
            #{item.injuryMechanismDesc},
            #{item.trafficTool},
            #{item.carType},
            #{item.trafficAccidentType},
            #{item.insuredIdentity},
            #{item.trafficAccidentNature},
            #{item.detailPlace},
            #{item.detailPlaceDesc},
            #{item.professionCode},
            #{item.subProfessionCode},
            #{item.amountRate},
            #{item.accidentTime},
            #{item.accidentDetail},
            #{item.insuredApplyStatus},
            #{item.accidentType},
            #{item.taskId},
            #{item.status},
            #{item.isHugeAccident},
            #{item.idHugeAccidentInfo},
            #{item.hugeAccidentType},
            #{item.highFailType},
            #{item.workInjuryType},
            #{item.isConstruction},
            #{item.isFullAmount},
            #{item.constructionPayRate},
            #{item.isPolicyAreaAccident},
            #{item.isSpecialWork},
            #{item.specialWorkPaperNeed},
            #{item.hugeAccidentCode},
            <if test="item.archiveTime != null ">
                #{item.archiveTime}
            </if>
            <if test="item.archiveTime == null ">
                SYSDATE()
            </if>
            from DUAL
        </foreach>
    </insert>

    <!-- 根据报案号、赔付次数获取最新环节的致伤机制 -->
    <select id="getInjuryMechanismCode" resultType="string">
        select t.INJURY_MECHANISM
        from CLMS_person_accident t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS = '1'
        AND t.IS_EFFECTIVE = 'Y'
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_accident t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.STATUS = '1'
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 获取事故时间 -->
    <select id="getAccidentTimeByReportNo" resultType="string">
        SELECT date_format(APA.ACCIDENT_TIME,'%Y%m%d') ACCIDENT_TIME
        FROM CLMS_PERSON_ACCIDENT APA
        WHERE APA.REPORT_NO = #{reportNo}
        AND APA.CASE_TIMES = #{caseTimes}
        AND APA.ACCIDENT_TIME is not null
        AND APA.IS_EFFECTIVE = 'Y'
        AND APA.TASK_ID =
        (select * from
        (select t1.TASK_ID from CLMS_PERSON_ACCIDENT t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.ACCIDENT_TIME is not null
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.CREATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_ACCIDENT (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_ACCIDENT,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            OVERSEAS_OCCUR,
            ACCIDENT_AREA,
            PROVINCE_CODE,
            ACCIDENT_CITY_CODE,
            ACCIDENT_COUNTY_CODE,
            ACCIDENT_PLACE,
            INJURY_MECHANISM,
            INJURY_MECHANISM_DESC,
            HIGH_FAIL_TYPE,
            WORK_INJURY_TYPE,
            TRAFFIC_TOOL,
            CAR_TYPE,
            TRAFFIC_ACCIDENT_TYPE,
            INSURED_IDENTITY,
            TRAFFIC_ACCIDENT_NATURE,
            DETAIL_PLACE,
            DETAIL_PLACE_DESC,
            PROFESSION_CODE,
            SUB_PROFESSION_CODE,
            AMOUNT_RATE,
            ACCIDENT_TIME,
            ACCIDENT_DETAIL,
            INSURED_APPLY_STATUS,
            ACCIDENT_TYPE,
            TASK_ID,
            STATUS,
            IS_HUGE_ACCIDENT,
            ID_HUGE_ACCIDENT_INFO,
            HUGE_ACCIDENT_TYPE,
            IS_CONSTRUCTION,
            IS_FULL_AMOUNT,
            CONSTRUCTION_PAY_RATE,
            IS_POLICY_AREA_ACCIDENT,
            IS_SPECIAL_WORK,
            SPECIAL_WORK_PAPER_NEED,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            OVERSEA_NATION_CODE,
            HUGE_ACCIDENT_CODE
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            OVERSEAS_OCCUR,
            ACCIDENT_AREA,
            PROVINCE_CODE,
            ACCIDENT_CITY_CODE,
            ACCIDENT_COUNTY_CODE,
            ACCIDENT_PLACE,
            INJURY_MECHANISM,
            INJURY_MECHANISM_DESC,
            HIGH_FAIL_TYPE,
            WORK_INJURY_TYPE,
            TRAFFIC_TOOL,
            CAR_TYPE,
            TRAFFIC_ACCIDENT_TYPE,
            INSURED_IDENTITY,
            TRAFFIC_ACCIDENT_NATURE,
            DETAIL_PLACE,
            DETAIL_PLACE_DESC,
            PROFESSION_CODE,
            SUB_PROFESSION_CODE,
            AMOUNT_RATE,
            ACCIDENT_TIME,
            ACCIDENT_DETAIL,
            INSURED_APPLY_STATUS,
            ACCIDENT_TYPE,
            TASK_ID,
            STATUS,
            IS_HUGE_ACCIDENT,
            ID_HUGE_ACCIDENT_INFO,
            HUGE_ACCIDENT_TYPE,
            IS_CONSTRUCTION,
            IS_FULL_AMOUNT,
            CONSTRUCTION_PAY_RATE,
            IS_POLICY_AREA_ACCIDENT,
            IS_SPECIAL_WORK,
            SPECIAL_WORK_PAPER_NEED,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            OVERSEA_NATION_CODE,
            HUGE_ACCIDENT_CODE
        FROM CLMS_PERSON_ACCIDENT
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>

    <!-- 根据报案号查询事故时间 -->
    <select id="getPersonAccidentDate" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO" resultMap="result">
      select t.ACCIDENT_TIME,  t.REPORT_NO, t.CASE_TIMES
        from CLMS_person_accident t where
        t.REPORT_NO = #{reportNo}
        and t.STATUS = '1'
        AND t.IS_EFFECTIVE = 'Y'
        AND t.CASE_TIMES=#{caseTimes}
        order by t.created_date desc
    </select>
</mapper>