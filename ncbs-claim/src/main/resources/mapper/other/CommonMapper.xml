<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.CommonMapper">

    <select id="generateIdBySysGuid" resultType="java.lang.String" flushCache="true">
        select
        md5(uuid()) as AHCSId
    </select>

    <select id="getReportSeq" resultType="java.lang.String" useCache="false" flushCache="true">
        select nextval('SEQ_CLM_REPORT_NO')
        <!--select left(md5(uuid()),16)-->
    </select>

    <select id="getCaseSeq" resultType="java.lang.String" useCache="false" flushCache="true">
        select nextval('SEQ_CLM_CASE_NO')
        <!--select left(md5(uuid()),16)-->
    </select>

</mapper>