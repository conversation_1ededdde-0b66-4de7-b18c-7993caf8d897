package com.paic.ncbs.claim.common.constant;

import java.util.*;

public class CaseClassConstant {
    private CaseClassConstant(){}

    private static final String OTHER_INSURANCE = "01";  //01:其他险（如退运险）
    private static final String HEALTH_INSURANCE = "02"; //02:健康险
    private static final String INJURY_INSURANCE = "03"; //03:意健险
    private static final String RESPONSIBILITY_INSURANCE = "04"; //04:责任险
    private static final String HOME_INSURANCE = "05"; //05:家财险
    private static final String ENTERPRISE_INSURANCE = "22"; //22:企财险
    private static final String GUARANTEE_INSURANCE = "23"; //23:保证保险

    public static final Map<String, List<String>> PRODUCT_CLASS_CASE_CLASS_MAP = new HashMap<>();
    static {
        //其他-> 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(OTHER_INSURANCE, Arrays.asList("4","10"));
        //健康险-> 意健险 1 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(HEALTH_INSURANCE,Arrays.asList("1","4","10"));
        //意健险-> 意健险 1 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(INJURY_INSURANCE, Arrays.asList("1","4","10"));
        //责任险-> 责任险 2 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(RESPONSIBILITY_INSURANCE,Arrays.asList("2","4","10"));
        //家财险-> 个人财产 3 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(HOME_INSURANCE,Arrays.asList("3","4","10"));
        //企财险-> 企业财产 5 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(ENTERPRISE_INSURANCE,Arrays.asList("5","4","10"));
        //保证保险-> 保证险 6 其他 4
        PRODUCT_CLASS_CASE_CLASS_MAP.put(GUARANTEE_INSURANCE, Arrays.asList("6","4","10"));

    }
}
