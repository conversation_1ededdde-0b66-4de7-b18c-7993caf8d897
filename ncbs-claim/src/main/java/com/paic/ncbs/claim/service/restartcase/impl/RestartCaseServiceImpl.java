package com.paic.ncbs.claim.service.restartcase.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.doc.PrintMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper;
import com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentDutyMapper;
import com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.report.RestartCaseRecordDTO;
import com.paic.ncbs.claim.model.dto.restartcase.ApprovalProcessDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePage;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePageResult;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.restartcase.RestartCaseVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimUpdateDocumentFullDateService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.restartcase.CaseReopenCopyService;
import com.paic.ncbs.claim.service.restartcase.RestartCaseService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.taskdeal.ITaskInfoPlusService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-05-19 14:02
 */
@Service
public class RestartCaseServiceImpl implements RestartCaseService {

    @Autowired
    private PrintMapper printMapper;

    @Autowired
    private TaskListService taskListService;

    @Autowired
    private RestartCaseRecordMapper restartCaseRecordMapper;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private RegisterCaseService registerCaseService;

    @Autowired
    private WholeCaseMapper wholeCaseMapper;

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private ClmsEstimateRecordService clmsEstimateRecordService;

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordDao;

    @Autowired
    private CaseReopenCopyService caseReopenCopyService;

    @Autowired
    AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    PolicyPayService policyPayService;

    @Autowired
    CaseProcessService caseProcessService;

    @Autowired
    DepartmentDefineService departmentDefineService;

    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private RestartCaseService restartCaseService;

    @Autowired
    private CaseClassMapper caseClassDao;

    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private ClaimUpdateDocumentFullDateService claimUpdateDocumentFullDateService;

    @Autowired
    private ClmsPaymentDutyMapper paymentDutyMapper;

    @Autowired
    private CoinsureService coinsureService;

    @Autowired
    private MaxPayService maxPayService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;

    @Autowired
    private ITaskInfoPlusService taskInfoPlusService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private NoticeService noticeService;

    @Override
    public WholeCasePageResult getHistoryCaseList(WholeCaseVO wholeCaseVO) throws GlobalBusinessException {
        long startTime = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(wholeCaseVO.getReportNo())
                || StringUtils.isNotEmpty(wholeCaseVO.getPolicyNo())
                || StringUtils.isNotEmpty(wholeCaseVO.getCaseNo())
                || StringUtils.isNotEmpty(wholeCaseVO.getReportBatchNo())
        ) {
            LogUtil.audit("#获取结案信息#入参# reportNo={},CaseNo={},reportBatchNo={},policyNo={}", wholeCaseVO.getReportNo(), wholeCaseVO.getCaseNo(), wholeCaseVO.getReportBatchNo(), wholeCaseVO.getPolicyNo());
            //获取数据
            WholeCasePageResult pageResult = this.getHistoryCasePage(wholeCaseVO);
            LogUtil.audit("查询案件信息列表耗时A=" + (System.currentTimeMillis() - startTime) + "入参:" + JSONObject.toJSONString(wholeCaseVO));
            if (pageResult == null) {
                LogUtil.info("未查询到相关信息，请核实");
                throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "未查询到相关信息，请核实");
            }
            return pageResult;
        } else {
            LogUtil.info("查询条件为空");
            throw new GlobalBusinessException(ErrorCode.Print.PRINT_MISS_PARAM, "查询条件为空");
        }
    }

    @Transactional
    @Override
    public void addRestartCase(RestartCaseVO restartCaseVO) throws GlobalBusinessException{
        LogUtil.audit("案件号{}，案件重开申请入参{}",restartCaseVO.getReportNo(),JSONObject.toJSONString(restartCaseVO));
        //因注释了前端重开详情信息相关逻辑导致入参传入缺少，现需再次调用查询方法，对该方法的入参进行补充
        EstimatePolicyFormDTO result = getRestartCaseDetail(restartCaseVO.getReportNo(),restartCaseVO.getCaseTimes());
        restartCaseVO.setEstimatePolicyList(result.getEstimatePolicyList());
        List<RestartCaseRecordEntity> restartCaseRecordEntities = restartCaseRecordMapper.getRestartCaseListByApproval(restartCaseVO.getReportNo(),restartCaseVO.getCaseTimes());
        if(restartCaseRecordEntities != null && restartCaseRecordEntities.size() > 0){
            throw new GlobalBusinessException("禁止重复提交");
        }
        if(riskPropertyService.displayRiskProperty(restartCaseVO.getReportNo(),null)){
            throw new GlobalBusinessException("责任险暂不支持重开");
        }
        //报案注销案件不允许重开
        WholeCaseVO wholeCaseVO = restartCaseRecordMapper.getCaseByReportNoAndCaseTimes(restartCaseVO.getReportNo(),restartCaseVO.getCaseTimes());
        if(ConfigConstValues.INDEMNITYCONCLUSION_CANCEL_REPORT.equals(wholeCaseVO.getIndemnityConclusion())){
            throw new GlobalBusinessException("报案注销案件不支持重开");
        }
        Long count = taskInfoPlusService.lambdaQuery().eq(TaskInfoDTO::getReportNo, restartCaseVO.getReportNo())
                .eq(TaskInfoDTO::getCaseTimes, restartCaseVO.getCaseTimes())
                .eq(TaskInfoDTO::getTaskDefinitionBpmKey, BpmConstants.OC_RESTART_CASE_MODIFY)
                .eq(TaskInfoDTO::getStatus, "0").count();
        if(count > 0){
            throw new GlobalBusinessException("存在在途的重开修改任务，不允许再次发起重开");
        }
        List<PaymentPlanDutyDTO> paymentDutyDTOList = paymentDutyMapper.getPaymentDutyByReportNo(restartCaseVO.getReportNo(), restartCaseVO.getCaseTimes());
        Map<String, BigDecimal> oldPlanDutyCodeAmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(paymentDutyDTOList)){
            for (PaymentPlanDutyDTO paymentPlanDutyDTO: paymentDutyDTOList){
                if (paymentPlanDutyDTO.getPaymentType().equals("1J")){
                    //目前不计算费用变化量
                    continue;
                }
                String key = paymentPlanDutyDTO.getPlanCode() + paymentPlanDutyDTO.getDutyCode();
                if (oldPlanDutyCodeAmountMap.containsKey(key)){
                    oldPlanDutyCodeAmountMap.put(key, oldPlanDutyCodeAmountMap.get(key).add(paymentPlanDutyDTO.getDutyPayAmount()));
                }else {
                    oldPlanDutyCodeAmountMap.put(key, paymentPlanDutyDTO.getDutyPayAmount());
                }
            }
           // oldPlanDutyCodeAmountMap = paymentDutyDTOList.stream().collect(Collectors.toMap(k -> (k.getPlanCode() + k.getDutyCode()), v -> v.getDutyPayAmount()));
        }

        //将重开申请填入的金额数据入库clms_estimate_duty_record表，ESTIMATE_TYPE 未决类型  增加一个05重开未决
        List<EstimateDutyRecordDTO> list = getEstimateDutyRecordList(restartCaseVO.getEstimatePolicyList(),oldPlanDutyCodeAmountMap,restartCaseVO.getReportNo());
        //判断前次赔款立案总金额 <= 重开赔款立案金额
        BigDecimal newAmount = list.stream().filter(r-> !BigDecimalUtils.isNullOrZero(r.getEstimateAmount())).map(EstimateDutyRecordDTO::getEstimateAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal lastAmount = list.stream().filter(r-> !BigDecimalUtils.isNullOrZero(r.getLastPayAmount())).map(EstimateDutyRecordDTO::getLastPayAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        if(BigDecimalUtils.compareBigDecimalPlus(lastAmount,newAmount)){
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,"重开赔款立案金额不能小于前次理赔金额" + lastAmount);
        }
        restartCaseVO.setRestartAmount(lastAmount);
        RestartCaseRecordEntity restartCaseRecordEntity = buildPO(restartCaseVO);
        LogUtil.audit("addRestartCase插入数据入参{}",JSONObject.toJSONString(restartCaseRecordEntity));
        restartCaseRecordMapper.insert(restartCaseRecordEntity);
        ahcsCommonService.batchProcess(estimateDutyRecordDao::addEstimateDutyRecordList, list);
        //操作记录
        operationRecordService.insertOperationRecord(restartCaseVO.getReportNo(), BpmConstants.OC_RESTART_CASE_APPROVAL, "发起", null, WebServletContext.getUserIdForLog());
        //创建重开审批工作流任务,将clm_restart_case_record主键作为taskid带入
        bpmService.startProcessOc(restartCaseVO.getReportNo(), restartCaseVO.getCaseTimes(), BpmConstants.OC_RESTART_CASE_APPROVAL,restartCaseRecordEntity.getIdClmRestartCaseRecord(),null,null);

    }

    @Transactional
    @Override
    public void updateRestartCase(RestartCaseVO restartCaseVO) throws GlobalBusinessException{
        //因注释了前端重开详情信息相关逻辑导致入参传入缺少，现需再次调用查询方法，对该方法的入参进行补充
        EstimatePolicyFormDTO result = getRestartCaseDetail(restartCaseVO.getReportNo(),restartCaseVO.getCaseTimes());
        restartCaseVO.setEstimatePolicyList(result.getEstimatePolicyList());
        LogUtil.audit("案件号{}，案件重开修改申请入参{}",restartCaseVO.getReportNo(),JSONObject.toJSONString(restartCaseVO));
        //完成当前任务
        bpmService.completeTask_oc(restartCaseVO.getReportNo(), restartCaseVO.getCaseTimes()
        , BpmConstants.OC_RESTART_CASE_MODIFY,restartCaseVO.getIdClmRestartCaseRecord());
        List<RestartCaseRecordEntity> restartCaseRecordEntities = restartCaseRecordMapper.getRestartApprovalReturnList(restartCaseVO.getReportNo(),restartCaseVO.getCaseTimes());
        if(restartCaseRecordEntities.isEmpty()){
            throw new GlobalBusinessException("无重开修改案件记录!");
        }
        List<PaymentPlanDutyDTO> paymentDutyDTOList = paymentDutyMapper.getPaymentDutyByReportNo(restartCaseVO.getReportNo(), restartCaseVO.getCaseTimes());
        Map<String, BigDecimal> oldPlanDutyCodeAmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(paymentDutyDTOList)){
            for (PaymentPlanDutyDTO paymentPlanDutyDTO: paymentDutyDTOList){
                if (paymentPlanDutyDTO.getPaymentType().equals("1J")){
                    //目前不计算费用变化量
                    continue;
                }
                String key = paymentPlanDutyDTO.getPlanCode() + paymentPlanDutyDTO.getDutyCode();
                if (oldPlanDutyCodeAmountMap.containsKey(key)){
                    oldPlanDutyCodeAmountMap.put(key, oldPlanDutyCodeAmountMap.get(key).add(paymentPlanDutyDTO.getDutyPayAmount()));
                }else {
                    oldPlanDutyCodeAmountMap.put(key, paymentPlanDutyDTO.getDutyPayAmount());
                }
            }
            // oldPlanDutyCodeAmountMap = paymentDutyDTOList.stream().collect(Collectors.toMap(k -> (k.getPlanCode() + k.getDutyCode()), v -> v.getDutyPayAmount()));
        }

        //将重开申请填入的金额数据入库clms_estimate_duty_record表，ESTIMATE_TYPE 未决类型  增加一个05重开未决
        List<EstimateDutyRecordDTO> list = getEstimateDutyRecordList(restartCaseVO.getEstimatePolicyList(),oldPlanDutyCodeAmountMap,restartCaseVO.getReportNo());
        //判断前次赔款立案总金额 <= 重开赔款立案金额
        BigDecimal newAmount = list.stream().filter(r-> !BigDecimalUtils.isNullOrZero(r.getEstimateAmount())).map(EstimateDutyRecordDTO::getEstimateAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal lastAmount = list.stream().filter(r-> !BigDecimalUtils.isNullOrZero(r.getLastPayAmount())).map(EstimateDutyRecordDTO::getLastPayAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        if(BigDecimalUtils.compareBigDecimalPlus(lastAmount,newAmount)){
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,"重开赔款立案金额不能小于前次理赔金额" + lastAmount);
        }
        restartCaseVO.setRestartAmount(lastAmount);
        RestartCaseRecordEntity restartCaseRecordEntity = buildPO(restartCaseVO);
        //需要将clms_estimate_duty_record表中的数据置为无效 再插入新的数据
        restartCaseRecordMapper.invalidRestartCase(restartCaseVO.getReportNo(), restartCaseVO.getCaseTimes());
        LogUtil.audit("updateRestartCase修改数据入参{}",JSONObject.toJSONString(restartCaseRecordEntity));
        restartCaseRecordMapper.insert(restartCaseRecordEntity);
        ahcsCommonService.batchProcess(estimateDutyRecordDao::addEstimateDutyRecordList, list);
        //创建重开审批工作流任务,将clm_restart_case_record主键作为taskid带入
        bpmService.startProcessOc(restartCaseVO.getReportNo(), restartCaseVO.getCaseTimes(), BpmConstants.OC_RESTART_CASE_APPROVAL,restartCaseRecordEntity.getIdClmRestartCaseRecord(),null,null);
        //操作记录
        operationRecordService.insertOperationRecord(restartCaseVO.getReportNo(), BpmConstants.OC_RESTART_CASE_MODIFY, "提交", null, WebServletContext.getUserIdForLog());
    }

    @Override
    public List<RestartCaseRecordDTO> getRestratCases(String reportNo, Integer caseTimes){
        List<RestartCaseRecordEntity> getRestartCaseList =getRestartCaseList(reportNo,caseTimes);
        List<RestartCaseRecordDTO> dtoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(getRestartCaseList)){
            for (RestartCaseRecordEntity entity :getRestartCaseList) {
                RestartCaseRecordDTO dto = new RestartCaseRecordDTO();
                BeanUtils.copyProperties(entity,dto);
                if(StrUtil.isNotEmpty(entity.getCaseKind())){
                    List<String> stringList = StringUtils.getListWithSeparator(entity.getCaseKind(),Constants.SEPARATOR);
                    dto.setCaseKind(stringList);
                }
                dtoList.add(dto);

            }
        }
        return dtoList;
    }

    @Override
    public List<RestartCaseRecordDTO> getThisRestratCases(String reportNo, Integer caseTimes){
        List<RestartCaseRecordEntity> getRestartCaseList =getThisRestartCaseList(reportNo,caseTimes);
        List<RestartCaseRecordDTO> dtoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(getRestartCaseList)){
            for (RestartCaseRecordEntity entity :getRestartCaseList) {
                RestartCaseRecordDTO dto = new RestartCaseRecordDTO();
                BeanUtils.copyProperties(entity,dto);
                if(StrUtil.isNotEmpty(entity.getCaseKind())){
                    List<String> stringList = StringUtils.getListWithSeparator(entity.getCaseKind(),Constants.SEPARATOR);
                    dto.setCaseKind(stringList);
                }
                dtoList.add(dto);

            }
        }
        return dtoList;
    }

    @Override
    public List<RestartCaseRecordEntity> getThisRestartCaseList(String reportNo, Integer caseTimes) {
        return restartCaseRecordMapper.getThisRestartCaseList(reportNo, caseTimes);
    }
    @Override
    public List<RestartCaseRecordEntity> getRestartCaseList(String reportNo, Integer caseTimes) {
        if (caseTimes == null) {
            return restartCaseRecordMapper.getRestartCaseList(reportNo, null);
        } else {
            return restartCaseRecordMapper.getRestartCaseListForDetail(reportNo, caseTimes);
        }
    }

    @Transactional
    @Override
    public void approvalProcess(ApprovalProcessDTO approvalProcessDTO) {
        LogUtil.audit("RestartCaseServiceImpl.approvalProcess start");
        //判断审批结果，通过是否同意来做下一步操作
        //不管同不同意，上个工作流任务都要结束掉
        bpmService.completeTask_oc(approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes()
                , BpmConstants.OC_RESTART_CASE_APPROVAL,approvalProcessDTO.getIdClmRestartCaseRecord());
        //审批结果 0：同意 1：不同意
        if (approvalProcessDTO.getIsAgree() == 0) {
            LogUtil.audit("RestartCaseServiceImpl.approvalProcess IsAgree 0");
            //表数据复制等操作
            caseReopenCopyService.copyForCaseReopen(approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes(), approvalProcessDTO.getIdClmRestartCaseRecord());
            //案规则分派任务，并生成工作流
            bpmService.startProcess_rc(approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes(), true, null,null);
            //之前数据 重开标记字段置为0
            updateIsNewest(approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes());

            //重开审批后插入监管估损中间表
            estimateService.saveEstimateIntermediateData(WebServletContext.getUserId(), approvalProcessDTO.getReportNo()
                    , approvalProcessDTO.getCaseTimes(), EstimateUtil.ESTIMATE_TYPE_RESTART);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 重开，发送再保
                    RepayCalDTO repayCalDTO = new RepayCalDTO();
                    repayCalDTO.setReportNo(approvalProcessDTO.getReportNo());
                    repayCalDTO.setCaseTimes(approvalProcessDTO.getCaseTimes() + 1);
                    repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.CASE_REOPEN);
                    reinsuranceService.sendReinsurance(repayCalDTO);
                }
            });
            //操作记录
            operationRecordService.insertOperationRecord(approvalProcessDTO.getReportNo(), BpmConstants.OC_RESTART_CASE_APPROVAL, "通过", approvalProcessDTO.getApprovalExplain(), WebServletContext.getUserIdForLog());
        } else {
            LogUtil.audit("RestartCaseServiceImpl.approvalProcess IsAgree 1");
            //案规则分派任务，并生成工作流
            bpmService.startProcess_rc(approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes(), true, BpmConstants.OC_RESTART_CASE_MODIFY,null);
            //操作记录
            operationRecordService.insertOperationRecord(approvalProcessDTO.getReportNo(), BpmConstants.OC_RESTART_CASE_APPROVAL, "不通过", approvalProcessDTO.getApprovalExplain(), WebServletContext.getUserIdForLog());
            //重开赔案审批调查退回时添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_RESTART_CASE_APPROVAL);
            noticesDTO.setReportNo(approvalProcessDTO.getReportNo());
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(approvalProcessDTO.getCaseTimes());
            TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes(),
                    BpmConstants.OC_RESTART_CASE_MODIFY);
            if (taskInfoDTO != null){
                noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
            }

        }
        LogUtil.audit("RestartCaseServiceImpl.approvalProcess 结果入库操作");
        //重开审批结果入库操作
        RestartCaseRecordEntity restartCaseRecordEntity = new RestartCaseRecordEntity();
        restartCaseRecordEntity.setIdClmRestartCaseRecord(approvalProcessDTO.getIdClmRestartCaseRecord());
        restartCaseRecordEntity.setApprovalOpinions(approvalProcessDTO.getIsAgree());
        restartCaseRecordEntity.setApprovalDescription(approvalProcessDTO.getApprovalExplain());
        restartCaseRecordEntity.setUpdatedBy(WebServletContext.getUserId());
        restartCaseRecordEntity.setErrorType(approvalProcessDTO.getErrorType());
        restartCaseRecordMapper.saveApprovalProcess(restartCaseRecordEntity);
        LogUtil.audit("RestartCaseServiceImpl.approvalProcess end");
    }

    @Transactional
    @Override
    public void giveUpRestart(ApprovalProcessDTO approvalProcessDTO) {
        LogUtil.audit("RestartCaseServiceImpl.giveUpRestart start");
        String reportNo = approvalProcessDTO.getReportNo();
        Integer caseTimes = approvalProcessDTO.getCaseTimes();
        //完成当前任务
        bpmService.completeTask_oc(reportNo, caseTimes
                , BpmConstants.OC_RESTART_CASE_MODIFY,approvalProcessDTO.getIdClmRestartCaseRecord());
        //需要将clms_estimate_duty_record表中的数据置为无效
        restartCaseRecordMapper.invalidRestartCase(reportNo, caseTimes);
        //将审批记录置为失效
        restartCaseRecordMapper.invalidRestartCaseRecord(reportNo, caseTimes);
        //操作记录
        operationRecordService.insertOperationRecord(approvalProcessDTO.getReportNo(), BpmConstants.OC_RESTART_CASE_MODIFY, "放弃", null, WebServletContext.getUserIdForLog());
        LogUtil.audit("RestartCaseServiceImpl.giveUpRestart end");
    }

    @Override
    public EstimatePolicyFormDTO getRestartCaseDetail(String reportNo, Integer caseTimes) {
        EstimatePolicyFormDTO result = getRestartCaseInfo(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        return result;
    }

    @Override
    public EstimatePolicyFormDTO getRestartModifyCaseDetail(String reportNo, Integer caseTimes) {
        EstimatePolicyFormDTO result = getRestartCaseInfo(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_RESTART);
        return result;
    }

    @Override
    public EstimatePolicyFormDTO getRestartCaseApprovalDetail(String reportNo, Integer caseTimes) {
        //当前重开信息
        EstimatePolicyFormDTO result = getRestartCaseInfo(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_RESTART);
        List<RestartCaseRecordDTO> dtoList = restartCaseService.getRestratCases(reportNo,caseTimes);
        if(CollectionUtil.isNotEmpty(dtoList)){
            if(ObjectUtil.isNotEmpty(dtoList.get(0).getCaseType())){
                result.setCaseType(Integer.toString(dtoList.get(0).getCaseType()));
            }
            result.setCaseKind(dtoList.get(0).getCaseKind());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getHistoryReportPayList(String reportNo, Integer caseTimes) {
        List<Map<String, Object>> resultMaps = new ArrayList<>();
        if (caseTimes > 1) {
            for (int i = 1; i < caseTimes; i++) {
                Map<String, Object> map = new HashMap<>();
                map.put("reportNo", reportNo);
                map.put("caseTimes", "");
                BigDecimal sum = BigDecimal.ZERO;
                BigDecimal sumPay = BigDecimal.ZERO;
                List<PolicyPayDTO> policyPayList = policyPayService.getByReportNo(reportNo, i);
                if (!CollectionUtils.isEmpty(policyPayList)) {
                    List<BigDecimal> policyPay = policyPayList.stream().map(PolicyPayDTO::getPolicyPay).collect(Collectors.toList());
                    List<BigDecimal> policySumPay = policyPayList.stream().map(PolicyPayDTO::getPolicySumPay).collect(Collectors.toList());
                    sum = BigDecimalUtils.sum(policyPay);
                    sumPay = BigDecimalUtils.sum(policySumPay);
                    policyPayList.forEach(PolicyPayDTO -> {
                        map.put("indemnityAmount", PolicyPayDTO.getPolicyPay());
                        map.put("feeAmount", PolicyPayDTO.getPolicySumFee());
                    });
                }
                WholeCaseVO wholeCase = new WholeCaseVO();
                wholeCase.setReportNo(reportNo);
                wholeCase.setCaseTimes(i);
                WholeCaseVO wv = wholeCaseMapper.getWhleCaseVoByReportNo(wholeCase);
                String conclusion = handleIndemnityConclusion(wv.getIndemnityConclusion(), wv.getIndemnityModel());
                map.put("indemnityConclusion", conclusion);
                map.put("payAmount", sum);
                map.put("endCaseTime", wv.getEndCaseDate());
                map.put("policySumPay",sumPay);
                resultMaps.add(map);
            }
        }
        return resultMaps;
    }

    @Override
    public void updateIsNewest(String reportNo, Integer caseTimes) {
        for (int i = caseTimes; i > 0; i--) {
            RestartCaseRecordEntity restartCaseRecordEntity = new RestartCaseRecordEntity();
            restartCaseRecordEntity.setReportNo(reportNo);
            restartCaseRecordEntity.setCaseTimes(i);
            restartCaseRecordEntity.setUpdatedBy(WebServletContext.getUserId());
            restartCaseRecordMapper.updateIsNewest(restartCaseRecordEntity);
        }
    }

    private String handleIndemnityConclusion(String indemnityConclusion, String indemnityModel) {
        String conclusion = null;
        if (ConfigConstValues.INDEMNITYCONCLUSION_PAY.equals(indemnityConclusion)) {
            if (ConfigConstValues.INDEMNITYMODEL_PROTOCOL.equals(indemnityModel)) {
                conclusion = "协议赔付";
            } else if (ConfigConstValues.INDEMNITYMODEL_ACCOMMODATION.equals(indemnityModel)) {
                conclusion = "通融赔付";
            } else {
                conclusion = "正常赔付";
            }
        } else {
            if (ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED.equals(indemnityConclusion)) {
                conclusion = "零结";
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_CANCEL.equals(indemnityConclusion)) {
                conclusion = "立案注销";
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(indemnityConclusion)) {
                conclusion = "拒赔";
            } else if(ConfigConstValues.INDEMNITYCONCLUSION_CANCEL_REPORT.equals(indemnityConclusion)) {
                conclusion = "报案注销";
            }
        }
        return conclusion;
    }

    private EstimatePolicyFormDTO getRestartCaseInfo(String reportNo, Integer caseTimes, String type) {
        EstimatePolicyFormDTO result = null;
        LogUtil.audit("报案跟踪查询立案信息,reportNo={}, caseTimes={}", reportNo, caseTimes);
        if (EstimateUtil.ESTIMATE_TYPE_REGISTRATION.equals(type)) {
            //02 立案未决
            result = estimateService.getEstimateDataByTache(reportNo, caseTimes, type, null);
        } else {
            //05 重开未决
            result = estimateService.getEstimateDataByType(reportNo, caseTimes, type);
        }
        //初始化此案件前一次责任赔款金额
        maxPayService.initEstClaimCasePayMaxPay(result.getEstimatePolicyList());
        int policyListSize = ListUtils.isNotEmpty(result.getEstimatePolicyList()) ? result.getEstimatePolicyList().size() : 0;
        LogUtil.audit("报案跟踪环节做过修正金额, reportNo={}, caseTimes={}, policyList.size={}", reportNo, caseTimes, policyListSize);
        result.setOldEstimateAmountSum(EstimateUtil.getEstimateAmountSum(result.getEstimatePolicyList()));

        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        result.setRegisterUm(wholeCaseBaseDTO.getRegisterUm());
        if (ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister())) {
            if (ConstValues.SYSTEM_UM.equalsIgnoreCase(wholeCaseBaseDTO.getRegisterUm())) {
                result.setRegisterName(ConstValues.SYSTEM_NAME);
            } else {
                result.setRegisterName(userInfoService.getUserNameById(wholeCaseBaseDTO.getRegisterUm()));
            }
        }
        result.setRegisterDate(wholeCaseBaseDTO.getRegisterDate());

        ResponseResult resultVO = registerCaseService.registerCaseCheck(reportNo, caseTimes);
        boolean isAutoRegisterOrAuditing = (ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister()) &&
                ConstValues.SYSTEM_UM.equals(wholeCaseBaseDTO.getRegisterUm())) || !ErrorCode.RegisterCase.CAN_APPLY_REGISTER_CASE.equals(resultVO.getCode());
        if (isAutoRegisterOrAuditing) {
            result.setIsAmend(EstimateConstValues.NO_AMEND);
        } else {
            result.setIsAmend(EstimateConstValues.YES_AMEND);
        }
        //报即立(报案估损)金额
        result.setEstimateLossAmount(clmsEstimateRecordService.getEstimateLossAmount(reportNo, caseTimes));
        //概要信息补全
        addDetailInfo(result);
        // 获取环节号
        String taskId = caseClassDao.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes, "");
        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, taskId);
        List<CaseClassDTO> caseClassList = caseClassDao.getBigCaseClassList(reportNo, caseTimes, "", taskId);
        if (!CollectionUtils.isEmpty(caseClassList)){
            result.setCaseType(caseClassList.get(0).getCaseSubClass());
        }
        if (!CollectionUtils.isEmpty(caseSubClassList)){
            result.setCaseKind(caseSubClassList);
        }
        return result;
    }

    private RestartCaseRecordEntity buildPO(RestartCaseVO restartCaseVO) {
        RestartCaseRecordEntity restartCaseRecordEntity = new RestartCaseRecordEntity();
        restartCaseRecordEntity.setIdClmRestartCaseRecord(UuidUtil.getUUID());
        restartCaseRecordEntity.setCreatedBy(WebServletContext.getUserName());//创建人员
        restartCaseRecordEntity.setUpdatedBy(WebServletContext.getUserName());//修改人员
        restartCaseRecordEntity.setReportNo(restartCaseVO.getReportNo());
        restartCaseRecordEntity.setCaseTimes(restartCaseVO.getCaseTimes());
        restartCaseRecordEntity.setCaseType(restartCaseVO.getCaseType());
        List<String> caselist = restartCaseVO.getCaseKind();
        if(CollectionUtil.isNotEmpty(caselist)) {
           String caseKind = StringUtils.getMergeString(caselist,Constants.SEPARATOR);
           restartCaseRecordEntity.setCaseKind(caseKind);
        }


        restartCaseRecordEntity.setRestartReason(restartCaseVO.getRestartReason());
        restartCaseRecordEntity.setRestartDescription(restartCaseVO.getRestartDescription());
        restartCaseRecordEntity.setRestartAmount(restartCaseVO.getRestartAmount());
        return restartCaseRecordEntity;
    }

    private RestartCaseRecordEntity buildRetartModifyPO(RestartCaseVO restartCaseVO) {
        RestartCaseRecordEntity restartCaseRecordEntity = new RestartCaseRecordEntity();
        restartCaseRecordEntity.setUpdatedBy(WebServletContext.getUserName());//修改人员
        restartCaseRecordEntity.setCaseType(restartCaseVO.getCaseType());
        List<String> caselist = restartCaseVO.getCaseKind();
        if(CollectionUtil.isNotEmpty(caselist)) {
            String caseKind = StringUtils.getMergeString(caselist,Constants.SEPARATOR);
            restartCaseRecordEntity.setCaseKind(caseKind);
        }
        restartCaseRecordEntity.setRestartReason(restartCaseVO.getRestartReason());
        restartCaseRecordEntity.setRestartDescription(restartCaseVO.getRestartDescription());
        restartCaseRecordEntity.setRestartAmount(restartCaseVO.getRestartAmount());
        return restartCaseRecordEntity;
    }

    private void addDetailInfo(EstimatePolicyFormDTO result) {
        String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(result.getReportNo());
        String departmentName = "";
        if (StringUtils.isNotEmpty(acceptDepartmentCode)) {
            //关联机构表查名称
            DepartmentDefineEntity departmentDefineEntity = departmentDefineService.getDepartmentInfo(acceptDepartmentCode);
            if (departmentDefineEntity != null) {
                departmentName = departmentDefineEntity.getDepartmentChineseName();
            }
        }
        result.setCaseDepartment(departmentName);
        BigDecimal sum;
        List<PolicyPayDTO> policyPayList = policyPayService.getByReportNo(result.getReportNo(), result.getCaseTimes());
        if (policyPayList == null || policyPayList.size() < 1) {
            sum = new BigDecimal(0);
        } else {
            List<BigDecimal> policyPay = policyPayList.stream().map(PolicyPayDTO::getPolicyPay).collect(Collectors.toList());
            sum = BigDecimalUtils.sum(policyPay);
        }
        result.setSumPayAmount(sum);

        String caseStatus = caseProcessService.getCaseProcessStatus(result.getReportNo(), result.getCaseTimes());
        String caseStatusName = CaseProcessStatus.getName(caseStatus);
        result.setProcessStatus(caseStatusName);

        //加入核赔结论字段
        WholeCaseVO vo = restartCaseRecordMapper.getCaseByReportNoAndCaseTimes(result.getReportNo(),result.getCaseTimes());
        String strConclusion = handleIndemnityConclusion(vo.getIndemnityConclusion(),vo.getIndemnityModel());
        result.setIndemnityConclusion(strConclusion);
    }

    //分页
    private WholeCasePageResult getHistoryCasePage(WholeCaseVO wholeCase) {
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        Pager pager = new Pager();
        pager.setPageIndex(wholeCase.getCurrentPage());
        pager.setPageRows(wholeCase.getPerPageSize());
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        PageHelper.orderBy("report_date desc");
//        PageHelper.orderBy("case_times asc");
        List<WholeCaseVO> list = null;
        if (StringUtils.isNotEmpty(wholeCase.getReportNo())) {
            list = restartCaseRecordMapper.getHistoryCaseByReportNo(wholeCase.getReportNo(), departmentCodes);
        } else if (StringUtils.isNotEmpty(wholeCase.getPolicyNo()) || StringUtils.isNotEmpty(wholeCase.getCaseNo())) {
            list = restartCaseRecordMapper.getHistoryCaseByPolicyCaseNo(wholeCase.getPolicyNo(), wholeCase.getCaseNo(), wholeCase.getReportBatchNo(), departmentCodes);
        } else if (StringUtils.isNotEmpty(wholeCase.getReportBatchNo())) {
            list = restartCaseRecordMapper.getHistoryCaseByBatchNo(wholeCase.getReportBatchNo(), departmentCodes);
        } else {
            return null;
        }
        Integer count;
        if (RapeCheckUtil.isEmpty(list)) {
            return null;
        }
        LogUtil.audit("getHistoryCasePage list = {}", JSONObject.toJSON(list));
        for (int i = 0; i < list.size(); i++) {
            WholeCaseVO r = list.get(i);
            String caseStatusName = caseProcessService.getCaseProcessStatusName(r.getReportNo(),r.getCaseTimes());
            r.setCaseStatusName(caseStatusName);

            r.setAccidentDate(printMapper.getAccidentDateByReportNo(r.getReportNo()));//获取事故时间
            r.setInsuredName(printMapper.getInsuredNameByReportNo(r.getReportNo()));//获取被保险人
            r.setEndCaseAmount(this.getSumPay(r.getReportNo(), r.getCaseTimes()));//获取结案金额
            //核赔结论赋值中文
            String conclusion = handleIndemnityConclusion(r.getIndemnityConclusion(), r.getIndemnityModel());
            //零结或者注销
            if (BaseConstant.STRING_2.equals(r.getIndemnityConclusion()) || BaseConstant.STRING_5.equals(r.getIndemnityConclusion()) || BaseConstant.STRING_6.equals(r.getIndemnityConclusion())) {
                r.setIndemnityConclusion(conclusion);
                count = restartCaseRecordMapper.countTask(r.getReportNo(), r.getCaseTimes());
                LogUtil.info("该案件，ReportNo = {}，caseTimes = {}，查找库中存在收单任务，数量为：{}",
                        r.getReportNo(), r.getCaseTimes(), count);
                if (count < 1 && r.getCaseTimes() ==1) {
                    // 未通过报案跟踪且是首次赔付的 不允许重开
                    r.setIsNewest(BaseConstant.STRING_0);
                    continue;
                }
            }else {
                r.setIndemnityConclusion(conclusion);
                //其他需要结案
                if(!BaseConstant.STRING_0.equals(r.getEndCaseFlag())){
                    //未结案不允许重开
                    r.setIsNewest(BaseConstant.STRING_0);
                    continue;
                }
            }

            // 责任险案件不支持重开
            if (BaseConstant.STRING_1.equals(r.getIsNewest()) && riskPropertyService.displayRiskProperty(r.getReportNo(),null)) {
                r.setIsNewest(BaseConstant.STRING_0);
            }

            //提交之后，未审批的，不能申请重开
            List<RestartCaseRecordEntity> restartCaseRecordEntities = getRestartCaseList(r.getReportNo(), r.getCaseTimes());
            if (restartCaseRecordEntities != null && restartCaseRecordEntities.size() > 0) {
                RestartCaseRecordEntity record = restartCaseRecordEntities.get(0);
                if (record.getApprovalOpinions() == null) {
                    r.setIsNewest(BaseConstant.STRING_2);
                }
            }
        }
        PageInfo<WholeCaseVO> pageInfo = new PageInfo<>(list);
        List<WholeCaseVO> pageList = pageInfo.getList();

        WholeCasePage wholeCasePage = new WholeCasePage();
        wholeCasePage.setPageIndex(pager.getPageIndex());
        wholeCasePage.setPageRows(pager.getPageRows());
        wholeCasePage.setCurrPageRows(pager.getPageRows());
        wholeCasePage.setDefaultPageRows(20);
        wholeCasePage.setTotalPages(pageInfo.getPages());
        wholeCasePage.setHasNextPage(pageInfo.isHasNextPage());
        wholeCasePage.setHasPrevPage(pageInfo.isHasPreviousPage());
        wholeCasePage.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();

        WholeCasePageResult pageResult = new WholeCasePageResult();
        pageResult.setPager(wholeCasePage);
        pageResult.setList(pageList);

        return pageResult;
    }

    private BigDecimal getSumPay(String reportNo, Integer caseTimes) {
        BigDecimal b = printMapper.getSumPay(reportNo, caseTimes);
        b = b.setScale(2, BigDecimal.ROUND_DOWN);
        return b;
    }

    //从入参中获取dutyRecordList数据
    private List<EstimateDutyRecordDTO> getEstimateDutyRecordList(List<EstimatePolicyDTO> epList, Map<String, BigDecimal> oldPlanDutyCodeAmountMap,String reportNo) {
        List<EstimateDutyRecordDTO> dutyRecordList = new ArrayList<>();
        Date archiveTime = new Date();
        Map<String,BigDecimal> ssCoinsRateMap = estimateService.getCoinsRateMap(reportNo);
        // 获取再保的保单，获取主共从共等数据
        Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(reportNo);

        for (EstimatePolicyDTO estimatePolicyDTO : epList) {
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                for (EstimateDutyRecordDTO recordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
                    recordDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_RESTART);
                    recordDTO.setTaskId(TacheConstants.REPORT_TRACK);
                    recordDTO.setEstimateAmount(recordDTO.getLastPayAmount());
                    //费用不做默认
                    recordDTO.setArbitrageFee(null);
                    recordDTO.setExecuteFee(null);
                    recordDTO.setInquireFee(null);
                    recordDTO.setOtherFee(null);
                    recordDTO.setLawyerFee(null);
                    recordDTO.setLawsuitFee(null);
                    recordDTO.setCommonEstimateFee(null);
                    recordDTO.setSpecialSurveyFee(null);
                    recordDTO.setVerifyAppraiseFee(null);
                    BigDecimal oldPayAmount = oldPlanDutyCodeAmountMap.get(recordDTO.getPlanCode()+recordDTO.getDutyCode());
                    if (oldPayAmount != null && recordDTO.getEstimateAmount()!=null){
                        recordDTO.setChgPayValue(recordDTO.getEstimateAmount().subtract(oldPayAmount));
                    }else {
                        //如果上一次是零结，变化量为估损金额
                        recordDTO.setChgPayValue(recordDTO.getEstimateAmount()==null?new BigDecimal(0):recordDTO.getEstimateAmount());
                    }
                    recordDTO.setArchiveTime(archiveTime);
                    BigDecimal coinsRate = ssCoinsRateMap.get(estimatePolicyDTO.getPolicyNo());
                    if (coinsRate != null) {
                        recordDTO.setSsCoinsRate(coinsRate);
                        //0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)
                        String coinsuranceType = null;
                        List<CoinsureDTO> coinsureDTOList = coinsuranceMap.get(recordDTO.getPolicyNo());
                        if (!CollectionUtils.isEmpty(coinsureDTOList)) {
                            coinsuranceType = coinsureDTOList.get(0).getCoinsuranceType();
                        }
                        if ("0".equals(coinsuranceType) || "1".equals(coinsuranceType)){
                            BigDecimal estimateAmount = recordDTO.getEstimateAmount()==null?new BigDecimal(0):recordDTO.getEstimateAmount();
                            BigDecimal coinsPayValue = estimateAmount.multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            BigDecimal coinsChgPayValue = recordDTO.getChgPayValue().multiply(nvl(coinsRate, 0))
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            recordDTO.setSsCoinsPayValue(coinsPayValue);
                            recordDTO.setSsCoinsChgPayValue(coinsChgPayValue);
                        } else {
                            recordDTO.setSsCoinsPayValue(recordDTO.getEstimateAmount());
                            recordDTO.setSsCoinsChgPayValue(recordDTO.getChgPayValue());
                        }

                    }
                    dutyRecordList.add(recordDTO);
                }
            }
        }
        return dutyRecordList;
    }

}

