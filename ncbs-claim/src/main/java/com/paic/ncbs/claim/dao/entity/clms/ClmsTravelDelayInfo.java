package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 延误信息表(ClmsTravelDelayInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsTravelDelayInfo implements Serializable {
    private static final long serialVersionUID = 834148355243855253L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 延误类型 1 航空延误、2 旅行延误、3 其他延误
     */
    private String delayType;

    /**
     * 其他延误描述
     */
    private String otherTypeDescription;
    /**
     * 延误起期
     */
    private Date delayStartDate;
    /**
     * 延误止期
     */
    private Date delayEndDate;
    /**
     * 延误天数
     */
    private Integer delayDays;
    /**
     * 航空公司
     */
    private String airCompany;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 出发地
     */
    private String departPlace;
    /**
     * 目的地
     */
    private String  arrivalPlace;
    /**
     * 预计起飞时间
     */
    private Date expectTakeOffTime;
    /**
     * 预计到达时间
     */
    private Date expectArrivalTime;
    /**
     * 实际到达时间
     */
    private Date realArrivalTime;
    /**
     * 费用金额
     */
    private BigDecimal amount;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;

}

