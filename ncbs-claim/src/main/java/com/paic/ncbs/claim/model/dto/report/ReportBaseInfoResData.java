package com.paic.ncbs.claim.model.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@ApiModel("报案基本信息")
public class ReportBaseInfoResData extends EntityDTO {


    private static final long serialVersionUID = -6047057644235647923L;

    @ApiModelProperty("是否海外 1：境外 0 境内")
    private String overseasOccur  ;

    @ApiModelProperty("出险区域")
    private String accidentArea ;

    @ApiModelProperty("出险国家")
    private String accidentNation ;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("报案号")
    private String policyNo;
    @ApiModelProperty("救援案件号")
    private String succorNo;
    @ApiModelProperty("出险类型")
    private String accidentType;

    @ApiModelProperty("出险类型名称")
    private String accidentTypeName;

    @ApiModelProperty("报案人类型名称")
    private String relationWithReportName;

    @ApiModelProperty("报案人")
    private String reporterName;

    @ApiModelProperty("报案人类型（取值clm_common_parameter  COLLECTION_CODE=AHCS_PERSON_RELATION）")
    private String relationWithReport;

    @ApiModelProperty("联系人名字")
    private String linkManName;

    @ApiModelProperty("与被保险人关系（取值clm_common_parameter  COLLECTION_CODE=AHCS_PERSON_RELATION）")
    private String linkManRelation;

    @ApiModelProperty("与被保险人关系")
    private String linkManRelationName;

    @ApiModelProperty("报案人电话")
    private String reporterCallNo;

    @ApiModelProperty("报案人登记号码")
    private String reporterRegisterTel;

    @ApiModelProperty("")
    private String reporterCallNoForMsg;

    @ApiModelProperty("")
    private String reporterRegisterTelForMsg;

    @ApiModelProperty("报案来源(参考clm_common_parameter.collection_code = REPORT_FROM_TYPE)")
    private String reportMode;

    @ApiModelProperty("报案来源")
    private String reportModeName;

    @ApiModelProperty("接报案人UM编码")
    private String reportregisterUm;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("系统备注(电话中心按规则生成的)")
    private String reportRemark;

    @ApiModelProperty("事故经过")
    private String accidentDetail;

    @ApiModelProperty("事故地点")
    private String accidentPlace;

    @ApiModelProperty("是否符合自助（Y/N）")
    private String isSelfHelp;

    @ApiModelProperty("报案时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date reportDate;

    @ApiModelProperty("出险时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date accidentDate;
    @ApiModelProperty("")
    private String accidentDateForMsg;

    @ApiModelProperty("被保人姓名")
    private String name;

    @ApiModelProperty("事故者姓名")
    private String accidentPersonName;

    @ApiModelProperty("证件号")
    private String certificateNo;

    @ApiModelProperty("证件类型")
    private String certificateType;

    @ApiModelProperty("证件类型名称")
    private String certificateTypeName;

    @ApiModelProperty("报案类型")
    private String reportType;

    @ApiModelProperty("重灾信息")
    private String hugeAccident;
    @ApiModelProperty("重灾名称")
    private String hugeAccidentName;
    @ApiModelProperty("是否重复报案（N：非重复报案）")
    private String isRepeatReport;
    @ApiModelProperty("是否展示重复信息")
    private boolean isShowRepeatInfo;
    @ApiModelProperty("重复报案编号列表")
    private List<String> RepeatReportNoList;
    @ApiModelProperty("案件状态")
    private String abnormalCaseStatus;
    @ApiModelProperty("案件状态描述")
    private String abnormalCaseStatusDesc;
    @ApiModelProperty("客户证件类型")
    private String clientCertificateType;
    @ApiModelProperty("联系人列表")
    private List<LinkManDTO> linkManList;
    @ApiModelProperty("原因说明")
    private String reasonDesc;
    @ApiModelProperty("备注")
    private List<String> remarks;
    @ApiModelProperty("出生日期")
    private String birthday;
    @ApiModelProperty("处理后的直赔标识")
    private String isDirectCompensation;
    @ApiModelProperty("合作伙伴编码")
    private String partnerCode;

    @ApiModelProperty("省份CODE")
    private String accidentProvince ;
    @ApiModelProperty("出险地点城市代码")
    private String   accidentCity   ;
    @ApiModelProperty("出险区县代码")
    private String accidentCounty   ;
    /**
     * 出险原因大类
     */
    private String accidentCauseLevel1;
    /**
     * 出险原因细类
     */
    private String accidentCauseLevel2;

    /**
     * 风险备注
     */
    private String riskRemark;

    /**
     * 风险表示
     */
    private String riskFlag;

    /**
     * 是否共保案件，0否1是
     */
    private String isCoinsureCase;

    private boolean displayTargets;
    private String riskGroupNo;
    private String riskGroupName;
    private String targetType;

    private String acceptanceNo;
    private String lossRatio;//赔付率
    private String businessType;//个团属性
    /**
     * 出险原因大类
     */
    private String accidentCauseLevel1Code;
    /**
     * 出险原因细类
     */
    private String accidentCauseLevel2Code;
    @ApiModelProperty("损失类别")
    private String lossClass;
    public void setAccidentCauseLevel1Code(String accidentCauseLevel1Code) {
        this.accidentCauseLevel1Code = accidentCauseLevel1Code;
    }
    public String getAccidentCauseLevel1Code() {
        return accidentCauseLevel1Code;
    }
    public void setAccidentCauseLevel2Code(String accidentCauseLevel2Code) {
        this.accidentCauseLevel2Code = accidentCauseLevel2Code;
    }
    public String getAccidentCauseLevel2Code() {
        return accidentCauseLevel2Code;
    }
    public String getLossClass() {
        return lossClass;
    }
    public void setLossClass(String lossClass) {
        this.lossClass = lossClass;
    }
    public String getLossRatio() {
        return lossRatio;
    }

    public void setLossRatio(String lossRatio) {
        this.lossRatio = lossRatio;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getAcceptanceNo() {
        return acceptanceNo;
    }

    public void setAcceptanceNo(String acceptanceNo) {
        this.acceptanceNo = acceptanceNo;
    }

    /**
     * 外部调查标记，N-旧流程 Y-新流程
     */
    private String investigateFlag;

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur;
    }

    public String getAccidentProvince() {
        return accidentProvince;
    }

    public void setAccidentProvince(String accidentProvince) {
        this.accidentProvince = accidentProvince;
    }

    public String getAccidentCity() {
        return accidentCity;
    }

    public void setAccidentCity(String accidentCity) {
        this.accidentCity = accidentCity;
    }

    public String getAccidentCounty() {
        return accidentCounty;
    }

    public void setAccidentCounty(String accidentCounty) {
        this.accidentCounty = accidentCounty;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public List<String> getRemarks() {
        return remarks;
    }

    public void setRemarks(List<String> remarks) {
        this.remarks = remarks;
    }

    public String getReasonDesc() {
        return reasonDesc;
    }

    public void setReasonDesc(String reasonDesc) {
        this.reasonDesc = reasonDesc;
    }

    public String getCertificateTypeName() {
        return certificateTypeName;
    }

    public void setCertificateTypeName(String certificateTypeName) {
        this.certificateTypeName = certificateTypeName;
    }

    public String getAccidentDateForMsg() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        if (accidentDate != null) {
            return dateFormat.format(accidentDate);
        } else {
            return null;
        }
    }

    public String getAccidentPersonName() {
        return accidentPersonName;
    }

    public void setAccidentPersonName(String accidentPersonName) {
        this.accidentPersonName = accidentPersonName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName;
    }

    public String getRelationWithReport() {
        return relationWithReport;
    }

    public void setRelationWithReport(String relationWithReport) {
        this.relationWithReport = relationWithReport;
    }

    public String getLinkManName() {
        return linkManName;
    }

    public void setLinkManName(String linkManName) {
        this.linkManName = linkManName;
    }

    public String getLinkManRelation() {
        return linkManRelation;
    }

    public void setLinkManRelation(String linkManRelation) {
        this.linkManRelation = linkManRelation;
    }

    public String getReporterCallNo() {
        return reporterCallNo;
    }

    public void setReporterCallNo(String reporterCallNo) {
        this.reporterCallNo = reporterCallNo;
    }

    public String getReporterRegisterTel() {
        return reporterRegisterTel;
    }

    public void setReporterRegisterTel(String reporterRegisterTel) {
        this.reporterRegisterTel = reporterRegisterTel;
    }

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode;
    }

    public String getReportregisterUm() {
        return reportregisterUm;
    }

    public void setReportregisterUm(String reportregisterUm) {
        this.reportregisterUm = reportregisterUm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReportRemark() {
        return reportRemark;
    }

    public void setReportRemark(String reportRemark) {
        this.reportRemark = reportRemark;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getIsSelfHelp() {
        return isSelfHelp;
    }

    public void setIsSelfHelp(String isSelfHelp) {
        this.isSelfHelp = isSelfHelp;
    }

    public String getLinkManRelationName() {
        return linkManRelationName;
    }

    public void setLinkManRelationName(String linkManRelationName) {
        this.linkManRelationName = linkManRelationName;
    }

    public String getRelationWithReportName() {
        return relationWithReportName;
    }

    public void setRelationWithReportName(String relationWithReportName) {
        this.relationWithReportName = relationWithReportName;
    }

    public String getReportModeName() {
        return reportModeName;
    }

    public void setReportModeName(String reportModeName) {
        this.reportModeName = reportModeName;
    }

    public String getAccidentTypeName() {
        return accidentTypeName;
    }

    public void setAccidentTypeName(String accidentTypeName) {
        this.accidentTypeName = accidentTypeName;
    }

    public String getReporterCallNoForMsg() {
        if (StringUtils.isEmpty(reporterCallNo)) {
            return "";
        }
        return reporterCallNo.replace("+86-", "");

    }

    public String getReporterRegisterTelForMsg() {
        if (StringUtils.isEmpty(reporterRegisterTel)) {
            return "";
        }
        return reporterRegisterTel.replace("+86-", "");
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getAbnormalCaseStatus() {
        return abnormalCaseStatus;
    }

    public void setAbnormalCaseStatus(String abnormalCaseStatus) {
        this.abnormalCaseStatus = abnormalCaseStatus;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public List<LinkManDTO> getLinkManList() {
        return linkManList;
    }

    public void setLinkManList(List<LinkManDTO> linkManList) {
        this.linkManList = linkManList;
    }

    public void setReporterCallNoForMsg(String reporterCallNoForMsg) {
        this.reporterCallNoForMsg = reporterCallNoForMsg;
    }

    public void setReporterRegisterTelForMsg(String reporterRegisterTelForMsg) {
        this.reporterRegisterTelForMsg = reporterRegisterTelForMsg;
    }

    public void setAccidentDateForMsg(String accidentDateForMsg) {
        this.accidentDateForMsg = accidentDateForMsg;
    }

    public String getIsRepeatReport() {
        return isRepeatReport;
    }

    public void setIsRepeatReport(String isRepeatReport) {
        this.isRepeatReport = isRepeatReport;
    }

    public boolean isShowRepeatInfo() {
        return isShowRepeatInfo;
    }

    public void setShowRepeatInfo(boolean showRepeatInfo) {
        isShowRepeatInfo = showRepeatInfo;
    }

    public List<String> getRepeatReportNoList() {
        return RepeatReportNoList;
    }

    public void setRepeatReportNoList(List<String> repeatReportNoList) {
        RepeatReportNoList = repeatReportNoList;
    }

    public String getHugeAccident() {
        return hugeAccident;
    }

    public void setHugeAccident(String hugeAccident) {
        this.hugeAccident = hugeAccident;
    }

    public String getHugeAccidentName() {
        return hugeAccidentName;
    }

    public void setHugeAccidentName(String hugeAccidentName) {
        this.hugeAccidentName = hugeAccidentName;
    }

    public String getIsDirectCompensation() {
        return isDirectCompensation;
    }

    public void setIsDirectCompensation(String isDirectCompensation) {
        this.isDirectCompensation = isDirectCompensation;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getSuccorNo() {
        return succorNo;
    }

    public void setSuccorNo(String succorNo) {
        this.succorNo = succorNo;
    }

    public String getAccidentArea() {
        return accidentArea;
    }

    public void setAccidentArea(String accidentArea) {
        this.accidentArea = accidentArea;
    }

    public String getAccidentNation() {
        return accidentNation;
    }

    public void setAccidentNation(String accidentNation) {
        this.accidentNation = accidentNation;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAccidentCauseLevel1() {
        return accidentCauseLevel1;
    }

    public void setAccidentCauseLevel1(String accidentCauseLevel1) {
        this.accidentCauseLevel1 = accidentCauseLevel1;
    }

    public String getAccidentCauseLevel2() {
        return accidentCauseLevel2;
    }

    public void setAccidentCauseLevel2(String accidentCauseLevel2) {
        this.accidentCauseLevel2 = accidentCauseLevel2;
    }

    public String getRiskRemark() {
        return riskRemark;
    }

    public void setRiskRemark(String riskRemark) {
        this.riskRemark = riskRemark;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public boolean isDisplayTargets() {
        return displayTargets;
    }

    public void setDisplayTargets(boolean displayTargets) {
        this.displayTargets = displayTargets;
    }

    public String getIsCoinsureCase() {
        return isCoinsureCase;
    }

    public void setIsCoinsureCase(String isCoinsureCase) {
        this.isCoinsureCase = isCoinsureCase;
    }

    public String getAbnormalCaseStatusDesc() {
        return abnormalCaseStatusDesc;
    }

    public void setAbnormalCaseStatusDesc(String abnormalCaseStatusDesc) {
        this.abnormalCaseStatusDesc = abnormalCaseStatusDesc;
    }

    public String getRiskGroupNo() {
        return riskGroupNo;
    }

    public void setRiskGroupNo(String riskGroupNo) {
        this.riskGroupNo = riskGroupNo;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public String getInvestigateFlag() {
        return investigateFlag;
    }

    public void setInvestigateFlag(String investigateFlag) {
        this.investigateFlag = investigateFlag;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }
}
