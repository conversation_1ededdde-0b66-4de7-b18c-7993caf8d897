package com.paic.ncbs.claim.service.other;

import com.paic.ncbs.claim.model.dto.other.CommonDTO;
import com.paic.ncbs.claim.service.base.BaseService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;

public interface CommonService extends BaseService<CommonDTO> {

    String generateIdBySysGuid();

    String generateReportNo(String departmentCode);

    String generateReportNoNew(String departmentCode, String flag);

    String generateCaseNo(String departmentCode);

    String generateCaseNoNew(String departmentCode, String flag);

    String generateNo(String noType, VoucherTypeEnum voucherTypeEnum, String departmentCode);

    String generateNoOnline(String noType, VoucherTypeEnum voucherTypeEnum, String departmentCode);

}
