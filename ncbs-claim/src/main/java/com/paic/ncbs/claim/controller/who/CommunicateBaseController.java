package com.paic.ncbs.claim.controller.who;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.communicate.CommunicateBaseVO;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.settle.CommunicateBaseService;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import io.swagger.annotations.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Api(tags = "沟通")
@RestController
@RequestMapping("/who/app/communicateBaseAction")
public class CommunicateBaseController extends BaseController {

    @Autowired
    private CommunicateBaseService communicateBaseService;

    @Autowired
    private CacheService cacheService;
    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;

    @ApiOperation(value = "获取案件的沟通次数")
    @GetMapping(value = "/getCommunicateBaseTimes/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = true, dataType = "Integer", dataTypeClass=Integer.class , paramType = "Query")
    })
    public ResponseResult<Integer> getCommunicateBaseTimes(@PathVariable("reportNo") String reportNo,
                                                              @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(communicateBaseService.getCommunicateBaseTimes(reportNo, caseTimes));
    }

    @ApiOperation(value = "完成沟通")
    @PostMapping(value = "/finishCommunicate")
    public ResponseResult<Object> finishCommunicate(@RequestBody CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {
        UserInfoDTO userDTO = WebServletContext.getUser();
        communicateBaseVO.setUserId(userDTO.getUserCode());
        communicateBaseService.finishCommunicate(communicateBaseVO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "获取历史沟通信息列表")
    @GetMapping(value = "/getHistoryCommunicateBaseList/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = true, dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<CommunicateBaseVO> getHistoryCommunicateBaseList(@PathVariable("reportNo") String reportNo,
                                                                           @PathVariable("caseTimes") int caseTimes) throws GlobalBusinessException {

        return ResponseResult.success(communicateBaseService.getHistoryCommunicateBaseList(reportNo, caseTimes));
    }

    @ApiOperation(value = "获取沟通任务")
    @PostMapping(value = "/getNotCommunicateTask")
    public ResponseResult<CommunicateBaseVO> getNotCommunicateTask(@RequestBody CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {

        return ResponseResult.success(communicateBaseService.getNotCommunicateTask(communicateBaseVO));
    }

    @ApiOperation(value = "初始化沟通基本信息")
    @PostMapping(value = "/initCommunicateBaseInfo")
    public ResponseResult<CommunicateBaseVO> initCommunicateBaseInfo(@RequestBody CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {

        UserInfoDTO userDTO = WebServletContext.getUser();
        communicateBaseVO.setUserId(userDTO.getUserCode());

        return ResponseResult.success(communicateBaseService.initCommunicateBaseInfo(communicateBaseVO));
    }

    @ApiOperation(value = "根据机构号及用户id查询沟通人")
    @GetMapping(value = "/searchUserByUmIdAndDepartmentCode")
    public ResponseResult searchUserByUmIdAndDepartmentCode(@RequestParam("userId") String userId) throws GlobalBusinessException {
        List<Map<String,String>> userL = new ArrayList<>();
        try {
            String selfId = WebServletContext.getUserId();
            UserInfoDTO userInfoDTO = cacheService.queryUserInfo(userId);
            List<SystemComInfoDTO> systemComInfoDTOS = cacheService.queryUserSystemComList(userId);
            if (userInfoDTO !=null && CollectionUtils.isNotEmpty(systemComInfoDTOS) && !selfId.equals(userId)) {
                systemComInfoDTOS.forEach(s->{
                    Map<String,String> resultMap = new HashMap<>();
                    resultMap.put("userCode",userInfoDTO.getUserCode());
                    resultMap.put("userName",userInfoDTO.getUserName());
                    resultMap.put("comCode",s.getComCode());
                    resultMap.put("comCodeName",s.getComCodeName());
                    userL.add(resultMap);
                });
            }
        } catch (Exception e) {
            LogUtil.error("searchUserByUmIdAndDepartmentCode-查询用户异常",e);
        }
        return ResponseResult.success(userL);
    }

    @ApiOperation(value = "发送沟通信息")
    @PostMapping(value = "/sendCommunicateBaseInfo")
    public ResponseResult<Object> sendCommunicateBaseInfo(@RequestBody CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {
        UserInfoDTO userDTO = WebServletContext.getUser();
        communicateBaseVO.setUserId(userDTO.getUserCode());
        communicateBaseService.sendCommunicateBaseInfo(communicateBaseVO);
        //发送MQ通知TPA中台
        if(Objects.equals(BpmConstants.REPORT_TRACK,communicateBaseVO.getCommunicateLink())){
            claimSendTpaMqInfoService.sendTpaMq(communicateBaseVO.getReportNo(),communicateBaseVO.getCaseTimes(), CaseProcessStatus.COMMUNICATE_PENDING_RESPONDED.getCode());
        }
        return ResponseResult.success();
    }

    @ApiOperation(value = "获取沟通详情")
    @GetMapping(value = "/getCommunicateDetailByCommunicateBaseId/{idAhcsCommunicateBase}")
    public ResponseResult<CommunicateBaseVO> getCommunicateDetailByCommunicateBaseId(@ApiParam("沟通主表主键") @PathVariable("idAhcsCommunicateBase") String idAhcsCommunicateBase) {
        return ResponseResult.success(communicateBaseService.getCommunicateDetailByCommunicateBaseId(idAhcsCommunicateBase));
    }

    @ApiOperation("获取可分配调度的机构列表")
    @GetMapping(value = "/getCommunicateDepartmentList")
    public ResponseResult<Object> getCommunicateDepartmentList() {
        return ResponseResult.success(communicateBaseService.getCommunicateDepartmentList());

    }
}
