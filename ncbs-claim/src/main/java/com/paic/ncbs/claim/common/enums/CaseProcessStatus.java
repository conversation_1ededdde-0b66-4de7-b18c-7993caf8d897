package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 案件流程状态
 */
public enum CaseProcessStatus {

    /**
     * 主流程-主状态
     */
    PENDING_REGISTER("01","已报案待报案跟踪"),
    PENDING_ACCEPT("02","已报案跟踪待收单"),
    PENDING_SETTLE("03","已收单待理算"),
    CASE_CLOSED("05","已结案"),
    CASE_CANCELLED("06","已注销"),

    /**
     * 子状态
     */
    REGISTRATION_WAIT_APPROVING("0101","待立案审批"),
    REGISTRATION_REJECT("0102","立案退回"),
    REGISTRATION_APPROVED("0103","立案已批复"),
    /**
     * 0301-理算节点发起的客户补材
     */
    WAIT_CUSTOMER_SUPPLEMENTS("0301","理算待客户补材"),

    //二核任务状态
    REPORTTRACK_SECOND_UNDERWRITING("0105","报案跟踪待理赔二核"),
    CHECKDUTY_SECOND_UNDERWRITING("0202","收单待理赔二核"),
    SETTLE_SECOND_UNDERWRITING("0302","理算待理赔二核"),
    /**
     * 0104-报案跟踪节点发起的客户补材
     */
    WAIT_CUSTOMER_SUPPLEMENTS_0104("0104","报案跟踪待客户补材"),

    /**
     * 0201-收单节点发起的客户补材
     */
    WAIT_CUSTOMER_SUPPLEMENTS_0201("0201","收单待客户补材"),

    //零注 06
    CANCEL_WAIT_APPROVING("0601","零注待审批"),
    CANCEL_REJECT("0602","零注退回"),
    CANCEL_APPROVED("0603","零注已批复"),

    //分支流程-调查 07
    WAIT_INVESTIGATION_APPROVING("0701","待提调审批"),
    START_INVESTIGATION_REJECT("0702","提调退回"),
    INVESTIGATING("0703","调查处理中"),
    INVESTIGATION_REVIEW("0704","调查待复核"),
    INVESTIGATION_COMPLETED("0705","调查已完成"),

    //沟通 08
    COMMUNICATE_PENDING_RESPONDED("0801","沟通待回复"),
    COMMUNICATE_COMPLETED("0802","沟通已完成"),

    //核赔 10
    WAIT_VERIFICATION("1001","待核赔"),
    VERIFICATION_REJECT("1002","核赔退回"),

    //拒赔11
    WAIT_REJECT("1101","待拒赔审批"),
    REJECT_BACK("1102","拒赔退回"),
    PREPAY_REJECT("1103","预赔审批");

    private final String code;
    private final String name;

    private CaseProcessStatus(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(String code){
        if(StrUtil.isEmpty(code)){
            return null;
        }
        for (CaseProcessStatus status : CaseProcessStatus.values()){
            if (code.equals(status.getCode())){
                return status.getName();
            }
        }
        return null;
    }
}
