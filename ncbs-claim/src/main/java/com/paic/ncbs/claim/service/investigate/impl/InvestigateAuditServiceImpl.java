package com.paic.ncbs.claim.service.investigate.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateAuditMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.api.DocumentListDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigatorDto;
import com.paic.ncbs.claim.model.dto.investigate.tpa.TpaInvestigatorTaskDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.model.vo.investigate.OffSiteInvestigateVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyPdfDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.investigate.InvestigateAuditService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.print.util.GeneratePDFUtil;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;

@Slf4j
@Service("investigateAuditService")
public class InvestigateAuditServiceImpl implements InvestigateAuditService {
	@Autowired
	InvestigateService investigateService;

    @Autowired
    InvestigateAuditMapper investigateAuditDao;
    
    @Autowired
    InvestigateMapper investigateDao;

    @Autowired
    InvestigateTaskMapper investigateTaskDao;

	@Autowired
	private DepartmentDefineMapper departmentDefineMapper;

	@Autowired
	private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    BpmService bpmService ;

	@Autowired
	private TaskInfoService taskInfoService ;

	@Autowired
	private CaseProcessService caseProcessService ;

	@Autowired
	private WholeCaseService wholeCaseService;

	@Autowired
	private FileUploadService fileUploadService;

	@Autowired
	private IOBSFileUploadService iobsFileUploadService;

	@Autowired
	private CaseBaseService caseBaseService;

	@Autowired
	private OcasRequest ocasRequest;

    @Autowired
	private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;
	@Autowired
	private TPAFeign tpaFeign;
	@Autowired
	private ReportInfoExMapper reportInfoExMapper;
	@Autowired
	private IOperationRecordService operationRecordService;
	@Autowired
	private NoticeService noticeService;
    @Transactional(rollbackFor = Exception.class)
    @Override
	public void addInvestigateAudit(InvestigateAuditDTO investigateAudit) throws GlobalBusinessException {

    	checkExist(investigateAudit);

		// 移交审批
		if (InvestigateConstants.INVESTIGATE_AUDIT_OPINION_TRANSFER.equals(investigateAudit.getAuditOpinion())) {
			handleTransferApproval(investigateAudit);
			return;
		}

		investigateAudit.setIdAhcsInvestigateAudit(UuidUtil.getUUID());
		InvestigateVO investigateVo = investigateDao.getInvestigateById(investigateAudit.getIdAhcsInvestigate());
		InvestigateDTO investigate = new InvestigateDTO();
		BeanUtils.copyProperties(investigateVo, investigate);
		investigate.setUpdatedBy(investigateAudit.getUpdatedBy());
		investigate.setServerCode(investigateAudit.getServerCode());
		if(StringUtils.isEmptyStr(investigateAudit.getInvestigateDepartment())){
			investigateAudit.setInvestigateDepartment(investigate.getInvestigateDepartment());
		}
		investigateAudit.setInitiatorUm(investigate.getInitiatorUm());
		investigateAuditDao.addInvestigateAudit(investigateAudit);

		Map<String, Object> extVariable = new HashMap<>();
		extVariable.put(InvestigateConstants.ID_AHCS_INVESTIGATE,investigate.getIdAhcsInvestigate());

		String primaryIdAhcsInvestigateTask = UuidUtil.getUUID();

		if(investigateAudit.getInvestigators() != null && !investigateAudit.getInvestigators().isEmpty()
				&& !investigateAudit.getAuditOpinion().equals(InvestigateConstants.INVESTIGATE_AUDIT_OPINION_REJECT)){
			String primaryInvestigatorUm = null;
			for(InvestigatorDto investigator:investigateAudit.getInvestigators()){
				Map<String, Object> extVariable2 = new HashMap<>();

				String idAhcsInvestigateTask = UuidUtil.getUUID();
				String investigatorUm = investigator.getInvestigatorUm();
				InvestigateTaskDTO task = new InvestigateTaskDTO();

				// 外部调查
				if (BaseConstant.STRING_02.equals(investigate.getInitMode()) && ConstValues.YES.equals(investigate.getInvestigateFlag())) {
					task.setTaskType(investigateAudit.getTaskType());
					task.setInvestigateDepartment(investigatorUm);
					task.setInvestigateDepartmentName(investigator.getInvestigatorUmName());

					investigateAudit.setInvestigateDepartment(investigatorUm);
				} else {
					if(StringUtils.isEmptyStr(investigate.getInvestigateDepartment())) {
						investigate.setInvestigateDepartment(investigateAudit.getInvestigateDepartment());
					}
					task.setInvestigateDepartment(investigate.getInvestigateDepartment());
				}

				task.setIdAhcsInvestigateTask(primaryIdAhcsInvestigateTask);
				task.setUpdatedBy(investigateAudit.getCreatedBy());
				task.setCreatedBy(investigateAudit.getCreatedBy());
				task.setIdAhcsInvestigate(investigate.getIdAhcsInvestigate());
				task.setCaseTimes(investigate.getCaseTimes());
				task.setDispatchOpinion(investigateAudit.getRemark());
				task.setDispatchUm(investigateAudit.getAuditorUm());
				task.setInvestigatorUm(investigatorUm);
				task.setInvestigatorUmName(investigator.getInvestigatorUmName());
				task.setReportNo(investigate.getReportNo());
				task.setIsOffsiteTask(InvestigateConstants.VALIDATE_FLAG_NO);
				task.setHasEvidence(InvestigateConstants.VALIDATE_FLAG_NO);
				task.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_PROCESSING);
				task.setIsPrimaryTask(investigator.getIsPrimary());
				task.setIdAhcsInvestigateAudit(investigateAudit.getIdAhcsInvestigateAudit());
				investigateTaskDao.addInvestigateTask(task);

				extVariable2.put(InvestigateConstants.INVESTIGATOR_UM, investigatorUm);
				extVariable2.put(InvestigateConstants.ID_AHCS_INVESTIGATE, investigate.getIdAhcsInvestigate());
				extVariable2.put(InvestigateConstants.ID_AHCS_INVESTIGATE_TASK, idAhcsInvestigateTask);
				extVariable2.put(InvestigateConstants.DEPARTMENT_CODE, task.getInvestigateDepartment());
				extVariable2.put(InvestigateConstants.DISPATCH_UM, task.getDispatchUm());
				extVariable2.put(ConstValues.TASK_SPONSOR_UM, task.getDispatchUm());
				extVariable2.put(ConstValues.TASK_DISPATH_UM, task.getDispatchUm());
				investigateService.addIsFromOldSystem(extVariable2, investigate.getReportNo());

				if(investigator.getIsPrimary().equals(InvestigateConstants.VALIDATE_FLAG_NO)){

				}else{
					primaryInvestigatorUm = investigatorUm;
				}
			}
			investigate.setInvestigateStatus(InvestigateConstants.AHCS_INVESTIGATE_STATUS_PROCESSING);
			investigate.setPrimaryInvestigatorUm(primaryInvestigatorUm);
		}
		if (investigateAudit.getAuditOpinion().equals(InvestigateConstants.INVESTIGATE_AUDIT_OPINION_REJECT)) {
			investigateAudit.setAuditReply(investigateAudit.getRemark());
			investigateAuditDao.modifyInvestigateAudit(investigateAudit);
			investigate.setInvestigateStatus(InvestigateConstants.AHCS_INVESTIGATE_STATUS_BACK);
			//提调审批调查退回时添加消息提醒
			NoticesDTO noticesDTO = new NoticesDTO();
			noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
			noticesDTO.setNoticeSubClass(BpmConstants.NSC_INVESTIGATE_APPROVAL);
			noticesDTO.setReportNo(investigate.getReportNo());
			noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
			noticesDTO.setCaseTimes(investigate.getCaseTimes());
			TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(investigate.getReportNo(), investigate.getCaseTimes(), BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
			if (taskInfoDTO!=null){
				noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
			}

		}

		investigateDao.modifyInvestigate(investigate);


		if (InvestigateConstants.AHCS_INVESTIGATE_STATUS_BACK.equals(investigate.getInvestigateStatus())) {
			bpmService.completeTask_oc(investigate.getReportNo(), investigate.getCaseTimes(), BpmConstants.OC_INVESTIGATE_APPROVAL);
			//释放跳转状态之前的状态 ，限报案跟踪、收单、理算
			// zjtang 最新逻辑已无挂起状态，且主流程只会有一个流程未处理的情况，故调整查询逻辑为查询挂起的主流程改为查询未处理的主流程
			TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(investigate.getReportNo(),
					investigate.getCaseTimes(), BpmConstants.SUPEND_USE, BaseConstant.STRING_0);
			if (null != taskInfoDTO && BpmConstants.SURVER.contains(taskInfoDTO.getTaskDefinitionBpmKey())) {
				bpmService.suspendOrActiveTask_oc(investigate.getReportNo(), investigate.getCaseTimes(), taskInfoDTO.getTaskDefinitionBpmKey(), false);
				caseProcessService.updateCaseProcess(investigate.getReportNo(), investigate.getCaseTimes(),
						BpmConstants.TASK_PROCESS_MAP.get(taskInfoDTO.getTaskDefinitionBpmKey()));
			}
			//操作记录
			operationRecordService.insertOperationRecordByLabour(investigate.getReportNo(), BpmConstants.OC_INVESTIGATE_APPROVAL, "不通过", investigateAudit.getRemark());
			//提调审批退回时通知TPA
			List<ReportInfoExEntity> reportInfos = reportInfoExMapper.getReportInfoEx(investigate.getReportNo());
			ReportInfoExEntity reportInfo = reportInfos.get(0);
			if("1".equals(reportInfo.getClaimDealWay())
					&& !"channel".equals(reportInfo.getCompanyId())
					&& 1== investigate.getCaseTimes()) {
				String investigateApprovalTaskId = taskInfoService.getLatestTaskId(investigate.getReportNo(), investigate.getCaseTimes(),BpmConstants.OC_INVESTIGATE_APPROVAL);
				ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
				problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
				RequestData requestData = new RequestData();
				requestData.setRegistNo(investigate.getReportNo());
				requestData.setProblemNo(investigateApprovalTaskId);
				requestData.setProblemType("05");
				requestData.setCaseConclusion(null);
				//TPA问题件答复接口增加沟通人
				requestData.setDealUser(WebServletContext.getUserName()+"-"+investigateAudit.getAuditorUm());
				try {
					requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
				} catch (ParseException e) {
					LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
				}
				requestData.setCaseConclusionDetail(null);
				requestData.setRemark(investigateAudit.getRemark());
				problemCaseRequestDTO.setRequestData(requestData);
				LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",investigate.getReportNo(), JSON.toJSONString(problemCaseRequestDTO));
				ProblemCaseResponseDTO response = tpaFeign.response(problemCaseRequestDTO);
				LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",investigate.getReportNo(), JSON.toJSONString(response));
			}
			return;
		}

		bpmService.completeTask_oc(investigate.getReportNo(),investigate.getCaseTimes(),BpmConstants.OC_INVESTIGATE_APPROVAL);
		//操作记录
		operationRecordService.insertOperationRecordByLabour(investigate.getReportNo(), BpmConstants.OC_INVESTIGATE_APPROVAL, "通过", investigateAudit.getRemark());

		//操作记录
		operationRecordService.insertOperationRecordByLabour(investigate.getReportNo(), BpmConstants.OC_MAJOR_INVESTIGATE, "发起", null);
		// 外部调查
		if (BaseConstant.STRING_02.equals(investigate.getInitMode()) && ConstValues.YES.equals(investigate.getInvestigateFlag())) {
			if (BaseConstant.STRING_1.equals(investigateAudit.getTaskType())) {
				String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(investigate.getReportNo());
				bpmService.startProcessOc(investigate.getReportNo(), investigate.getCaseTimes(), BpmConstants.OC_MAJOR_INVESTIGATE,
						primaryIdAhcsInvestigateTask, BpmConstants.INVESTIGATE_PLATFORM, acceptDepartmentCode);
			} else {
				// 线下委托分发给提调人
				bpmService.startProcessOc(investigate.getReportNo(), investigate.getCaseTimes(), BpmConstants.OC_MAJOR_INVESTIGATE,
						primaryIdAhcsInvestigateTask, investigate.getInitiatorUm(), investigate.getInitiateDepartment());
			}
		} else {
			bpmService.startProcessOc(investigate.getReportNo(),investigate.getCaseTimes(),BpmConstants.OC_MAJOR_INVESTIGATE,
					primaryIdAhcsInvestigateTask,investigateAudit.getInvestigators().get(0).getInvestigatorUm(),
					investigateAudit.getInvestigateDepartment());

//			bpmService.startProcessOc(investigate.getReportNo(),investigate.getCaseTimes(),BpmConstants.OC_MAJOR_INVESTIGATE,
//					primaryIdAhcsInvestigateTask,investigateAudit.getInvestigators().get(0).getInvestigatorUm(),
//					investigateAudit.getInvestigateDepartment());
		}

		caseProcessService.updateCaseProcess(investigate.getReportNo(), investigate.getCaseTimes(),
				CaseProcessStatus.INVESTIGATING.getCode());
		//调查审批通过MQ通知渠道
		if(!InvestigateConstants.AHCS_INVESTIGATE_STATUS_BACK.equals(investigate.getInvestigateStatus())){
			SyncCaseStatusDto dto = new SyncCaseStatusDto();
			dto.setReportNo(investigate.getReportNo());
			dto.setCaseTimes(investigate.getCaseTimes());
			dto.setCaseStatus(SyncCaseStatusEnum.INVESTIGATE);
			mqProducerSyncCaseStatusService.syncCaseStatus(dto);
		}

		// 外部调查
		if (BaseConstant.STRING_02.equals(investigate.getInitMode()) && ConstValues.YES.equals(investigate.getInvestigateFlag())) {
			// 生成调查委托书
			generateInvestigatePdf(primaryIdAhcsInvestigateTask, investigateAudit, investigate);

			// 线上委托下发调查中台
			if (BaseConstant.STRING_1.equals(investigateAudit.getTaskType())) {
				sendTpa(investigateAudit, investigate);
			}
		}
	}

	private void checkExist(InvestigateAuditDTO investigateAudit) {
    	if (StringUtils.isNotEmpty(investigateAudit.getIdAhcsInvestigate()) && StringUtils.isNotEmpty(investigateAudit.getAuditorUm())){
			Integer  num = investigateTaskDao.checkExist(investigateAudit);
			if (num > 0 ){
				throw new GlobalBusinessException("提调审批已发送，请勿重新发送!") ;
			}
		}
	}

	/**
	 * 下发调查中台
	 * @param investigateAudit
	 * @param investigate
	 */
	private void sendTpa(InvestigateAuditDTO investigateAudit, InvestigateDTO investigate) {
		TpaInvestigatorTaskDTO dto = new TpaInvestigatorTaskDTO();
		dto.setInvestigateDepartment(investigateAudit.getInvestigateDepartment());
		dto.setRemark(investigateAudit.getRemark());

		// 批改系统获取电子保单和批单信息
		List<PolicyPdfDTO> policyPdfList = getPolicyPdfList(investigate.getReportNo(), investigate.getCaseTimes());
		dto.setPolicyInfoList(policyPdfList);

		// 提调信息
		InvestigateVO investigateVO = investigateService.getInvestigateById(investigate.getIdAhcsInvestigate());
		dto.setInvestigateInfo(investigateVO);

		// 报案信息
		ReportBaseInfoResData reportBaseInfo = wholeCaseService.getReportBaseInfo(investigate.getReportNo(), investigate.getCaseTimes());
		dto.setReportBaseInfo(reportBaseInfo);

		// 单证
		List<DocumentListDTO> documentList = fileUploadService.queryDocumenList(investigate.getReportNo(), investigate.getCaseTimes());
		dto.setDocumentList(documentList);

		log.info("InvestigateAuditService.sendTpa, dto={}", JSON.toJSONString(dto));

		// TODO 下发调查中台
	}

	/**
	 * 生成调查委托书
	 * @param idAhcsInvestigateTask
	 * @param investigateAudit
	 * @param investigate
	 */
	private void generateInvestigatePdf(String idAhcsInvestigateTask, InvestigateAuditDTO investigateAudit, InvestigateDTO investigate) {
		FileInfoDTO fileInfo = new FileInfoDTO();
		ByteArrayOutputStream out = null;
		String fileId;
		try {
			String fileName = "调查委托书.pdf";
			Map<String, String> map = new HashMap<>();
			map.put("investigateDepartmentName", investigateAudit.getInvestigators().get(0).getInvestigatorUmName());
			out = new ByteArrayOutputStream();
			GeneratePDFUtil generatePDFUtil = new GeneratePDFUtil();
			generatePDFUtil.generatePDF4SX("ClmInvestigateLetter.html", map, out);
			fileId = iobsFileUploadService.uploadFileToFilePlatform(fileName, out.toByteArray());

			fileInfo.setFileId(fileId);
			fileInfo.setSmallCode("008004");
			fileInfo.setFileFormat("pdf");
			fileInfo.setFileName(fileName);
			fileInfo.setFileSize(out.size());
		} catch (Exception e) {
			log.error("generateInvestigatePdf error", e);
			throw new GlobalBusinessException("生成调查委托书失败！");
		} finally {
			if (out != null) {
				try {
					out.flush();
					out.close();
				} catch (IOException e) {
					log.error("out.close error", e);
				}
			}
		}

		if (StringUtils.isEmptyStr(fileId)) {
			throw new GlobalBusinessException("生成调查委托书失败！");
		}

		fileInfo.setCreatedBy(WebServletContext.getUserId());
		fileInfo.setUploadPersonnel("system-system");
		fileInfo.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
		fileInfo.setReportNo(investigate.getReportNo());
		fileInfo.setCaseTimes(investigate.getCaseTimes());
		fileInfo.setFlowType(StringUtils.getFlowTypeByCaseTimes(investigate.getCaseTimes()));
		fileInfo.setRecordNo(fileUploadService.getSeq());
		fileInfo.setSupplement(BaseConstant.UPPER_CASE_N);
		fileInfo.setDocumentGroupId(investigate.getReportNo());
		fileUploadService.addFilesInfo(fileInfo);

		InvestigateTaskDTO investigateTask = new InvestigateTaskDTO();
		investigateTask.setFileId(fileId);
		investigateTask.setIdAhcsInvestigateTask(idAhcsInvestigateTask);
		investigateTaskDao.modifyInvestigateTask(investigateTask);
	}

	/**
	 * 获取电子保单、电子批单
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	private List<PolicyPdfDTO> getPolicyPdfList(String reportNo, Integer caseTimes) {
		List<PolicyPdfDTO> list = Lists.newArrayList();
		List<CaseBaseDTO> caseBaseDTOList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
		for (CaseBaseDTO caseBase : caseBaseDTOList) {
			Map<String, Object> param = Maps.newHashMap();
			param.put("applyType", BaseConstant.INT_3);
			param.put("policyNo", caseBase.getPolicyNo());
			String result = ocasRequest.queryEPolicyClaim(param);

			JSONObject jsonObject = JSON.parseObject(result, JSONObject.class);
			if (Objects.isNull(jsonObject) || !GlobalResultStatus.SUCCESS.getCode().equals(jsonObject.get("responseCode"))) {
				throw new GlobalBusinessException("查询电子保单电子发票失败！");
			}
			List<PolicyPdfDTO> policyPdfList = JSONObject.parseArray(JSON.toJSONString(jsonObject.get("data")), PolicyPdfDTO.class);
			list.addAll(policyPdfList);
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addOffSiteInvestigate(OffSiteInvestigateVO offSiteInvestigateVO, UserInfoDTO u) throws GlobalBusinessException {
		InvestigateAuditDTO auditDTO = new InvestigateAuditDTO();
		auditDTO.setAuditType(InvestigateConstants.AHCS_INVESTIGATE_AUDIT_TYPE_OFFSITE);
		auditDTO.setCreatedBy(u.getUserCode());
		auditDTO.setIdAhcsInvestigate(offSiteInvestigateVO.getIdAhcsInvestigate());
		auditDTO.setIdAhcsInvestigateAudit(UuidUtil.getUUID());
		auditDTO.setInitiatorUm(u.getUserCode());
		auditDTO.setUpdatedBy(u.getUserCode());
		auditDTO.setInvestigateDepartment(offSiteInvestigateVO.getInvestigateDepartment());
		auditDTO.setRemark(offSiteInvestigateVO.getInvestigateItems());
		investigateAuditDao.addInvestigateAudit(auditDTO);

	}

	/**
	 * 处理移交审批逻辑
	 * @param investigateAudit 调查审批DTO
	 * @throws GlobalBusinessException
	 */
	private void handleTransferApproval(InvestigateAuditDTO investigateAudit) throws GlobalBusinessException {

		InvestigateVO investigateVo = investigateDao.getInvestigateById(investigateAudit.getIdAhcsInvestigate());

		if (!InvestigateConstants.AHCS_INVESTIGATE_STATUS_APPROVAL.equals(investigateVo.getInvestigateStatus())) {
			throw new GlobalBusinessException("当前调查状态不是待提调审批，无法进行移交审批");
		}

		TaskInfoDTO currentTask = taskInfoService.findLatestByReportNoAndBpmKey(
			investigateVo.getReportNo(), investigateVo.getCaseTimes(), BpmConstants.OC_INVESTIGATE_APPROVAL);
		
		if (currentTask == null) {
			throw new GlobalBusinessException("未找到当前的提调审批任务");
		}
		
		// 更新任务流的审核人信息
		currentTask.setDepartmentCode(investigateAudit.getAuditorDepartment());
		currentTask.setAssigner(investigateAudit.getAuditorUm());
		currentTask.setAssigneeName(investigateAudit.getAuditorName());
		taskInfoService.updateTaskAssigner(currentTask);
		
		// 构建审批信息字符串
		String assignee = WebServletContext.getUser().getUserName();
		String assigneeOpinion = investigateAudit.getRemark();
		String assigneeTime;
		try {
			assigneeTime = DateUtils.parseToFormatString(new Date(), DateUtils.FULL_DATE_STR);
		} catch (ParseException e) {
			assigneeTime = DateUtils.parseToFormatString(new Date());
		}
		String currentApprover = WebServletContext.getUser().getUserCode();

		// 存储 分配人|分配意见|分配时间|已审核人code
		String existingConclusion = investigateVo.getInvestigateConclusion();
		StringBuilder conclusionBuilder = new StringBuilder();

		String existingApprovers = "";
		if (StringUtils.isNotEmpty(existingConclusion)) {
			String[] existingParts = existingConclusion.split("\\|");
			if (existingParts.length >= 4) {
				existingApprovers = existingParts[3];
			}
		}

		StringBuilder allApprovers = new StringBuilder();
		if (StringUtils.isNotEmpty(existingApprovers)) {
			allApprovers.append(existingApprovers).append(",");
		}
		allApprovers.append(currentApprover);

		conclusionBuilder.append(assignee).append("|")
			.append(assigneeOpinion).append("|")
			.append(assigneeTime).append("|")
			.append(allApprovers.toString());

		InvestigateDTO investigate = new InvestigateDTO();
		investigate.setIdAhcsInvestigate(investigateAudit.getIdAhcsInvestigate());
		investigate.setInvestigateConclusion(conclusionBuilder.toString());
		investigate.setServerCode(investigateAudit.getServerCode());
		investigateDao.modifyInvestigate(investigate);
		
		// 添加移交审批操作记录
		operationRecordService.insertOperationRecordByLabour(investigateVo.getReportNo(), 
			BpmConstants.OC_INVESTIGATE_APPROVAL, "移交审批", investigateAudit.getRemark());
	}
}