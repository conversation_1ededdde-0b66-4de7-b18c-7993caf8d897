package com.paic.ncbs.claim.model.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class PaymentInfoDTO {
    private String createdBy;
    private Date createdDate;
    private String updatedBy;
    private Date updatedDate;
    private String idClmPaymentInfo;
    private String reportNo;
    private Integer caseTimes;
    private Integer subTimes;
    private String idClmChannelProcess;
    @ApiModelProperty("支付信息类型")
    private String paymentInfoType;
    @ApiModelProperty("收付途径：公司柜面,银行转帐,手工转帐等")
    private String collectPayApproach;
    @ApiModelProperty("帐号类型:个人帐号=1,公司帐号=0")
    private String bankAccountAttribute;
    private String cityName;
    @ApiModelProperty("开户行帐号，当收付途径为03－银行转帐时非空")
    private String clientBankAccount;
    @ApiModelProperty("客户银行代码，当收付途径为03－银行转帐时非空")
    private String clientBankCode;
    @ApiModelProperty("客户开户银行，当收付途径为03－银行转帐时非空")
    private String clientBankName;
    @ApiModelProperty("客户证件号码")
    private String clientCertificateNo;
    @ApiModelProperty("证件类型")
    private String clientCertificateType;
    private String clientMobile;
    private String clientName;
    private String clientType;
    private String provinceName;
    private String regionCode;
    @ApiModelProperty("支付信息状态（是否有效）0=有效  1＝作废  2=暂存")
    private String paymentInfoStatus;
    private Date validBeginDate;
    private Date validEndDate;
    @ApiModelProperty("备注/附言")
    private String remark;
    @ApiModelProperty("是否合并支付(Y-是，N-否)")
    private String isMergePay;
    @ApiModelProperty("附件组id")
    private String documentGroupId;
    @ApiModelProperty("数据来源，区分理赔各子领域以及其新老数据，默认n代表新车险理赔数据，c代码原车险理赔数据，np代表新财产险理赔数据，op代表原财产险理赔数据，a代表新农险数据")
    private String migrateFrom;
    @ApiModelProperty("柜面支付原因编码,对应clm_common_parameter表中collection_code=GMZFYY")
    private String counterPaymentReason;
    @ApiModelProperty("柜面支付其他原因")
    private String otherReason;
    @ApiModelProperty("数据来源 01:好车主APP; 02:好车主App,已被理赔系统更新; 03:财产险淘宝质保险;04:PA18官网;05:PTS")
    private String dataSource;
    @ApiModelProperty("领款人账号类型为公司时有值，组织机构代码：机构类型为一般机构时存社会统一信用代码，机构类型为其他时存经营证件号码")
    private String organizeCode;
    @ApiModelProperty("一般纳税人标识")
    private String isTaxpayer;
    @ApiModelProperty("客户是否同意马上支付到修理厂(Y:同意 N:不同意 默认为空)")
    private String isAgreeImmediatelyPay;
    @ApiModelProperty("支付用途，P1-赔款，P2-费用")
    private String paymentUsage;
    @ApiModelProperty("归档时间")
    private Date archiveDate;
    @ApiModelProperty("合并策略,H-小时,D -按天,D_BY_DEPT-按天分机构合并,W -按周 W_BY_DEPT-按周按机构")
    private String mergeStrategy;

    @ApiModelProperty("开户行明细")
    private String bankDetail ;
    @ApiModelProperty("城市编码")
    private String cityCode ;

    @ApiModelProperty("与被保人关系")
    private String clientRelation;

    @ApiModelProperty("开户行明细码")
    private String bankDetailCode;

    /**
     * 机构类型
     */
    @ApiModelProperty("机构类型")
    private String agencyType;

    /**
     * 客户号
     */
    @ApiModelProperty("客户号")
    private String customerNo;

    @ApiModelProperty("公司证件类型/企业证件类型：CertificateTypeEnum枚举类，统一社会信用代码-610099，组织机构代码-610001，税务登记证-610007，营业执照-610005，其他证件-619999")
    private String companyCardType;

    @ApiModelProperty("省的中文名给前端展示用")
    private String proName;
    @ApiModelProperty("市的中文名给前端展示用")
    private String ciName;
    @ApiModelProperty("县的中闻名给前端展示用")
    private String countryName;

    @ApiModelProperty(value = "微信/美团点评 openId")
    private String openId;

    @ApiModelProperty(value = "支付方式1-微信零钱，2-银行转账, 3-美团点评")
    private String payType;

    @ApiModelProperty(value = "国籍：洲编码（如亚洲-02）")
    private String areaCode;

    @ApiModelProperty(value = "国籍：数字编码（如中国-156）")
    private String nationCode;

    @ApiModelProperty(value = "职业类别大类：数字编码，如001")
    private String professionMaximumCode;

    @ApiModelProperty(value = "职业类别中类：数字编码，如00101")
    private String professionMediumCode;

    @ApiModelProperty(value = "职业类别小类：数字编码，如00101001")
    private String professionMinimumCode;

    @ApiModelProperty(value = "证件生效时间（格式：YYYY-MM-DD）")
    private String certificateEffectiveDate;

    @ApiModelProperty(value = "证件失效时间，长期标识为9999-12-31")
    private String certificateExpireDate;

    @ApiModelProperty(value = "性别：F-女性，M-男性")
    private String gender;

    @ApiModelProperty(value = "收入类型：1-5万以下，2-5到15万，3-15万以上，4-其他（200字符限制）")
    private String incomeCode;

    @ApiModelProperty(value = "收入文本：与收入类型对应（200字符限制）")
    private String incomeText;

    @ApiModelProperty(value = "工作单位")
    private String company;

    @ApiModelProperty(value = "地址")
    private String address;

}
