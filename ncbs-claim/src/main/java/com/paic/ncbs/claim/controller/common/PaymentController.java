package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.settle.PaymentCompensateVO;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api(tags = "送收付")
@RestController
@RequestMapping("/public/payment")
public class PaymentController {

    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Autowired
    private PaymentInfoService paymentInfoService;

    @ApiOperation("送收付补推")
    @PostMapping(value = "/compensate")
    public ResponseResult<Object> paymentCompensate(@RequestBody (required = false) List<PaymentCompensateVO> paymentCompensateVOList) {
        if (ListUtils.isNotEmpty(paymentCompensateVOList)) {
            for (PaymentCompensateVO paymentCompensateVO : paymentCompensateVOList) {
                payInfoNoticeThirdPartyCoreSAO.noticePayment(paymentCompensateVO.getReportNo(),paymentCompensateVO.getCaseTimes(),
                        paymentCompensateVO.getPaySerialNo(), paymentCompensateVO.isVerifyFirstPay(), paymentCompensateVO.isPrePay());
            }
        }
        return ResponseResult.success();
    }

    @ApiOperation("退货险合并支付送收付补推")
    @GetMapping(value = "/mergePayCompensate")
    public ResponseResult<Object> mergePayCompensate(@RequestParam("failDate")
                                                         String failDate) {
        if(StringUtils.isBlank(failDate)){
            throw new GlobalBusinessException("失败日期不能为空");
        } else {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            try {
                Date date = format.parse(failDate);
                paymentInfoService.mergePayCompensate(date);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return ResponseResult.success();
    }
}
