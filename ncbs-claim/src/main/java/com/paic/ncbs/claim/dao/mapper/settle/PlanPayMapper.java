package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.EpcisPlanDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PlanPayMapper extends BaseDao<PlanPayDTO> {

    PlanPayDTO getById(@Param("idAhcsPlanPay") String id);

    List<PlanPayDTO> getByPolicy(PolicyPayDTO policy);

    List<PlanPayDTO> getPlanByIdPolicy(@Param("idAhcsPolicyInfo") String idAhcsPolicyInfo);

    List<PlanPayDTO> getFromCopyByPolicy(PolicyPayDTO policy);

    int insertPlanPayInfo(@Param("list") List<PlanPayDTO> list);

    void updatePlanPayInfoList(PlanPayDTO planPayArr);

    void updatePlanPayInfoListForTPAReSettle(@Param("list") List<PlanPayDTO> planPayArr);

    void deleteByBatchId(@Param("idAhcsBatch") String idAhcsBatch, @Param("claimType") String claimType);

    List<PlanPayDTO> getPlanPayInfoByReportNoList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<PlanPayDTO> getPlanPayInfoList(PlanPayDTO planPayDTO);

    List<EpcisPlanDTO> getPlanDutyDetailByPolicyNo(MaxPayParam maxPayParam);

    Integer getChaseSubTimes(@Param("reportNo") String reportNo);

    List<PlanPayDTO> getPlanPayInfoByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes, @Param("claimType") String claimType);

    List<PlanPayDTO> getPlanPayInfoByCaseNoTimes(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

    List<PlanPayDTO> getCasePlanPayInfoByCaseNo(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo,
                                                @Param("caseTimes") Integer caseTimes, @Param("claimType") String claimType);

    List<PlanPayDTO> getPlanPayInfoByCase(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo,
                                          @Param("caseTimes") Integer caseTimes, @Param("claimType") String claimType);

    List<PlanPayDTO> getEstimatePlanList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                @Param("caseNo") String caseNo, @Param("estimateType") String estimateType);

    List<PlanPayDTO> getEstimateNullPlanList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                    @Param("caseNo") String caseNo, @Param("estimateType") String estimateType);

    String getPlanCodeByIdPolicy(@Param("idAhcsPlanPay") String idAhcsPlanPay);

    List<PlanPayDTO> getIdPlanByIdPolicy(@Param("idAhcsPolicyInfo") String idAhcsPolicyInfo);

    List<String> getTypeOfCaseNo(@Param("caseNo") String caseNo);
}
