package com.paic.ncbs.claim.service.pay.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.pay.CompensationIntermediateDataMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.ClmsPayModifyRecordDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.pay.CompensationIntermediateData;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.PayFaildInfoDto;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.pay.ClmsPayModifyRecordService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.settle.SettleValidateService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PaymentItemServiceImpl implements PaymentItemService {
    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @Autowired
    private SettleValidateService settleValidateService;

    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private ClmsPayModifyRecordService clmsPayModifyRecordService;

    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;

    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @Autowired
    @Lazy
    private VerifyService verifyService;

    @Autowired
    private CompensationIntermediateDataMapper compensationIntermediateDataMapper;

    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Override
    public void addPaymentItem(PaymentItemDTO paymentItemDTO) {
        setDefaultValue(paymentItemDTO);
        paymentItemMapper.addPaymentItem(paymentItemDTO);
    }

    @Override
    public List<PaymentItemDTO> getPaymentItem(PaymentItemDTO paymentItemDTO) {
        List<PaymentItemDTO> payItemList = paymentItemMapper.getPaymentItem(paymentItemDTO);
        if(ListUtils.isEmptyList(payItemList)){
            return new ArrayList<>();
        }
        payItemList.forEach(dto->{
            dto.setPaymentTypeName(PaymentTypeEnum.getName(dto.getPaymentType()));
            if("0".equals(dto.getCollectPaySign())){
                dto.setCollectPaySignName("收款");
            }else{
                dto.setCollectPaySignName("付款");
            }
            });
        return payItemList;
    }

    @Override
    public List<PaymentItemDTO> getHisPaymentItem(PaymentItemDTO paymentItemDTO) {
        List<PaymentItemDTO> payItemList = paymentItemMapper.getHisPaymentItem(paymentItemDTO);
        if(ListUtils.isEmptyList(payItemList)){
            return new ArrayList<>();
        }
        payItemList.forEach(dto->{
            dto.setPaymentTypeName(PaymentTypeEnum.getName(dto.getPaymentType()));
            if("0".equals(dto.getCollectPaySign())){
                dto.setCollectPaySignName("收款");
            }else{
                dto.setCollectPaySignName("付款");
            }
        });
        return payItemList;
    }

    @Override
    public void updatePaymentItem(PaymentItemDTO paymentItemDTO) {
        if(null == paymentItemDTO.getUpdatedBy()){
            paymentItemDTO.setUpdatedBy(BaseConstant.SYSTEM);
        }
        paymentItemMapper.updatePaymentItem(paymentItemDTO);
    }

    @Override
    public void updateCompensateNoPaymentItem(String reportNo,Integer caseTimes,String claimType) {
        // 同一个保单的 费用和 赔款的计算书号保持一致
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
        // 不区分
//        paymentItemDTO.setClaimType(claimType);
        List<PaymentItemDTO> allItems = paymentItemMapper.getPaymentItem(paymentItemDTO);
        Map<String, List<PaymentItemDTO>> itemGroupByType = allItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getPolicyNo));
        itemGroupByType.forEach((k,v) ->{
            String compensateNo = v.get(0).getCompensateNo();
            v.forEach(paymentItemDTO1 -> {
                paymentItemDTO1.setCompensateNo(compensateNo);
                this.updatePaymentItem(paymentItemDTO1);
            });
        });
    }

    @Override
    public void failurePaymentItem(PaymentItemDTO paymentItemDTO) {
        //失效
        paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_90);
        if(null == paymentItemDTO.getUpdatedBy()){
            paymentItemDTO.setUpdatedBy(BaseConstant.SYSTEM);
        }
        paymentItemMapper.updatePaymentItem(paymentItemDTO);
    }

    @Override
    public void delPaymentItem(PaymentItemDTO paymentItemDTO){
        //删除
        paymentItemMapper.delPaymentItem(paymentItemDTO);
    }

    @Override
    public List<PaymentItemDTO> addPaymentItemList(List<PaymentItemDTO> paymentItemList) {
        if(ListUtils.isEmptyList(paymentItemList)){
            return null;
        }

        for(PaymentItemDTO p :paymentItemList){
            setDefaultValue(p);
        }
        paymentItemMapper.addPaymentItemList(paymentItemList);
        return paymentItemList;

    }

    @Override
    public List<PaymentItemDTO> addPaymentItemDataList(List<PaymentItemComData> paymentItemDataList) {
        if(ListUtils.isEmptyList(paymentItemDataList)){
            return null;
        }
        List<PaymentItemDTO> paymentItemList = paymentItemDataList.stream().map(PaymentItemComData::convertToDTO).collect(Collectors.toList());
        return addPaymentItemList(paymentItemList);

    }

    private void setDefaultValue(PaymentItemDTO paymentItemDTO){
        if(null == paymentItemDTO.getPaymentItemStatus()){
            paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
        }
        paymentItemDTO.setIdClmPaymentItem(UuidUtil.getUUID());

        if(null == paymentItemDTO.getCreatedBy()){
            paymentItemDTO.setCreatedBy(BaseConstant.SYSTEM);
        }
        if(null == paymentItemDTO.getUpdatedBy()){
            paymentItemDTO.setUpdatedBy(BaseConstant.SYSTEM);
        }
        if(null == paymentItemDTO.getMigrateFrom()){
            paymentItemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        }
        if(null == paymentItemDTO.getMergeSign()){
            paymentItemDTO.setMergeSign(Constants.PAYMENT_ITEM_MERGE_01);
        }
        if(null == paymentItemDTO.getIdClmPaymentInfo()){
            LogUtil.info("支付信息ID为空");
            throw new GlobalBusinessException("支付信息ID为空");
        }
        if(null == paymentItemDTO.getClientBankCode()){
            String bankCode = commonParameterMapper.getBankCodeByName(paymentItemDTO.getClientBankName());
            paymentItemDTO.setClientBankCode(bankCode);
        }
    }

    @Override
    public List<PaymentItemComData> splitCoinsureItem(List<CoinsureInfoDTO> coinsureList, List<PaymentItemComData> payItemList) {
        if(ListUtils.isEmptyList(coinsureList) || ListUtils.isEmptyList(payItemList)){
            //不是共保保单或者支付项为空
            return payItemList;
        }
        //按保单号分组
        Map<String,List<CoinsureInfoDTO>> coinsureMap = coinsureList.stream().collect(Collectors.groupingBy(CoinsureInfoDTO::getPolicyNo));
        //按保单号分组
        Map<String,List<PaymentItemComData>> payItemMap = payItemList.stream().collect(Collectors.groupingBy(PaymentItemComData::getPolicyNo));

        List<PaymentItemComData> result = new ArrayList<>();
        for(Map.Entry<String,List<PaymentItemComData>> entry : payItemMap.entrySet()){
            String policyNo = entry.getKey();
            List<PaymentItemComData> tempItemList = entry.getValue();
            CoinsureInfoDTO coinsureInfoDTO = Optional.ofNullable(coinsureMap.get(policyNo)).orElse(new ArrayList<>()).get(0);
            //共保信息为空不分摊
            if(null == coinsureInfoDTO || ListUtils.isEmptyList(coinsureInfoDTO.getCoinsureDtos())){
                result.addAll(tempItemList);
                continue;
            }

            //校验共保比例之和是否等于1
            settleValidateService.checkCoinsureRadio(coinsureInfoDTO);

            result.addAll(doSplit(coinsureInfoDTO.getCoinsureDtos(),tempItemList));
        }

        return result;
    }

    /**
     * 按共保比例拆分支付项
     * @param coinsureDTOList
     * @param paymentItemComDataList
     * @return
     */
    private List<PaymentItemComData> doSplit(List<CoinsureDTO> coinsureDTOList, List<PaymentItemComData> paymentItemComDataList){
        //主共保
        CoinsureDTO leader = null;
        //从共保
        List<CoinsureDTO> followList = new ArrayList<>();
        //从共代付
        for (CoinsureDTO coinsureDTO : coinsureDTOList) {
            if("1".equals(coinsureDTO.getAcceptInsuranceFlag())){
                leader = coinsureDTO;
            }else{
                followList.add(coinsureDTO);
            }
        }
        List<PaymentItemComData> result = new ArrayList<>();
        for (PaymentItemComData itemComData : paymentItemComDataList) {
            //总金额的支付项
//            result.add(itemComData);
            //支付项总金额
            BigDecimal totalAmt = itemComData.getPaymentAmount();
            //主共保支付项金额=主共比例*支付项总金额   仅保留2位小数,舍弃多余的小数。
            BigDecimal leaderAmt = leader.getReinsureScale().multiply(totalAmt).setScale(2,BigDecimal.ROUND_DOWN);
            result.add(createdPaymentItem(leaderAmt,itemComData));

            //从共保代付支付项总金额
            BigDecimal followAmt = totalAmt.subtract(leaderAmt);
            result.add(createdPaymentItem(followAmt,itemComData));

            //从共摊回的中间合计金额，用于归一化
            BigDecimal addAmt = BigDecimal.ZERO;
            BigDecimal followSplitAmt = null;
            for(int i=0;i<followList.size();i++){
                if(i == followList.size()-1){
                    //最后一项做归一化，用总额-前面N项之和
                    followSplitAmt = followAmt.subtract(addAmt);
                }else{
                    //从共摊回金额=从共比例*支付项总金额  仅保留2位小数,舍弃多余的小数。
                    followSplitAmt = followList.get(i).getReinsureScale().multiply(totalAmt).setScale(2,BigDecimal.ROUND_DOWN);
                    //累加从共摊回金额
                    addAmt = addAmt.add(followSplitAmt);
                }

                result.add(createdPaymentItem(followSplitAmt,itemComData));
            }

        }

        return result;
    }

    private PaymentItemComData createdPaymentItem(BigDecimal payAmount,PaymentItemComData paymentItemComData){
        PaymentItemComData generatedPaymentItem = new PaymentItemComData();
        BeanUtils.copyProperties(paymentItemComData,generatedPaymentItem);
        generatedPaymentItem.setPaymentAmount(payAmount);
        return generatedPaymentItem;
    }

    @Override
    public List<PaymentItemComData> getPaymentItemList(PaymentItemComData paymentItemComData) {
        PaymentItemDTO dto = new PaymentItemDTO();
        BeanUtils.copyProperties(paymentItemComData,dto);
        List<PaymentItemDTO> paymentItemDTOList = getPaymentItem(dto);
        return exchangeComDataList(paymentItemDTOList);
    }

    @Override
    public List<PaymentItemDTO> paydInfo(PaymentItemDTO exchangePaymentItem) {
        return null;
    }

    private List<PaymentItemComData> exchangeComDataList(List<PaymentItemDTO> paymentItemList){
        if(ListUtils.isEmptyList(paymentItemList)){
            return Collections.EMPTY_LIST;
        }
        List<PaymentItemComData> dataList = new ArrayList<>();
        for (PaymentItemDTO dto:paymentItemList){
            dataList.add(exchangeComData(dto));
        }
        return dataList;
    }

    private PaymentItemComData exchangeComData(PaymentItemDTO paymentItemDTO){
        PaymentItemComData comData = new PaymentItemComData();
        BeanUtils.copyProperties(paymentItemDTO,comData);
        return comData;
    }

    @Override
    public void updatePaymentItemStatus(PaymentItemDTO paymentItemDTO) {
        paymentItemMapper.updatePaymentItemStatus(paymentItemDTO);
    }

    @Override
    public List<PaymentItemDTO> getPrePaymentItem(PaymentItemDTO paymentItemDTO) {
        return paymentItemMapper.getPrePaymentItem(paymentItemDTO);
    }

    @Override
    @Transactional
    public void modifyPayBackItem(PaymentInfoVO paymentInfoVO, PaymentItemDTO itemDTO) {
        if(Objects.equals("1",paymentInfoVO.getPayType())){
            if(StrUtil.isEmptyIfStr(paymentInfoVO.getOpenId())){
                throw new GlobalBusinessException("领款方式为微信零钱，必须录入OpenID！");
            }
        }
        if(null == itemDTO.getClientBankCode()){
            String bankCode = commonParameterMapper.getBankCodeByName(itemDTO.getClientBankName());
            itemDTO.setClientBankCode(bankCode);
        }
        String userId = StringUtils.isBlank(WebServletContext.getUserId()) ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        //修改：收款方类型、收款人姓名，案件流转至“支付修改审批“池
        if (!paymentInfoVO.getClientType().equals(itemDTO.getClientType()) || !paymentInfoVO.getClientName().equals(itemDTO.getClientName())) {
            // 挂起支付修改任务
            bpmService.suspendOrActiveTask_oc(itemDTO.getReportNo(), itemDTO.getCaseTimes(), BpmConstants.OC_PAY_BACK_MODIFY, true, itemDTO.getIdClmPaymentItem());

            //操作记录
            operationRecordService.insertOperationRecord(itemDTO.getReportNo(), BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, "发起", null, userId);
            // 生成支付修改审批任务
            bpmService.startProcessOc(itemDTO.getReportNo(), itemDTO.getCaseTimes(), BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, itemDTO.getIdClmPaymentItem() +"_REVIEW", null, WebServletContext.getDepartmentCode());

            String jsonString = JSON.toJSONString(paymentInfoVO);
            itemDTO.setModifyInfo(jsonString);
            this.updatePaymentItem(itemDTO);
        } else {
            BeanUtils.copyProperties(paymentInfoVO, itemDTO);
            itemDTO.setCityName(paymentInfoVO.getCityCode());
            itemDTO.setUpdatedBy(WebServletContext.getUserId());
            itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
            this.updatePaymentItem(itemDTO);
            // 调用二次支付接口
            payInfoNoticeThirdPartyCoreSAO.noticePayment(itemDTO.getReportNo(), itemDTO.getCaseTimes(),itemDTO.getIdClmPaymentItem(), false, false);
            bpmService.completeTask_oc(itemDTO.getReportNo(),itemDTO.getCaseTimes(), BpmConstants.OC_PAY_BACK_MODIFY,itemDTO.getIdClmPaymentItem());
            //操作记录
            operationRecordService.insertOperationRecord(itemDTO.getReportNo(), BpmConstants.OC_PAY_BACK_MODIFY, "提交", null, userId);
        }
    }

    @Override
    @Transactional
    public void submitPayBackModifyReview(String idClmPaymentItem, ClmsPayModifyRecordDTO clmsPayModifyRecordDTO, PaymentItemDTO itemDTO) {
        String reportNo = itemDTO.getReportNo();
        Integer caseTimes = itemDTO.getCaseTimes();

        clmsPayModifyRecordDTO.setIdClmsPayModifyRecord(UuidUtil.getUUID());
        String applyUm = clmsPayModifyRecordDTO.getApplyUm();
        clmsPayModifyRecordDTO.setApplyUm(applyUm);
        clmsPayModifyRecordDTO.setIdClmPaymentItem(idClmPaymentItem);
        clmsPayModifyRecordDTO.setAuditUm(WebServletContext.getUserName());
        clmsPayModifyRecordDTO.setAuditDate(new Date());
        clmsPayModifyRecordDTO.setUpdatedBy(WebServletContext.getUserId());
        clmsPayModifyRecordDTO.setCreatedBy(WebServletContext.getUserId());
        clmsPayModifyRecordService.addClmsPayModifyRecordDTO(clmsPayModifyRecordDTO);

        String modifyInfo = itemDTO.getModifyInfo();
        PaymentInfoVO paymentInfoVO = JSON.parseObject(modifyInfo, PaymentInfoVO.class);
        if ("2".equals(clmsPayModifyRecordDTO.getAuditOpinion())){
            // 重启支付修改任务
            bpmService.suspendOrActiveTask_oc(reportNo, caseTimes, BpmConstants.OC_PAY_BACK_MODIFY,false, idClmPaymentItem);

            // 审批不同意，通知渠道
            log.info("PaymentItemServiceImpl.submitPayBackModifyReview disagree, idClmPaymentItem={}", itemDTO.getIdClmPaymentItem());
            PayFaildInfoDto payFaildInfo = new PayFaildInfoDto();
            payFaildInfo.setPaySerialNo(itemDTO.getIdClmPaymentItem());
            payFaildInfo.setPayInfo(clmsPayModifyRecordDTO.getAuditRemark());
            payFaildInfo.setProvinceCode(StringUtils.isNotBlank(paymentInfoVO.getProvinceCode()) ? paymentInfoVO.getProvinceCode() : itemDTO.getProvinceName());
            payFaildInfo.setCityCode(StringUtils.isNotBlank(paymentInfoVO.getCityCode()) ? paymentInfoVO.getCityCode() : itemDTO.getCityName());
            payFaildInfo.setRegionCode(StringUtils.isNotBlank(paymentInfoVO.getRegionCode()) ? paymentInfoVO.getRegionCode() : itemDTO.getRegionCode());
            payFaildInfo.setBankAccountAttribute(StringUtils.isNotBlank(paymentInfoVO.getBankAccountAttribute()) ? paymentInfoVO.getBankAccountAttribute() : itemDTO.getBankAccountAttribute());
            payFaildInfo.setBankDetail(StringUtils.isNotBlank(paymentInfoVO.getBankDetail()) ? paymentInfoVO.getBankDetail() : itemDTO.getBankDetail());
            payFaildInfo.setClientBankAccount(StringUtils.isNotBlank(paymentInfoVO.getClientBankAccount()) ? paymentInfoVO.getClientBankAccount() : itemDTO.getClientBankAccount());
            payFaildInfo.setClientBankCode(StringUtils.isNotBlank(paymentInfoVO.getClientBankCode()) ? paymentInfoVO.getClientBankCode() : itemDTO.getClientBankCode());
            payFaildInfo.setClientBankName(StringUtils.isNotBlank(paymentInfoVO.getClientBankName()) ? paymentInfoVO.getClientBankName() : itemDTO.getClientBankName());
            payFaildInfo.setClientCertificateNo(StringUtils.isNotBlank(paymentInfoVO.getClientCertificateNo()) ? paymentInfoVO.getClientCertificateNo() : itemDTO.getClientCertificateNo());
            payFaildInfo.setClientCertificateType(StringUtils.isNotBlank(paymentInfoVO.getClientCertificateType()) ? paymentInfoVO.getClientCertificateType() : itemDTO.getClientCertificateType());
            payFaildInfo.setClientType(StringUtils.isNotBlank(paymentInfoVO.getClientType()) ? paymentInfoVO.getClientType() : itemDTO.getClientType());
            payFaildInfo.setClientName(StringUtils.isNotBlank(paymentInfoVO.getClientName()) ? paymentInfoVO.getClientName() : itemDTO.getClientName());
            payFaildInfo.setClientMobile(StringUtils.isNotBlank(paymentInfoVO.getClientMobile()) ? paymentInfoVO.getClientMobile() : itemDTO.getClientMobile());
            payFaildInfo.setCollectPayApproach(StringUtils.isNotBlank(paymentInfoVO.getCollectPayApproach()) ? paymentInfoVO.getCollectPayApproach() : itemDTO.getCollectPayApproach());

            SyncCaseStatusDto syncCaseStatusDto = new SyncCaseStatusDto();
            syncCaseStatusDto.setReportNo(reportNo);
            syncCaseStatusDto.setCaseTimes(caseTimes);
            syncCaseStatusDto.setCaseStatus(SyncCaseStatusEnum.PAYFAILD);
            syncCaseStatusDto.setPayFaildInfo(payFaildInfo);
            mqProducerSyncCaseStatusService.syncCaseStatus(syncCaseStatusDto);
            //操作记录
            operationRecordService.insertOperationRecordByLabour(itemDTO.getReportNo(), BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, "不通过", clmsPayModifyRecordDTO.getAuditRemark());
            //支付修改审批不同意添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_PAY_BACK_MODIFY);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(caseTimes);
            TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes,
                    BpmConstants.OC_PAY_BACK_MODIFY);
            if (taskInfoDTO != null){
                noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
            }
        } else{
            BeanUtils.copyProperties(paymentInfoVO, itemDTO);
            itemDTO.setCityName(paymentInfoVO.getCityCode());
            itemDTO.setProvinceName(paymentInfoVO.getProvinceCode());
            itemDTO.setUpdatedBy(WebServletContext.getUserId());
            itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
            itemDTO.setReportNo(reportNo);
            itemDTO.setCaseTimes(caseTimes);
            itemDTO.setIdClmPaymentItem(idClmPaymentItem);
            if(null == itemDTO.getClientBankCode()){
                String bankCode = commonParameterMapper.getBankCodeByName(itemDTO.getClientBankName());
                itemDTO.setClientBankCode(bankCode);
            }
            this.updatePaymentItem(itemDTO);
            // 调用二次支付接口
            payInfoNoticeThirdPartyCoreSAO.noticePayment(reportNo, caseTimes, idClmPaymentItem, false, false);
            //操作记录
            operationRecordService.insertOperationRecordByLabour(itemDTO.getReportNo(), BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, "通过", clmsPayModifyRecordDTO.getAuditRemark());
        }
        bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_PAY_BACK_MODIFY_REVIEW,idClmPaymentItem + "_REVIEW");
    }

    @Override
    public int getPaymentItemCount(PaymentItemDTO itemDTO) {
        return paymentItemMapper.getPaymentItemCount(itemDTO);
    }

    @Override
    public List<PaymentItemDTO> getPaymentItemByReportNo(PaymentItemDTO paymentItemDTO) {
        List<PaymentItemDTO> payItemList = paymentItemMapper.getPaymentItemByReportNo(paymentItemDTO);
        if(ListUtils.isEmptyList(payItemList)){
            return new ArrayList<>();
        }
        return payItemList;
    }

    /**
     * 获取所有的赔付信息
     * @param paymentItemDTO 入参
     * @return
     */
    @Override
    public List<PaymentItemDTO> getPaymentItemByReportNoAndPayType(PaymentItemDTO paymentItemDTO) {
        return paymentItemMapper.getPaymentItemPayByReportNoAndPayType(paymentItemDTO);
    }

    @Override
    public BigDecimal getLastPaymentAmount(PaymentItemDTO paymentItemDTO) {
        return paymentItemMapper.getLastPaymentAmount(paymentItemDTO);
    }

    @Override
    public void checkAndPayFee(String reportNo, Integer caseTimes) {
        try {
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setCaseTimes(caseTimes);
            paymentItemDTO.setClaimType("1");
            List<PaymentItemDTO> allItems = this.getPaymentItem(paymentItemDTO);
            // 没有费用直接返回
            if(CollectionUtils.isEmpty(allItems)){
                return;
            }
            List<EstimatePolicyDTO> estimatePolicyDTOS = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
            verifyService.splitPlanAndDuty4Fee(estimatePolicyDTOS,allItems);
            //通知收付费
            payInfoNoticeThirdPartyCoreSAO.noticePayment(reportNo,caseTimes,null, false, false);
            //零结和拒赔 保存监管赔付中间表费用数据。注销 只记录赔款，赔款总金额为上一次费用，变化量为0
            saveCompensationIntermediateData(reportNo, caseTimes);
        } catch (Exception e) {
            log.error("费用支付异常-",e);
        }
    }

    @Override
    public void saveCompensationIntermediateData(String reportNo, Integer caseTimes) {
        /*String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM :WebServletContext.getUserId() ;
        // 场景:
        // 1. 第一次 正常结案赔付，会记录赔款和费用到赔付中间表。 第二次 重开正常赔付记录赔付中间表
        // 2. 第一次 正常结案赔付，会记录赔款和费用到赔付中间表。 第二次 重开零结、注销、拒赔， 赔款也记录赔付中间表，查询上一次的赔款，总金额一样，变化量为0
        // 3. 第一次 进行零结、注销、拒赔， 赔款不记录赔付中间表，如果有费用则记录赔付中间表。第二次 重开后如有赔款和费用，则记录赔付中间表
        // 4. 第一次 进行零结、注销、拒赔， 赔款不记录赔付中间表，如果有费用则记录赔付中间表。第二次 重开后进行零结、注销、拒赔，赔款不记录中间表，如果有费用则只记录费用。

        //1. 查询赔付和费用数据
        List<CompensationIntermediateData> intermediateDataList = paymentItemMapper.getCompensationIntermediateData(reportNo, caseTimes);
        //2.查出以前监管赔付中间表责任数据  key：保单号+报案号+险种+责任+赔付类型标志  value：每个责任对象
        Map<String, CompensationIntermediateData> oldIntermediateDataMap = getCompensationIntermediateDataMap(reportNo);

        if (CollectionUtils.isEmpty(intermediateDataList) && oldIntermediateDataMap.isEmpty()) {
            log.info("未查到监管所需赔付数据和赔付中间表数据 报案号:{}, 赔付次数:{}", reportNo, caseTimes);
            return;
        }
        //3.如果重开，进行零结、注销、拒赔，并且没有费用场景： 需记录，根据intermediateDataList为空判断是否为零结、注销、拒赔
        log.info("赔付和费用数据={}", JsonUtils.toJsonString(intermediateDataList));
        if (caseTimes > 1 && !oldIntermediateDataMap.isEmpty() && intermediateDataList.isEmpty()){
            intermediateDataList = new ArrayList<>();
            log.info("saveCompensationIntermediateData={},intermediateDataList={}", JsonUtils.toJsonString(oldIntermediateDataMap),JsonUtils.toJsonString(oldIntermediateDataMap));
            for (String  key : oldIntermediateDataMap.keySet()){
                CompensationIntermediateData data = oldIntermediateDataMap.get(key);
                data.setPayAmountChange(new BigDecimal(0));
                data.setPayDate(new Date());
                data.setCaseTimes(caseTimes);
                data.setCreatedBy(loginUm);
                data.setUpdatedBy(loginUm);
                data.setId(UuidUtil.getUUID());
                intermediateDataList.add(data);
            }
            compensationIntermediateDataMapper.batchInsert(intermediateDataList);
            return;
        }
        //4. 第一次赔付、重开场景，有赔付或费用 都进行记录
        try {
            StringBuilder key;
            for (CompensationIntermediateData intermediateData : intermediateDataList) {
                //取出上一次估损赔款数据
                key = new StringBuilder();
                key.append(intermediateData.getPolicyNo()).append("|")
                        .append(reportNo).append("|")
                        .append(intermediateData.getPlanCode()).append("|")
                        .append(intermediateData.getDutyCode()).append("|")
                        .append(intermediateData.getPayFlag());
                CompensationIntermediateData oldIntermediateData = oldIntermediateDataMap.get(key.toString());
                //如果是初次，赔付中间表无数据，金额为 0
                BigDecimal oldPayAmount = ObjectUtils.isEmpty(oldIntermediateData) ? new BigDecimal(0) : oldIntermediateData.getPayAmount();
                BigDecimal newPayAmountChange = new BigDecimal(0);
                if (PaymentInfoTypeEnum.INDEMNITY.getType().equals(intermediateData.getPayFlag())) {
                    //如果是赔款，计算本次与上一次的赔款变化量
                    newPayAmountChange = intermediateData.getPayAmount().subtract(oldPayAmount);
                } else if (PaymentInfoTypeEnum.COST.getType().equals(intermediateData.getPayFlag())) {
                    //如果是费用,变化量就是本次金额
                    newPayAmountChange = intermediateData.getPayAmount();
                    //费用总金额 = 上次总金额 + 本次金额
                    intermediateData.setPayAmount(oldPayAmount.add(intermediateData.getPayAmount()));
                } else {
                    log.error("paymentType存在不是1J和13的类型,报案号:{}", intermediateData.getReportNo());
                    continue;
                }
                //组装赔付费用数据
                intermediateData.setPayAmountChange(newPayAmountChange);
                intermediateData.setCreatedBy(loginUm);
                intermediateData.setUpdatedBy(loginUm);
                intermediateData.setId(UuidUtil.getUUID());
            }
            compensationIntermediateDataMapper.batchInsert(intermediateDataList);
        }catch (Exception e){
            log.error("保存监管中间表处理异常",e);
        }*/

    }

    /**
     * 查出以前赔付表责任数据
     * @param reportNo
     * @return
     */
    private Map<String, CompensationIntermediateData> getCompensationIntermediateDataMap(String reportNo) {
        List<CompensationIntermediateData> oldIntermediateDataList = compensationIntermediateDataMapper.queryListByCondition(
                CompensationIntermediateData.builder()
                        .reportNo(reportNo)
                        .build());
        Map<String, CompensationIntermediateData> map = new HashMap<>();
        if (CollectionUtils.isEmpty(oldIntermediateDataList)){
            return map;
        }
        //获取每个责任下最新的赔付数据
        StringBuilder key ;
        for (CompensationIntermediateData data : oldIntermediateDataList){
            key = new StringBuilder();
            key.append(data.getPolicyNo()).append("|")
                    .append(data.getReportNo()).append("|")
                    .append(data.getPlanCode()).append("|")
                    .append(data.getDutyCode()).append("|")
                    .append(data.getPayFlag());
            if (map.containsKey(key.toString())){
                CompensationIntermediateData temp = map.get(key.toString());
                //获取最新一条数据
                if(temp.getUpdatedDate().compareTo(data.getUpdatedDate()) < 0){
                    map.put(key.toString(),data);
                }
            }else {
                map.put(key.toString(),data);
            }
        }
        return map;
    }

    @Override
    public void delPaymentItemByInfo(String idClmPaymentInfo) {
        paymentItemMapper.delPaymentItemByInfo(idClmPaymentInfo);
    }

}
