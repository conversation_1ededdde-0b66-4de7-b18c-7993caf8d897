package com.paic.ncbs.claim.sao;


import com.paic.ncbs.claim.model.dto.pay.BatchPaymentDetailInfo;
import com.paic.ncbs.claim.model.dto.pay.BatchPaymentInfo;
import com.paic.ncbs.claim.model.dto.pay.FeeInvoiceSendPaymentDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;

import java.util.List;

/**
 * 第三方系统-支付对接
 * <AUTHOR>
 */
public interface PayInfoNoticeThirdPartyCoreSAO {

    /**
     * 保单批单送收付
     *
     * 新健康险理赔同步核心理赔后，调用该接口。
     */
    void noticePayment(String reportNo, Integer caseTimes, String paySerialNo, boolean isVerifyFirstPay, boolean isPrePay);

    /**
     * 理赔费用发票 送收付Q13
     * @param paymentItemDTO
     */
    void sendClaimVatInfo(PaymentItemDTO paymentItemDTO);

    /**
     * 费用发票修改发送收付费
     * @param feeInvoiceSendPaymentDTO
     */
    void feeInvoiceModifySendPayment(String reportNo, FeeInvoiceSendPaymentDTO feeInvoiceSendPaymentDTO);

    /**
     * 获取支付凭证
     * @param paymentItemDTO
     * @return 支付凭证COS地址
     */
    String queryPaymenVoucherUrl(PaymentItemDTO paymentItemDTO);

    /**
     * 合并支付
     */
    void sendMergePayment(String batchNo, BatchPaymentInfo batchPaymentInfo,
                          List<BatchPaymentDetailInfo> batchPaymentDetailInfo);

}
