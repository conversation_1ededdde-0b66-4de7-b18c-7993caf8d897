package com.paic.ncbs.claim.model.dto.ahcs;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PolicyCopySendDetail {


    private static final long serialVersionUID = 8771003733112197871L;

    /**  主键 */
    private String idAhcsPolicyInfo;

    /**  报案表里面的报案号 */
    private String reportNo;

    /**  保单号 */
    private String policyNo;

    /**  承保时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date acceptInsuranceDate;

    /**  承保机构编码 */
    private String departmentCode;


    /**  承保机构名稱 */
    private String departmentName;

    /**  保单开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceBeginTime;

    /**  保单结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceEndTime;

    /**  核保通过时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date underwriteDate;

    /**  保单状态 01退保 02注销 03退保恢复 04契撤 B5在保 */
    private String policyStatus;

    private String status;

    /**  产品代码 */
    private String productCode;

    /**  产品名称 */
    private String productName;

    /**  业务类型 1-个人 2-团体 */
    private String businessType;

    /**  应交保费 */
    private BigDecimal totalAgreePremium;

    /**  实交保费 */
    private BigDecimal totalActualPremium;

    /**  保额币种 */
    private String amountCurrencyCode;

    /**  保费币种 */
    private String premiumCurrencyCode;

    /**  数据产生源 */
    private String dataSource;

    /**  共保标志 0 非共保1 共保 */
    private String coinsuranceMark;

    /**  总保额 */
    private BigDecimal totalInsuredAmount;

    /**  组合产品再保批复编号 */
    private String replyCode;

    /**  组合产品再保批复名称 */
    private String replyName;

    /**  保单出单系统ID */
    private String systemId;

    /**  批单系统ID */
    private String endorseSystemId;

    /**  业务员代码 */
    private String saleAgentCode;

    /**  业务员姓名 */
    private String saleAgentName;

    /**  是否社保保单 1是 0 否 */
    private String socialFlag;

    /**  是否协议定义 1是 0否 */
    private String agreenmentDefine;

    /**  业务来源编码 */
    private String businessSourceCode;

    /**  业务来源编码名称 */
    private String businessSourceName;

    /**  业务来源细分编码 */
    private String businessSourceDetailCode;

    /**  业务来源细分编码名称 */
    private String businessSourceDetailName;

    /**  渠道编码 */
    private String channelSourceCode;

    /**  渠道编码名称 */
    private String channelSourceName;

    /**  渠道细分编码 */
    private String channelSourceDetailCode;

    /**  渠道细分编码名称 */
    private String channelSourceDetailName;

    /**  虚拟标的数量 大于0 说明是虚拟保单 */
    private Long virtualTargetNum;
    /**  保单有效性（Y：有效 N：无效） */
    private String policyValid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date edrEffectiveDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date returnEffectiveDate;

    /**  批改生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date effectiveDate;

    /**  批单号 */
    private String endorseNo;
    /**  投保日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date applyDate;
    /**  套餐编码 */
    private String productPackageType;
    /**  层级号 */
    private String subjectId;
    /**  版本号 */
    private String productVersion;
    /**  批次号 */
    private String batchNo;
    /**  自助卡号 */
    private String selfCardNo;
    /**  电子保单号 */
    private String policyCerNo;
    /**  上传标记（0：上传失败， 1：上传成功） */
    private String isUploadFlatSuccessed;
    /**  赔案号 */
    private String caseNo;
    /**  旧转新标识  00：PAS原始数据，01：旧转新生成的数据 */
    private String generateFlag;
    /**  标的共享保额,1：是，0：否 */
    private String shareInsuredAmount;
    /**  老系统的产品编码(旧转新用)  */
    private String orgProductCode;
    /**  老系统的产品名称(旧转新用)  */
    private String orgProductName;
    /**  是否是见费出单(1-是;0-否) */
    private String isPolicyBeforePayfee;

    /**  是否续保*/
    private String renewalType;
    /**  备注 */
    private String remark;
    /**  续保上次保单号 */
    private String lastPolicyNo;

    /**  影像ID */
    private String groupId;

    /**  班次 */
    private String trafficNo;
    /**  保单投保份数 */
    private BigDecimal applyNum;

    /**  保单表扩展领域外字段，理算解析使用，可扩展增加字段 */
    private String policyExtend;

    /**  合作伙伴编码 */
    private String partnerCode;

    /**  健康告知 */
    private String healthNotification;

    /**  缴费期数 */
    private Integer payTermNo;


    private String reportRegisterUm;


    private String relatedType;

    /**  合作伙伴名 */
    private String partnerName;

    /**  主介绍人代码 */
    private String primaryIntroducerCode;

    /**  代理人/经纪人代码 */
    private String agentBrokerCode;

    /**  代理人/经纪人类型,1:代理人,2:经纪人 */
    private String agentBrokerType;

    /**  代理人/经纪人名称 */
    private String agentBrokerName;

    /**  投保人名称*/
    private String name;

    /**  职业code*/
    private String professionCode;

    /**  职业name*/
    private String professionName;

    /** 电话*/
    private String linkManTelephone;

    /** 地址*/
    private String address;

    /** 客户类型*/
    private String clientType;

    /** 客户类别*/
    private String clientCategoty;

    /** 签约日期*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date  actualDate;

    /** 协议号*/
    private String  contractPactCode;

    /** 健康服务*/
    private String combinedName ;

    /** 再保类型*/
    private String  facultativeType ;

    /**车牌号  车辆发动机号  车架号*/
    private String vehicleLicenceCode;
    private String engineNo ;
    private String vehicleFrameNo;

    private String ocasUrl;

    private String  isFacultativeBusiness ;

    /** 是否家庭单：1-是，0-否*/
    private String isFamily;
    /**
     * 电子保单url
     */
    private String policyElectricPdfUrl;
}
