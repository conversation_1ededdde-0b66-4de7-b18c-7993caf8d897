package com.paic.ncbs.claim.service.settle.factor.impl.bill;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.BIllSettleBuildService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service
public class BIllSettleBuildServiceImpl implements BIllSettleBuildService {
    @Override
    public BIllSettleResultDTO dealBIllSettleResultDTO(EveryDayBillInfoDTO edto, DutyDetailPayDTO detailPayDTO, BIllSettleResultDTO bIllSettleResultDTO) {

        bIllSettleResultDTO.setEffectiveFlag(edto.getEffectiveFlag());
        bIllSettleResultDTO.setExceedMothPayDays(edto.getExceedMothPayDays());
        bIllSettleResultDTO.setExceedYearlyPayDays(edto.getExceedYearlyPayDays());
        bIllSettleResultDTO.setWaitFlag(edto.getWaitFlag());
        bIllSettleResultDTO.setId(edto.getId());
        bIllSettleResultDTO.setTherapyType(edto.getTherapyType());
        bIllSettleResultDTO.setBillAmount(edto.getBillAmount());
        bIllSettleResultDTO.setHospitalPropertyDes(edto.getHospitalPropertyDes());
        bIllSettleResultDTO.setHospitalCode(edto.getHospitalCode());
        bIllSettleResultDTO.setHospitalName(edto.getHospitalName());
        String settleType="";
        if(Objects.equals("N",edto.getEffectiveFlag())){
            settleType="1";//发票不在保单有效期
            return setBIllSettleResultDTO(detailPayDTO,edto,settleType,bIllSettleResultDTO,Constants.BILL_REMARK_NO_EFFECTIVE);
        }
        if(Objects.equals("Y",edto.getExceedMothPayDays())){
            settleType="3";//超每月赔付天数
            return setBIllSettleResultDTO(detailPayDTO,edto,settleType,bIllSettleResultDTO,Constants.BILL_REMARK_EXCEEDMONTH_PAY_DAY);
        }
        if(Objects.equals("Y",edto.getExceedYearlyPayDays())){
            settleType="4";//超年度赔付天数
            return setBIllSettleResultDTO(detailPayDTO,edto,settleType,bIllSettleResultDTO,Constants.EXCEE_YEAYLY_PAY_DAY);
        }
        if(Objects.equals("Y",edto.getWaitFlag())){
            settleType="2";
            return setBIllSettleResultDTO(detailPayDTO,edto,settleType,bIllSettleResultDTO,Constants.WAIT_FALG);
        }
        return bIllSettleResultDTO;

    }

    /**
     * 理算类型：0-正常理算，1-不在保单有效期，2-等待期发票，3-超每月赔付天数，4-超年度赔付天数
     * @param detailPayDTO
     * @param edto
     * @param settleType
     * @return
     */
    private BIllSettleResultDTO setBIllSettleResultDTO(DutyDetailPayDTO detailPayDTO, EveryDayBillInfoDTO edto,String settleType,BIllSettleResultDTO bIllSettleResultDTO,String remark) {
        bIllSettleResultDTO.setReportNo(detailPayDTO.getReportNo());
        bIllSettleResultDTO.setCaseTimes(detailPayDTO.getCaseTimes());
        bIllSettleResultDTO.setPolicyNo(detailPayDTO.getPolicyNo());
        bIllSettleResultDTO.setProductCode(detailPayDTO.getProductCode());
        bIllSettleResultDTO.setPlanCode(detailPayDTO.getPlanCode());
        bIllSettleResultDTO.setDutyCode(detailPayDTO.getDutyCode());
        bIllSettleResultDTO.setDutyDetailCode(detailPayDTO.getDutyDetailCode());
        bIllSettleResultDTO.setMedicalSettleFlag(edto.getMedicalSettleFlag());
        bIllSettleResultDTO.setBillNo(edto.getBillNo());
        bIllSettleResultDTO.setBillDate(edto.getBillDate());
        bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
        bIllSettleResultDTO.setRemitAmount(BigDecimal.ZERO);
        bIllSettleResultDTO.setBillNo(edto.getBillNo());
        bIllSettleResultDTO.setBillDate(edto.getBillDate());
        bIllSettleResultDTO.setSettleType(settleType);
        bIllSettleResultDTO.setFlag("1");
        bIllSettleResultDTO.setRemark(remark);
        return bIllSettleResultDTO;
    }
}
