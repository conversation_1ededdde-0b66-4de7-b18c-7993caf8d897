package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.openapi.ClmsIcdDTO;
import com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.MarketProductInfoVO;
import com.paic.ncbs.claim.service.openapi.OpenQueryIcdInfoService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/public/query")
public class OpenQueryIcdInfoController {
    @Autowired
    private OpenQueryIcdInfoService openQueryIcdInfoService;
    @Autowired
    private TaskListService taskListService;

    @Autowired
    private DepartmentDefineMapper departmentDefineMapper ;

    @Autowired
    private TaskPoolService taskPoolService;
    @PostMapping("/getCustomerIcInfo")
    public ResponseResult<ClmsIcdDTO> getReportDetailInfo(@RequestBody ReportQueryReqDTO reportQueryReqDTO){
        ClmsIcdDTO dto = openQueryIcdInfoService.getIcdInfo(reportQueryReqDTO.getClientNo());
        return ResponseResult.success(dto);
    }

    @PostMapping(value = "/getAllMarketProductInfo")
    @ApiOperation(value = "获取所有产品信息")
    public ResponseResult<List<MarketProductInfoVO>> getAllMarketProductInfo(@RequestBody Map<String, String> map) {
        List<MarketProductInfoVO> marketProductInfoVOList = taskListService.getAllMarketProductInfo(map);
        return ResponseResult.success(marketProductInfoVOList);
    }

    @PostMapping(value = "/getAllUserInfo")
    @ApiOperation(value = "获取所有理算作业人员信息")
    public ResponseResult<List<UserInfoDTO>> getAllUserInfo() throws GlobalBusinessException, NcbsException {

        List<String> childCodeList = new ArrayList<String>();
        childCodeList.add("1");
        List<String> parentCodeList = new ArrayList<String>();
        parentCodeList.add("775");
        childCodeList.addAll(departmentDefineMapper.getChildCodeList(parentCodeList));

        List<UserInfoDTO> userInfoDTO = new ArrayList<UserInfoDTO>();
        for (String departmentCode : childCodeList) {
            String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(departmentCode);
            List<UserInfoDTO> departmentUserInfoDTO = taskPoolService.searchTaskDealUser(departmentCode, BpmConstants.OC_MANUAL_SETTLE);
            departmentUserInfoDTO.forEach(e -> {
                e.setComCode(departmentCode);
                e.setComName(departmentName);
            });
            userInfoDTO.addAll(departmentUserInfoDTO);
        }
        return ResponseResult.success(userInfoDTO);

    }
}
