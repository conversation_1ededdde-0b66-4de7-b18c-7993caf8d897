package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 出险类型
 * 选项为<意外身故>、<疾病身故>、<意外残疾>、<疾病残疾>、<意外医疗>、<疾病住院医疗>、
 * <疾病门急诊医疗>、<重大疾病>、<住院补贴>、<其他>
 *
 *     后续可能会改动 IAT_1_01 中间的1代表意健险 2 代表责任险 3 代表个人财产 4 代表其他 5 代表企业财产
 *
 */
public enum InsuredApplyTypeEnum {

	ACCIDENTAL_MEDICAL_TREATMENT("IAT_1_05","意外医疗","Y07"),
	SICKNESS_HOSPITALIZATION("IAT_1_06","疾病住院医疗","Y08"),
	EMERGENCY_MEDICINE("IAT_1_07","疾病门急诊医疗","Y09"),
	MAJOR_DISEASES("IAT_1_08","重大疾病","Y10"),
	HOSPITAL_ALLOWANCE("IAT_1_09","意外津贴","Y11"),
	ACCIDENTAL_DISABILITY("IAT_1_03","意外残疾","Y05"),
	SICKNESS_AND_DISABILITY("IAT_1_04","疾病残疾","Y06"),
	ACCIDENTAL_DISEASE("IAT_1_01","意外身故","Y03"),
	DEATH_OF_DISEASE("IAT_1_02","疾病身故","Y04"),
	OTHER("IAT_1_10","其他","999"),
	EMERGENCY_MEDICINE_ALLOWANCE("IAT_1_11","疾病津贴","Y11"),

	//责任险:人身伤亡
	DUTY_BODY_HURT_OR_DIED("IAT_2_01","人身伤亡","D01"),
	//责任险财产损失
	DUTY_WEALTH_LOSS("IAT_2_02","第三者财产损失","D02"),
	//法律责任及其他
	DUTY_LAW_OR_OTHER("IAT_2_03","法律责任及其他","D03"),
	//董事责任
	DIRECTOR_LIABILITY("IAT_2_04","董事责任","D04"),

	//个人财产
	IDV_WEALTH_LOSS("IAT_3_01","个人财产损失","I01"),
	IDV_WEALTH_CASH_LOSS("IAT_3_02","现金损失","I02"),
	//其他
	OTHER_ALLOWANCE("IAT_4_01","给付型津贴","999"),
	DELAY_TYPE("IAT_4_02","延误类","B02"),
	RESCUE_TYPE("IAT_4_03","救援类","B03"),
	OTHER_TYPE("IAT_4_04","其他","B04"),

	ENTERPRISE_PROPERTY_LOSS("IAT_5_01","企业财产损失","999"),

	CREDIT_GUARANTEE("IAT_6_01","信用保证类","999"),
	FIDELITY_GUARANTEE("IAT_6_02","忠诚保证类","999"),
	OTHER_GUARANTEE("IAT_6_99","其他","999"),


	ENGINEERING_PROPERTY_LOSS("IAT_7_01","工程财产损失","999"),
	ENGINEERING_BODY_HURT_OR_DIED("IAT_7_02","人身伤亡","999"),
	THIRD_PROPERTY_LOSS("IAT_7_03","第三者财产损失","999"),
	LEGAL_LIABILITY_AND_OTHERS("IAT_7_04","法律责任及其他","999"),
	DIRECTORS_RESPONSIBILITIES("IAT_7_05","董事责任","999"),

	CARGO_INSURANCE_LOSS("IAT_8_01","货运险损失","999"),

	GUARANTEE_INFORMATION("IAT_9_01","保证类信息","999"),
	BENEFIT_TYPE_ALLOWANCE_INFORMATION("IAT_9_02","给付型津贴信息","999"),
	DELAY_INFORMATION("IAT_9_03","延误信息","999"),
	RESCUE_INFORMATION("IAT_9_04","救援信息","999"),
	OTHER_LOSS_INFORMATION("IAT_9_05","其他损失信息","999"),

	PERSONAL_INJURY_TRACKING("IAT_10_01","人伤跟踪","999"),

	FLIGHT_DELAY("16","航班延误/取消/备降","999"),
	PROPERTY_LOSS("17","财产损失","999"),
	TRAVEL_ALERT("18","旅行变更","999"),
	BAGGAGE_DELAY("19","行李延误","999"),
	VEHICL_DELAY_OTHER ("20","其他公共交通工具延误","999"),
	EXAMIN_FAILE("21","考试不通过","999"),
	OTHER_LOSS("22","其他非人伤","999"),
	PET_INJURE("24","宠物损伤","999");

	private final String type;
	private final String name;
	private final String code;

	InsuredApplyTypeEnum(String type, String name,String code) {
		this.type = type;
		this.name = name;
		this.code = code;
	}

	public String getType() {
		return type;
	}


	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}


	public static String getName(String type) {
		if(StrUtil.isEmpty(type)){
			return null;
		}
		for (InsuredApplyTypeEnum caseStatus : InsuredApplyTypeEnum.values()) {
			if (type.equals(caseStatus.getType())) {
				return caseStatus.getName();
			}
		}
		return null;
	}

	public static String getType(String name) {
		if(StrUtil.isEmpty(name)){
			return null;
		}
		for (InsuredApplyTypeEnum caseStatus : InsuredApplyTypeEnum.values()) {
			if (name.equals(caseStatus.getName())) {
				return caseStatus.getType();
			}
		}
		return null;
	}

	public static String getCode(String type) {
		if(StrUtil.isEmpty(type)){
			return null;
		}
		for (InsuredApplyTypeEnum caseStatus : InsuredApplyTypeEnum.values()) {
			if (type.equals(caseStatus.getType())) {
				return caseStatus.getCode();
			}
		}
		return null;
	}
}
