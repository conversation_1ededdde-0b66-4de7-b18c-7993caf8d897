package com.paic.ncbs.claim.service.report;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyPlanDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.report.ReportStatDTO;
import com.paic.ncbs.claim.model.dto.report.ReportInfoDTO;
import com.paic.ncbs.claim.model.vo.report.ReportQueryVO;
import com.paic.ncbs.claim.dao.entity.report.*;

import java.util.Date;
import java.util.List;

public interface ReportService {
    /**
     * 获取案件全量信息异常
     */
    AhcsDomainDTO getReportInfo(String reportNo);

    AhcsDomainDTO saveReportInfo(ReportInfoDTO reportInfo, AhcsDomainDTO ahcsDomainDTO, boolean isTranCertificate);

    AhcsDomainDTO requestReportDomainInfoNoCache(String reportNo) throws GlobalBusinessException;

    JSONObject requestReportDomainInfo(String reportNo);

    List<HistoryCaseDTO> getAccidentDateReport(Date accidentDate, String certificateNo, String name, String certificateType);

    AhcsDomainDTO saveBaseReportDomain(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto, boolean isTranCertificate);

    AhcsDomainDTO getReportInfo(String reportNo, String caseTimes);

    int updateByPrimaryKeySelective(ReportInfoExEntity reportInfoEx);

    ReportInfoEntity getReportInfoByReportNo(String reportNo);

    ReportAccidentEntity getReportAccident(String reportNo);

    ReportAccidentExEntity getReportAccidentEx(String reportNo);

    List<WholeCaseBaseEntity> getWholeCaseBase(String reportNo);

    List<WholeCaseBaseExEntity> getWholeCaseBaseEx(String reportNo);

    ReportCustomerInfoEntity getReportCustomerInfoByReportNo(String reportNo);

    List<LinkManEntity> getLinkMans(String reportNo,Integer  caseTimes);

    List<ReportInfoExEntity> getReportInfoEx(String reportNo);

    ReportExcEntity getReportExcByReportNo(String reportNo);

    /*-------------------------------------非人伤--------------------------------------------------------------*/
    ReportAccidentBaggageEntity getReportAccidentBaggageByReportNo(String reportNo);

    ReportAccidentExamEntity getReportAccidentExamByReportNo(String reportNo);

    ReportAccidentTrafficEntity getReportAccidentTrafficByReportNo(String reportNo);

    ReportAccidentTravelEntity getReportAccidentTravelByReportNo(String reportNo);

    ReportAccidentOtherEntity getReportAccidentOtherByReportNo(String reportNo);

    ReportAccidentFlightEntity getReportAccidentFlightByReportNo(String reportNo);

    ReportAccidentLossEntity getReportAccidentLossByReportNo(String reportNo);

    ReportAccidentPetEntity getReportAccidentPetByReportNo(String reportNo);

    /**
     * 新建报案
     * @param reportInfo
     * @param ahcsDomainDTO
     */
    void createReport(ReportInfoDTO reportInfo, AhcsDomainDTO ahcsDomainDTO);


	/**
	 * 获取事故日期报案统计
	 *
	 * @param queryVO
	 * @return
	 */
    ReportStatDTO getAccidentDateReportStat(ReportQueryVO queryVO);

    List<ReportPlanDutyVo> getReportPlanDuty(String reportNo,Integer caseTimes);

    List<ReportPlanDutyVo> getReportPlanDuty(String reportNo);
}
