package com.paic.ncbs.claim.service.dynamic.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.dynamic.DynamicFieldsResultEntity;
import com.paic.ncbs.claim.dao.mapper.dynamic.DynamicFieldResultMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.dynamic.DynamicFieldResultVO;
import com.paic.ncbs.claim.service.dynamic.IDynamicFieldResultService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 动态字段结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
public class DynamicFieldResultServiceImpl extends ServiceImpl<DynamicFieldResultMapper, DynamicFieldsResultEntity> implements IDynamicFieldResultService {

    @Override
    public void saveDynamicFieldResult(List<DynamicFieldResultVO> dynamicFieldResultVOList) throws Exception {
        for (DynamicFieldResultVO dynamicFieldResultVO : dynamicFieldResultVOList) {
            String reportNo = dynamicFieldResultVO.getReportNo();
            Integer caseTimes = dynamicFieldResultVO.getCaseTimes();
            String templateId = dynamicFieldResultVO.getTemplateId();
            if (StringUtils.isEmptyStr(reportNo) || Objects.isNull(caseTimes) || StringUtils.isEmptyStr(templateId) || CollectionUtils.isEmpty(dynamicFieldResultVO.getFieldDetailList())) {
                return;
            }
            String userId = WebServletContext.getUserId();
            this.lambdaUpdate().eq(DynamicFieldsResultEntity::getReportNo, reportNo)
                    .eq(DynamicFieldsResultEntity::getCaseTimes, caseTimes)
                    .eq(DynamicFieldsResultEntity::getTemplateId, templateId)
                    .set(DynamicFieldsResultEntity::getDelFlag, Constants.FLAG_Y).update();
            for (DynamicFieldResultVO.FieldDetailVo fieldDetailVo : dynamicFieldResultVO.getFieldDetailList()) {
                if (StringUtils.isEmptyStr(fieldDetailVo.getFieldCode())) {
                    throw new GlobalBusinessException("字段名不能为空！");
                }
                DynamicFieldsResultEntity dynamicFieldsResultEntity = new DynamicFieldsResultEntity();
                dynamicFieldsResultEntity.setReportNo(reportNo);
                dynamicFieldsResultEntity.setCaseTimes(caseTimes);
                dynamicFieldsResultEntity.setTemplateId(templateId);
                dynamicFieldsResultEntity.setFieldCode(fieldDetailVo.getFieldCode());
                dynamicFieldsResultEntity.setFieldValue(fieldDetailVo.getValue());
//                if (!StringUtils.isEmptyStr(fieldDetailVo.getValueMapper())) {
//                    dynamicFieldsResultEntity.setValueMapper(fieldDetailVo.getValueMapper());
//                } else {
//                    dynamicFieldsResultEntity.setValueMapper(fieldDetailVo.getValue());
//                }
                dynamicFieldsResultEntity.setValueMapper(fieldDetailVo.getValueMapper());
                dynamicFieldsResultEntity.setCreatedBy(userId);
                dynamicFieldsResultEntity.setUpdatedBy(userId);
                this.save(dynamicFieldsResultEntity);
            }
        }
    }

    @Override
    public List<DynamicFieldResultVO.FieldDetailVo> getDynamicFieldResult(DynamicFieldResultVO dynamicFieldResultVO) throws Exception {
        String reportNo = dynamicFieldResultVO.getReportNo();
        Integer caseTimes = dynamicFieldResultVO.getCaseTimes();
        String templateId = dynamicFieldResultVO.getTemplateId();
        if (StringUtils.isEmptyStr(reportNo) || Objects.isNull(caseTimes) || StringUtils.isEmptyStr(templateId)) {
            throw new GlobalBusinessException("查询参数不能为空！");
        }
        List<DynamicFieldResultVO.FieldDetailVo> fieldDetailVos = new ArrayList<>();
        List<DynamicFieldsResultEntity> dynamicFieldsResultEntities = this.lambdaQuery()
                .eq(DynamicFieldsResultEntity::getReportNo, reportNo)
                .eq(DynamicFieldsResultEntity::getCaseTimes, caseTimes)
                .eq(DynamicFieldsResultEntity::getTemplateId, templateId)
                .eq(DynamicFieldsResultEntity::getDelFlag, Constants.FLAG_N).list();

        if (CollectionUtils.isEmpty(dynamicFieldsResultEntities)) {
            return fieldDetailVos;
        }

        for (DynamicFieldsResultEntity dynamicFieldsResultEntity : dynamicFieldsResultEntities) {
            DynamicFieldResultVO.FieldDetailVo fieldDetailVo = new DynamicFieldResultVO.FieldDetailVo();
            fieldDetailVo.setFieldCode(dynamicFieldsResultEntity.getFieldCode());
            fieldDetailVo.setValue(dynamicFieldsResultEntity.getFieldValue());
            fieldDetailVo.setValueMapper(dynamicFieldsResultEntity.getValueMapper());
            fieldDetailVos.add(fieldDetailVo);
        }
        return fieldDetailVos;
    }

    @Override
    public List<DynamicFieldResultVO> getDynamicFieldResultReport(String reportNo, Integer caseTimes, List<String> tempIds) throws Exception {
        if (StringUtils.isEmptyStr(reportNo) || Objects.isNull(caseTimes)) {
            throw new GlobalBusinessException("查询参数不能为空！");
        }

        if(CollectionUtils.isEmpty(tempIds)){
            return null;
        }
        List<DynamicFieldResultVO> fieldResultVOS = new ArrayList<>();
        for (String tempId : tempIds) {
            List<DynamicFieldsResultEntity> dynamicFieldsResultEntities = this.lambdaQuery()
                    .eq(DynamicFieldsResultEntity::getReportNo, reportNo)
                    .eq(DynamicFieldsResultEntity::getCaseTimes, caseTimes)
                    .eq(DynamicFieldsResultEntity::getTemplateId, tempId)
                    .eq(DynamicFieldsResultEntity::getDelFlag, Constants.FLAG_N).list();
            if (CollectionUtils.isEmpty(dynamicFieldsResultEntities)) {
                continue;
            }
            DynamicFieldResultVO dynamicFieldResultVO = new DynamicFieldResultVO();
            dynamicFieldResultVO.setReportNo(reportNo);
            dynamicFieldResultVO.setCaseTimes(caseTimes);
            dynamicFieldResultVO.setTemplateId(tempId);
            List<DynamicFieldResultVO.FieldDetailVo> fieldDetailVos = new ArrayList<>();
            for (DynamicFieldsResultEntity dynamicFieldsResultEntity : dynamicFieldsResultEntities) {
                DynamicFieldResultVO.FieldDetailVo fieldDetailVo = new DynamicFieldResultVO.FieldDetailVo();
                fieldDetailVo.setFieldCode(dynamicFieldsResultEntity.getFieldCode());
                fieldDetailVo.setValue(dynamicFieldsResultEntity.getFieldValue());
                fieldDetailVo.setValueMapper(dynamicFieldsResultEntity.getValueMapper());
                fieldDetailVos.add(fieldDetailVo);
            }
            dynamicFieldResultVO.setFieldDetailList(fieldDetailVos);
            fieldResultVOS.add(dynamicFieldResultVO);
        }

        return fieldResultVOS;
    }

}
