package com.paic.ncbs.claim.model.vo.report;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "报案客户信息")
public class ReportCustomerInfoVO {

    @ApiModelProperty(value = "职业类别")
    private String professionClass;

    @ApiModelProperty(value = "主键")
    private String idAhcsReportCustomer;

    @ApiModelProperty(value = "客户号")
    private String clientNo;

    @ApiModelProperty(value = "客户姓名")
    private String name;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    @ApiModelProperty(value = "客户证件类型")
    private String clientCertificateType;

    @ApiModelProperty(value = "证件类型名称")
    private String certificateTypeName;

    @ApiModelProperty(value = "证件号码")
    private String certificateNo;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "性别")
    private String sexCode;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "客户类别")
    private String clientCategoty;

    @ApiModelProperty(value = "客户类别的中文名")
    private String clientCategotyName;

    @ApiModelProperty(value = "客户类型")
    private String clientType;

    @ApiModelProperty(value = "客户类型的中文名")
    private String clientTypeName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "保单号")
    private String policyNo;
    @ApiModelProperty(value = "职业类别")
    private String professionChnName;

    @ApiModelProperty(value = "客户属性 100真实、010连带真实、200虚拟、020连带虚拟")
    private String personnelAttribute = "";

    private String idPlyRiskPerson;

    @ApiModelProperty(value = "是否有社保")
    private String isSociaSecurity;
    /**
     * 续保保单号
     */
    private String lastPolicyNo;
    /**
     * 续保标志：Y-是 ，N-否
     */
    private String isRenew;
    /**
     * 报案人来电号码
     */
    private String reporterCallNo;

    /**
     * 投保人姓名
     */
    private String applicantName;

    /**
     * 投保人证件类型
     */

    private String applicantCertType;

    /**
     * 投保人证件号码
     */
    private String applicantCertNo;

    /**
     * 被保人与投保人关系
     */
    private String relationshipWithApplicant;

    public String getApplicantCertType() {
        return applicantCertType;
    }

    public void setApplicantCertType(String applicantCertType) {
        this.applicantCertType = applicantCertType;
    }

    public String getApplicantCertNo() {
        return applicantCertNo;
    }

    public void setApplicantCertNo(String applicantCertNo) {
        this.applicantCertNo = applicantCertNo;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getReporterCallNo() {
        return reporterCallNo;
    }

    public void setReporterCallNo(String reporterCallNo) {
        this.reporterCallNo = reporterCallNo;
    }

    public String getRelationshipWithApplicant() {
        return relationshipWithApplicant;
    }

    public void setRelationshipWithApplicant(String relationshipWithApplicant) {
        this.relationshipWithApplicant = relationshipWithApplicant;
    }

    public String getIsTransferInsure() {
        return isTransferInsure;
    }

    public void setIsTransferInsure(String isTransferInsure) {
        this.isTransferInsure = isTransferInsure;
    }

    /**
     * 转保标志：是，否
     */
    private String isTransferInsure;

    public String getIsSociaSecurity() {
        return isSociaSecurity;
    }

    public void setIsSociaSecurity(String isSociaSecurity) {
        this.isSociaSecurity = isSociaSecurity;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public String getCertificateTypeName() {
        return certificateTypeName;
    }

    public void setCertificateTypeName(String certificateTypeName) {
        this.certificateTypeName = certificateTypeName;
    }

    public String getClientCategotyName() {
        return clientCategotyName;
    }

    public void setClientCategotyName(String clientCategotyName) {
        this.clientCategotyName = clientCategotyName;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public String getIdAhcsReportCustomer() {
        return idAhcsReportCustomer;
    }

    public void setIdAhcsReportCustomer(String idAhcsReportCustomer) {
        this.idAhcsReportCustomer = idAhcsReportCustomer == null ? null : idAhcsReportCustomer.trim();
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo == null ? null : clientNo.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType == null ? null : certificateType.trim();
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo == null ? null : certificateNo.trim();
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode == null ? null : sexCode.trim();
    }

    public String getClientCategoty() {
        return clientCategoty;
    }

    public void setClientCategoty(String clientCategoty) {
        this.clientCategoty = clientCategoty == null ? null : clientCategoty.trim();
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType == null ? null : clientType.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    @Override
    public String toString() {
        return "ReportCustomerInfoVO [idAhcsReportCustomer=" + idAhcsReportCustomer + ", clientNo=" + clientNo
                + ", name=" + name + ", certificateType=" + certificateType + ", certificateTypeName="
                + certificateTypeName + ", certificateNo=" + certificateNo + ", age=" + age + ", sexCode=" + sexCode
                + ", birthday=" + birthday + ", clientCategoty=" + clientCategoty + ", clientCategotyName="
                + clientCategotyName + ", clientType=" + clientType + ", clientTypeName=" + clientTypeName + ", remark="
                + remark + ", reportNo=" + reportNo + "]";
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getProfessionClass() {
        return professionClass;
    }

    public void setProfessionClass(String professionClass) {
        this.professionClass = professionClass;
    }

    public String getProfessionChnName() {
        return professionChnName;
    }

    public void setProfessionChnName(String professionChnName) {
        this.professionChnName = professionChnName;
    }

    public String getPersonnelAttribute() {
        return personnelAttribute;
    }

    public void setPersonnelAttribute(String personnelAttribute) {
        this.personnelAttribute = personnelAttribute;
    }

    public String getIdPlyRiskPerson() {
        return idPlyRiskPerson;
    }

    public void setIdPlyRiskPerson(String idPlyRiskPerson) {
        this.idPlyRiskPerson = idPlyRiskPerson;
    }

    public String getLastPolicyNo() {
        return lastPolicyNo;
    }

    public void setLastPolicyNo(String lastPolicyNo) {
        this.lastPolicyNo = lastPolicyNo;
    }

    public String getIsRenew() {
        return isRenew;
    }

    public void setIsRenew(String isRenew) {
        this.isRenew = isRenew;
    }

    public String getRiskGroupType() {
        return riskGroupType;
    }

    public void setRiskGroupType(String riskGroupType) {
        this.riskGroupType = riskGroupType;
    }

    /**
     * 标的类型
     */
    private String riskGroupType;
}
