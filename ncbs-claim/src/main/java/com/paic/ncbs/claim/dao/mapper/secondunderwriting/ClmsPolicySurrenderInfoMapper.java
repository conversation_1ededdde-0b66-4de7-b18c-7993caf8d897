package com.paic.ncbs.claim.dao.mapper.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsPolicySurrenderInfoEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicySurrenderInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 理赔保单解约信息表(ClmsPolicySurrenderInfoEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-03 14:49:01
 */
public interface ClmsPolicySurrenderInfoMapper {

    /**
     * 通过报案号查询数据
     *
     * @return 实例对象
     */
    List<ClmsPolicySurrenderInfoEntity> getSurrenderInfo(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 新增数据
     *
     * @param clmsPolicySurrenderInfoEntity 实例对象
     * @return 影响行数
     */
    int insert(ClmsPolicySurrenderInfoEntity clmsPolicySurrenderInfoEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPolicySurrenderInfoEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsPolicySurrenderInfoEntity> entities);

    /**
     * 修改数据
     *
     * @param clmsPolicySurrenderInfoEntity 实例对象
     * @return 影响行数
     */
    int update(ClmsPolicySurrenderInfoEntity clmsPolicySurrenderInfoEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 删除报案号赔付次数下的理赔解约信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    int deleteByReportNoAndCaseTimes(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    int getSurrenderCount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 根据idClmsSecondUnderwriting 查询

     * @return
     */
    List<ClmsPolicySurrenderInfoVO> getSurrenderList(String idClmsSecondUnderwriting);

    /**
     * 理赔在途
     * @param policyNo
     * @param reportNo
     */
    String getClaimingInfo(String policyNo, String reportNo);

    /**
     * 查询保单报案历史记录最新事故日期
     * @param policyNo
     */
    Date getPolicyReportAccdentDate(String policyNo);

    /**
     * 判断报案号下是否存在解约信息
     * @param reportNo
     * @return
     */
    List<ClmsPolicySurrenderInfoVO> getSurrenderListByReportNo(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    List<ClmsPolicySurrenderInfoVO> getSurrenderSuccessCount(@Param("policyNo")String policyNo);

}

