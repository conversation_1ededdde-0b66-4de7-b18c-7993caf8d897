package com.paic.ncbs.claim.common.enums;

import lombok.Getter;

/**
 * 理赔送再保的业务环节
 */
@Getter
public enum ReinsuranceClaimTypeEnum {

    REGISTER("1", "立案"),
    ESTIMATE_LOSS("5", "估损"),
    ENDCASE("7", "结案"),
    CASE_REOPEN("9", "案件重开");

    private final String type;
    private final String name;

    ReinsuranceClaimTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }
}
