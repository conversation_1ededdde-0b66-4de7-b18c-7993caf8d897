package com.paic.ncbs.claim.common.constant;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class PrintConstValues {

    public static final String PRINT_XML_FILE_NAME_PROTOCOLPAY = "protocolPay";
    public static final String PRINT_XML_FILE_NAME_ZEROEND = "zeroEnd";
    public static final String PRINT_XML_FILE_NAME_CANCELEND = "cancelEnd";
    public static final String PRINT_XML_FILE_NAME_FORMALPAY = "formalPay";
    public static final String PRINT_XML_FILE_NAME_ACCOMMODATIONPAY = "accommodationPay";
    public static final String PRINT_XML_FILE_NAME_REFUSEPAY = "refusePay";
    public static final String PRINT_XML_FILE_NAME_CASEINFO_BOOT = "caseInfoBoot";
    public static final String PRINT_XML_FILE_NAME_MULTIPLECLAIM_BOOT = "multipleClaimBoot";
    public static final String FILE_NAME_FORMALPAY_BUSI = "formalPayBusi";
    public static final String FILE_NAME_PROTOCOLPAY_BUSI = "protocolPayBusi";
    public static final String FILE_NAME_ZEROEND_BUSI = "zeroEndBusi";
    public static final String FILE_NAME_REFUSEPAY_BUSI = "refusePayBusi";

    
    public static final String PRINT_DOCTYPE_PROTOCOL_PAY = "02";
    public static final String PRINT_DOCTYPE_ZERO_END = "05";
    public static final String PRINT_DOCTYPE_CANCEL_END = "06";
    public static final String PRINT_DOCTYPE_FORMAL_PAY = "01";
    public static final String PRINT_DOCTYPE_ACCOMMODATION_PAY = "03";
    public static final String PRINT_DOCTYPE_REFUSE_PAY = "04";
    public static final String PRINT_DOCTYPE_CASE_INFO = "07";
    public static final String PRINT_DOCTYPE_MULTIPLECLAIM = "08";
    public static final String PRINT_DOCTYPE_SELF = "09";
//正常
    public static final String DOCTYPE_FORMAL_PAY_BUSI = "14";
    //协议
    public static final String DOCTYPE_PROTOCOL_PAY_BUSI = "11";
    //通融
    public static final String DOCTYPE_ACCOMMODATION_PAY_BUSI = "15";
    //零结
    public static final String DOCTYPE_ZERO_END_BUSI = "12";
    //拒赔
    public static final String DOCTYPE_REFUSE_PAY_BUSI = "13";
    //注销
    public static final String DOCTYPE_BUSI = "00";
    
    public static final Map<String, String> PRINT_XML_FILE_NAME_MAP = new HashMap<String, String>();
    static {
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_FORMAL_PAY, PRINT_XML_FILE_NAME_FORMALPAY);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_PROTOCOL_PAY, PRINT_XML_FILE_NAME_PROTOCOLPAY);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_ACCOMMODATION_PAY, PRINT_XML_FILE_NAME_ACCOMMODATIONPAY);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_REFUSE_PAY, PRINT_XML_FILE_NAME_REFUSEPAY);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_ZERO_END, PRINT_XML_FILE_NAME_ZEROEND);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_CANCEL_END, PRINT_XML_FILE_NAME_CANCELEND);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_CASE_INFO, PRINT_XML_FILE_NAME_CASEINFO_BOOT);
        PRINT_XML_FILE_NAME_MAP.put(PRINT_DOCTYPE_MULTIPLECLAIM, PRINT_XML_FILE_NAME_MULTIPLECLAIM_BOOT);


        PRINT_XML_FILE_NAME_MAP.put(DOCTYPE_PROTOCOL_PAY_BUSI, FILE_NAME_PROTOCOLPAY_BUSI);
        PRINT_XML_FILE_NAME_MAP.put(DOCTYPE_ZERO_END_BUSI, FILE_NAME_ZEROEND_BUSI);
        PRINT_XML_FILE_NAME_MAP.put(DOCTYPE_REFUSE_PAY_BUSI, FILE_NAME_REFUSEPAY_BUSI);
        PRINT_XML_FILE_NAME_MAP.put(DOCTYPE_FORMAL_PAY_BUSI, FILE_NAME_FORMALPAY_BUSI);
    }
    
    
    public static final Map<String, String> PRINT_DOCTYPE_PAY_MAP = new HashMap<>();
    static {
    	PRINT_DOCTYPE_PAY_MAP.put(PRINT_DOCTYPE_FORMAL_PAY, PRINT_DOCTYPE_FORMAL_PAY);
    	PRINT_DOCTYPE_PAY_MAP.put(PRINT_DOCTYPE_PROTOCOL_PAY, PRINT_DOCTYPE_PROTOCOL_PAY);
    	PRINT_DOCTYPE_PAY_MAP.put(PRINT_DOCTYPE_ACCOMMODATION_PAY, PRINT_DOCTYPE_ACCOMMODATION_PAY);

        PRINT_DOCTYPE_PAY_MAP.put(DOCTYPE_FORMAL_PAY_BUSI, DOCTYPE_FORMAL_PAY_BUSI);
        PRINT_DOCTYPE_PAY_MAP.put(DOCTYPE_PROTOCOL_PAY_BUSI, DOCTYPE_PROTOCOL_PAY_BUSI);


    }

    public static final Map<String, String> PRINT_DATA_MODEL_MAP = new HashMap<String, String>();
    static {
        
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_PROTOCOL_PAY, "protocolPayPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_ZERO_END, "zeroEndPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_CANCEL_END, "cancelEndPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_FORMAL_PAY, "formalPayPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_ACCOMMODATION_PAY, "accommodationPayPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_REFUSE_PAY, "refusePayPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_CASE_INFO, "caseInfoBootPrint");
        PRINT_DATA_MODEL_MAP.put(PRINT_DOCTYPE_MULTIPLECLAIM, "caseInfoBootPrint");

        PRINT_DATA_MODEL_MAP.put(DOCTYPE_FORMAL_PAY_BUSI, "formalPayPrintBusi");
        PRINT_DATA_MODEL_MAP.put(DOCTYPE_PROTOCOL_PAY_BUSI, "protocolPayPrintBusi");
        PRINT_DATA_MODEL_MAP.put(DOCTYPE_ZERO_END_BUSI, "zeroEndPrintBusi");
        PRINT_DATA_MODEL_MAP.put(DOCTYPE_REFUSE_PAY_BUSI, "refusePayPrintBusi");
    }
    
    
    
    public static final String CLAIM_TYPE_PAY = "1";
	public static final String PAYMENT_TYPE_PAY = "13";
    public static final String CLAIM_TYPE_PRE_PAY = "2";
    public static final String PAYMENT_TYPE_PRE_PAY = "11";
	public static final String PAYMENT_TYPE_COINSURE_PAY = "P13";
    public static final String PAYMENT_TYPE_COINSURE_FEE = "P1J";
    public static final String PAYMENT_TYPE_REINSURE_RECEIVE_PAY = "C13";
    public static final String PAYMENT_TYPE_REINSURE_RECEIVE_FEE = "C1J";
    public static final String PAYMENT_TYPE_FEE = "1J";

    public static final String LOG_CASETIMES =",赔付次数=";



}
