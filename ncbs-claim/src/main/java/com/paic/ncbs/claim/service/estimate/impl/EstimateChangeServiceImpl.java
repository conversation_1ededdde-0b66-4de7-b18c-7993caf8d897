package com.paic.ncbs.claim.service.estimate.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.estimate.EstimateChangeApplyEntity;
import com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.*;
import com.paic.ncbs.claim.dao.mapper.prepay.ClmsPolicyPrepayDutyDetailMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.duty.AccidentLossVo;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeVo;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.duty.LossEstimationService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.estimate.IEstimateChangeApplyService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.AutoEstimateAmountService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.service.waitingcenter.WaitingCenterService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service("estimateChangeService")
@Slf4j
public class EstimateChangeServiceImpl implements EstimateChangeService {

    @Autowired
    private EstimateChangeMapper estimateChangeMapper;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private ReinsuranceService reinsuranceService;

    @Autowired
    private AutoEstimateAmountService autoEstimateAmountService;

    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;

    @Autowired
    private EstimateDutyHistoryMapper estimateDutyHistoryMapper;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private ClmsEstimateRecordMapper estimateRecordMapper;
    @Autowired
    private TPAFeign tpaFeign;

    @Autowired
    private CaseBaseService caseBaseService;

    @Autowired
    private EstimatePlanMapper estimatePlanDAO;

    @Autowired
    private EstimateDutyMapper estimateDutyDAO;

    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private CoinsureService coinsureService;

    @Autowired
    private IEstimateChangeApplyService estimateChangeApplyService;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private UserInfoService userInfoService ;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private PermissionUserMapper permissionUserMapper;
    @Autowired
    private NoticeService noticeService;

    @Autowired
    private WaitingCenterService unSettleApproveWaitingCenterServiceImpl;
    @Autowired
    private ClmsPolicyPrepayDutyDetailMapper policyPrepayDutyDetailMapper;
    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Autowired
    private LossEstimationService lossEstimationService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper ;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Override
    public List<EstimateChangeDTO> getAllEstimateChangeList(String reportNo, Integer caseTimes) {
        return estimateChangeMapper.getAllEstimateChangeList(reportNo, caseTimes);
    }

    @Override
    public List<EstimateChangeDTO> getLastEstimateChangeList(String reportNo, Integer caseTimes) {
        return Optional.ofNullable(estimateChangeMapper.getLastEstimateChangeList(reportNo, caseTimes)).orElse(new ArrayList<>());
    }

    @Override
    public void addEstimateChangeList(List<EstimateChangeDTO> estimateChangeList,List<EstimateDutyRecordDTO> dutyRecordDTOList) {
        if(ListUtils.isEmptyList(estimateChangeList)){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }

        EstimateChangeDTO changeDTO = estimateChangeList.get(0);
        //去掉重开案件不能修正校验
//        if (changeDTO.getCaseTimes() > 1) {
//            throw new GlobalBusinessException("重开案件不能修正");
//        }
        //校验责任级赔款金额
        this.checkDutyAmount(dutyRecordDTOList,changeDTO);

        WholeCaseBaseDTO wholecase = wholeCaseBaseService.getWholeCaseBase2(changeDTO.getReportNo(),changeDTO.getCaseTimes());
        if(!ConstValues.YES.equals(wholecase.getIsRegister()) ){
            throw new GlobalBusinessException("未立案不能修正");
        }
        if("0".equals(wholecase.getWholeCaseStatus())){
            throw new GlobalBusinessException("已结案不能修正");
        }
        String userId;
        try {
            userId = WebServletContext.getUserId();
        }catch (Exception e){
            userId = ConstValues.SYSTEM;
        }
        List<EstimateChangeDTO> changeList = new ArrayList<>();
        for (EstimateChangeDTO dto : estimateChangeList) {
            dto.setCreatedBy(userId);
            dto.setUpdatedBy(userId);
            if(dto.getChangeAmount() == null){
                continue;
            }
            BigDecimal maxPayAmt = dto.getMaxPayAmount();
            if(maxPayAmt == null){
                maxPayAmt = BigDecimal.ZERO;
            }

            // 责任险不校验金额
			if (!riskPropertyService.displayRiskProperty(dto.getReportNo(), dto.getPolicyNo())) {
				if (BigDecimalUtils.compareBigDecimalPlus(dto.getChangeAmount(), maxPayAmt)) {
					throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),
							dto.getPolicyNo() + "修正金额不能大于剩余理赔金额");
				}
			}

            //dto.setIdclmsEstimateChange(UuidUtil.getUUID());
            dto.setChangeDate(new Date());
            dto.setUserId(userId);
            changeList.add(dto);
        }

        if(changeList.size() < 1){
            throw new GlobalBusinessException("修正金额需大于零");
        }
        String oldIdFlagHistoryChange = null;
        List<EstimateChangeDTO> changeOldList = estimateChangeMapper.getLastEstimateChangeList(changeDTO.getReportNo(),changeDTO.getCaseTimes());
        if (!CollectionUtils.isEmpty(changeOldList)){
            oldIdFlagHistoryChange = changeOldList.get(0).getIdFlagHistoryChange();
        }
        deleteEstimateChange(estimateChangeList.get(0));
        estimateChangeMapper.addEstimateChangeList(changeList);

        // 发送再保
        RepayCalDTO repayCalDTO = new RepayCalDTO();
        repayCalDTO.setReportNo(changeDTO.getReportNo());
        repayCalDTO.setCaseTimes(changeDTO.getCaseTimes());
        repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ESTIMATE_LOSS);
        repayCalDTO.setOldIdFlagHistoryChange(oldIdFlagHistoryChange);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                reinsuranceService.sendReinsurance(repayCalDTO);
            }
        });

    }

    private void checkDutyAmount(List<EstimateDutyRecordDTO> dutyRecordDTOList,EstimateChangeDTO changeDTO) {

        String reportNo = changeDTO.getReportNo();
        Integer caseTimes = changeDTO.getCaseTimes();
        if(caseTimes>1){
            //有重开的情况下取未决修正金额
            List<EstimateDutyRecordDTO> oldRestarDutyRecordDtoList = estimateDutyRecordMapper.getRecordsOfRegisterCaseByReportNo(reportNo,caseTimes-1, EstimateUtil.ESTIMATE_TYPE_RESTART);
            Map<String,BigDecimal> oldAmount = oldRestarDutyRecordDtoList.stream().collect(Collectors.toMap(
                    k->k.getPlanCode()+k.getDutyCode(),
                    v ->ObjectUtil.isNotEmpty(v.getEstimateAmount()) ? v.getEstimateAmount() : new BigDecimal(BigInteger.ZERO)
            ));
            dutyRecordDTOList.stream().forEach(dto -> dto.setLastPayAmount(ObjectUtil.isNotEmpty(oldAmount.get(dto.getPlanCode()+dto.getDutyCode())) ? oldAmount.get(dto.getPlanCode()+dto.getDutyCode()) : BigDecimal.ZERO));
        }else{
            //无重开的情况下默认上次赔付金额为0
            dutyRecordDTOList.stream().forEach(dto -> dto.setLastPayAmount(BigDecimal.ZERO));
        }
        String msg = "";
        //数据处理
        //1.获取责任级预赔金额
        Map<String,BigDecimal> prepayAmountMap = new HashMap<>();
        //2.获取总理赔费用估损金额总和
//        BigDecimal fee = BigDecimal.ZERO;
//        for(EstimateDutyRecordDTO estimateDutyRecordDTO:dutyRecordDTOList){
//            fee = fee.add(estimateDutyRecordDTO.getArbitrageFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getArbitrageFee())
//                    .add(estimateDutyRecordDTO.getLawsuitFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getLawsuitFee())
//                    .add(estimateDutyRecordDTO.getCommonEstimateFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getCommonEstimateFee())
//                    .add(estimateDutyRecordDTO.getLawyerFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getLawyerFee())
//                    .add(estimateDutyRecordDTO.getExecuteFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getExecuteFee())
//                    .add(estimateDutyRecordDTO.getVerifyAppraiseFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getVerifyAppraiseFee())
//                    .add(estimateDutyRecordDTO.getInquireFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getInquireFee())
//                    .add(estimateDutyRecordDTO.getSpecialSurveyFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getSpecialSurveyFee())
//                    .add(estimateDutyRecordDTO.getOtherFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getOtherFee());
//        }
        //3.获取责任级预赔赔款和本次案件预赔费用总和
        List<ClmsPolicyPrepayDutyDetailEntity> clmsPolicyPrepayDutyDetailEntityList = policyPrepayDutyDetailMapper.selectPrePayAmountAndCode(reportNo,caseTimes);
        if(clmsPolicyPrepayDutyDetailEntityList != null && clmsPolicyPrepayDutyDetailEntityList.size()>0){
            for(ClmsPolicyPrepayDutyDetailEntity clmsPolicyPrepayDutyDetailEntity:clmsPolicyPrepayDutyDetailEntityList){
                prepayAmountMap.put(clmsPolicyPrepayDutyDetailEntity.getDutyCode(),clmsPolicyPrepayDutyDetailEntity.getPrepayAmount());
            }
        }
        //校验
        //校验责任赔款立案金额大于本次从开案件的预赔赔款+本次重开前赔款
        List<EstimateDutyRecordDTO> estimateAmountList = dutyRecordDTOList.stream()
                .filter(ent -> ObjectUtil.isNotEmpty(ent.getEstimateAmount())
                        && ent.getEstimateAmount()
                        .compareTo(ObjectUtil.isNotEmpty(prepayAmountMap.get(ent.getDutyName())) ?
                                prepayAmountMap.get(ent.getDutyName()) : BigDecimal.ZERO
                        .add(ObjectUtil.isNotEmpty(ent.getLastPayAmount()) ?
                                ent.getLastPayAmount():BigDecimal.ZERO))<0)
                .collect(Collectors.toList());
        if(ObjectUtil.isNotEmpty(estimateAmountList) && estimateAmountList.size()>0){
            for (int i = 0; i < estimateAmountList.size(); i++) {
                //前次赔付金额
                String lastAmount = (ObjectUtil.isNotEmpty(prepayAmountMap.get(estimateAmountList.get(i).getDutyName())) ?
                        prepayAmountMap.get(estimateAmountList.get(i).getDutyName()) : BigDecimal.ZERO
                        .add(ObjectUtil.isNotEmpty(estimateAmountList.get(i).getLastPayAmount()) ?
                                estimateAmountList.get(i).getLastPayAmount():BigDecimal.ZERO)).toString();
                msg += estimateAmountList.get(i).getDutyName()+"未决赔款立案金额不能小于前次理赔金额"+lastAmount;
                if(i!=0 || i!=estimateAmountList.size()-1){
                    msg+="\n";
                }
            }
        }
        //校验直接理赔费用估损金额大于等于前重开案件的总支付费用
        //前赔案总支付费用
//        BigDecimal sumFee = policyPayMapper.selectSumFee(reportNo);
//        sumFee = ObjectUtil.isNotEmpty(sumFee) ? sumFee : BigDecimal.ZERO;
//        if(fee.compareTo(sumFee)<0){
//            if(StringUtils.isNotEmpty(msg)){
//                msg+="\n";
//            }
//            msg+="未决直接理赔费用预估金额不能小于前次费用金额"+sumFee;
//        }
        if(ObjectUtil.isNotEmpty(msg)){
            throw new GlobalBusinessException(msg);
        }
    }


    @Override
    public void deleteEstimateChange(EstimateChangeDTO estimateChangeDTO) {
        estimateChangeMapper.deleteEstimateChange(estimateChangeDTO);
    }

    @Override
    public List<EstimateChangeDTO> getPolicyRegisterAmount(String reportNo, Integer caseTimes) {
        List<EstimateChangeDTO> resultList = Optional.ofNullable(estimateChangeMapper.getPolicyRegisterAmount(reportNo, caseTimes)).orElse(new ArrayList<>());
        List<EstimateChangeDTO> lastList = getLastEstimateChangeList(reportNo, caseTimes);
        if(lastList.size() > 0 ){
            //如果有修正金额则设置为修正金额
            Map<String, BigDecimal> policyAmoutMap = lastList.stream().collect(Collectors.toMap(
                    EstimateChangeDTO::getPolicyNo,EstimateChangeDTO::getChangeAmount));
            resultList.forEach( dto -> {
                dto.setRegisterAmount(policyAmoutMap.get(dto.getPolicyNo()));
            });
        }
        return resultList;
    }

    @Transactional
    @Override
    public String estimateChangeApply(EstimateChangePolicyFormDTO estimateChangePolicyForm) throws Exception {
        log.info("未决修正申请，param：{}", JsonUtils.toJsonString(estimateChangePolicyForm));
        String loginUm = WebServletContext.getUserId();
        String reportNo = estimateChangePolicyForm.getReportNo();
        Integer caseTimes = estimateChangePolicyForm.getCaseTimes();
        bpmService.processCheck(reportNo,BpmConstants.OC_ESTIMATE_CHANGE_REVIEW,BpmConstants.OPERATION_INITIATE);
        /* detele zjtang 删除旧流程校验逻辑
        Long count = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).count();
        if(count > 0L){
            throw new GlobalBusinessException("存在审批中的未决修正，不可提交");
        }
         */

        BigDecimal applyAmount = BigDecimal.ZERO;
        List<EstimateChangePolicyDTO> estimatePolicyList = estimateChangePolicyForm.getEstimatePolicyList();
        if(!CollectionUtils.isEmpty(estimatePolicyList)){
            List<EstimateChangePlanDTO> estimatePlanList;
            for (EstimateChangePolicyDTO changePolicyDTO : estimatePolicyList) {
                if(!CollectionUtils.isEmpty(changePolicyDTO.getRiskGroupList()) && changePolicyDTO.getRiskGroupList().size() == 1) {
                    EstimateChangeRiskGroupDTO estimateChangeRiskGroupDTO = changePolicyDTO.getRiskGroupList().get(0);
                    estimatePlanList = estimateChangeRiskGroupDTO.getEstimatePlanList();
                }else {
                    estimatePlanList = changePolicyDTO.getEstimatePlanList();
                }
                if(CollectionUtils.isEmpty(estimatePlanList)){
                    continue;
                }
                for (EstimateChangePlanDTO planDTO : estimatePlanList) {
                    List<EstimateDutyRecordDTO> estimateDutyRecordList = planDTO.getEstimateDutyRecordList();
                    if(CollectionUtils.isEmpty(estimateDutyRecordList)){
                        continue;
                    }
                    for (EstimateDutyRecordDTO recordDTO : estimateDutyRecordList) {
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getEstimateAmount()) ? BigDecimal.ZERO : recordDTO.getEstimateAmount());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getArbitrageFee()) ? BigDecimal.ZERO : recordDTO.getArbitrageFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getLawsuitFee()) ? BigDecimal.ZERO : recordDTO.getLawsuitFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getCommonEstimateFee()) ? BigDecimal.ZERO : recordDTO.getCommonEstimateFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getLawyerFee()) ? BigDecimal.ZERO : recordDTO.getLawyerFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getExecuteFee()) ? BigDecimal.ZERO : recordDTO.getExecuteFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getVerifyAppraiseFee()) ? BigDecimal.ZERO : recordDTO.getVerifyAppraiseFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getInquireFee()) ? BigDecimal.ZERO : recordDTO.getInquireFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getOtherFee()) ? BigDecimal.ZERO : recordDTO.getOtherFee());
                        applyAmount = applyAmount.add(Objects.isNull(recordDTO.getSpecialSurveyFee()) ? BigDecimal.ZERO : recordDTO.getSpecialSurveyFee());
                    }
                }
            }
        }
        //获取当前用户估损审批的权限等级:案件的估损金额(赔款+费用)大于等于当前处理人的审核权限的上限金额，需要审核，否则不需要审批
        boolean isNeedAudit = false;
        if (StringUtils.isNotEmpty(loginUm)) {
            if (!("system".equals(loginUm) || ConstValues.SYSTEM.equals(loginUm))) {
                PermissionUserVO permissionUserVO = new PermissionUserVO();
                permissionUserVO .setUserId(loginUm);
                permissionUserVO.setTypeCode(Constants.PERMISSION_REGIST);
                permissionUserVO.setComCode(WebServletContext.getDepartmentCode());
                List<PermissionUserDTO> permissionUserList = permissionUserMapper.getPermissionUserList(permissionUserVO);
                if (!CollectionUtils.isEmpty(permissionUserList)) {
                    PermissionUserDTO permissionUserDTO = permissionUserList.get(0);
                    if (permissionUserDTO.getMaxAmount().compareTo(applyAmount) < 0) {
                        isNeedAudit = true;
                    }
                }else {
                    isNeedAudit = true;
                }
            }
        } else {
            isNeedAudit = true;
        }
        //若未决金额与原未决金额相等，无需审批
        BigDecimal estimateRecordAmount = estimateRecordMapper.getLastEstimateRecordAmount(reportNo, caseTimes);
        estimateRecordAmount = Optional.ofNullable(estimateRecordAmount).orElse(new BigDecimal(0));
        if(applyAmount.compareTo(estimateRecordAmount)==0) {
            isNeedAudit= false;
        }
        //保存未决修正估损信息
        if(estimateChangePolicyForm.getLossEstimationVos()!=null){
            lossEstimationService.saveLossEstimationList(reportNo, caseTimes,BpmConstants.OC_ESTIMATE_CHANGE_REVIEW,estimateChangePolicyForm.getLossClass(),estimateChangePolicyForm.getLossEstimationVos());
        }
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, "发起", null, loginUm);
        if(!isNeedAudit){
            log.info("未决修正金额小于申请人审核权限的上限金额，不走审批。reportNo:{}", reportNo);
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, "自动通过", null, loginUm);
            return estimateChangeService.estimateChange(estimateChangePolicyForm);
        }

        log.info("未决修正金额大于等于申请人审核权限的上限金额，生成审批。reportNo:{}", reportNo);
        Integer applyTimes = estimateChangeMapper.getChangeApplyTimes(reportNo,caseTimes);
        EstimateChangeApplyEntity estimateChangeApply = new EstimateChangeApplyEntity();
        estimateChangeApply.setCreatedBy(loginUm);
        estimateChangeApply.setUpdatedBy(loginUm);
        estimateChangeApply.setReportNo(reportNo);
        estimateChangeApply.setCaseTimes(caseTimes);
        estimateChangeApply.setApplyUm(loginUm);
        estimateChangeApply.setApplyDate(new Date());
        estimateChangeApply.setAuditStatus(BaseConstant.STRING_1);
        estimateChangeApply.setApplyTimes(applyTimes+1);
        estimateChangeApply.setApplyAmount(applyAmount);
        estimateChangeApply.setApplyParam(JsonUtils.toJsonString(estimateChangePolicyForm));
        estimateChangeApply.setApplyRemark(estimateChangePolicyForm.getEstimateChangeReason());
        estimateChangeApply.setArchiveTime(new Date());
        estimateChangeApplyService.save(estimateChangeApply);

        String taskIdNew = UuidUtil.getUUID();
        //生成审批任务
        startEstimateChangeReview(reportNo, caseTimes, applyAmount, null,
                estimateChangePolicyForm.getEstimateChangeApproved(), taskIdNew);
        log.info("未决修正生成审批流程结束,reportNo={},caseTimes={}", reportNo, caseTimes);
        return null;
    }


    @Transactional
    @Override
    public String estimateChange(EstimateChangePolicyFormDTO estimateChangePolicyForm) {

        String reportNo = estimateChangePolicyForm.getReportNo();
        Integer caseTimes = estimateChangePolicyForm.getCaseTimes();
        String userId;
        try {
            userId = WebServletContext.getUserId();
        }catch (Exception e){
            userId = ConstValues.SYSTEM;
        }
        if (StringUtils.isNotEmpty(estimateChangePolicyForm.getOperator())){
            userId = estimateChangePolicyForm.getOperator();
        }

        //1 查询 duty_recode 表已存在数据,组装入参传新的数据
        List<EstimateDutyRecordDTO> dutyRecordDTOS = estimateDutyRecordMapper.getRecordsOfRegisterCaseByReportNo(reportNo
                                                            , caseTimes,EstimateUtil.ESTIMATE_TYPE_REGISTRATION);

        LogUtil.audit("未决责任信息入参:{}", JSONObject.toJSONString(estimateChangePolicyForm));
        if (CollectionUtils.isEmpty(dutyRecordDTOS)){
            throw new GlobalBusinessException("未决初始化数据查询为空");
        }
        LogUtil.audit("未决责任信息dutyRecordDTOS:{}", JSONObject.toJSONString(dutyRecordDTOS));

        Map<String, EstimateDutyRecordDTO> recordDTOListMap = new HashMap<>();
        dutyRecordDTOS.stream().forEach(recordDTO->{
            String key = recordDTO.getPolicyNo() + recordDTO.getPlanCode() + recordDTO.getDutyCode() + EstimateUtil.ESTIMATE_TYPE_REGISTRATION;
            LogUtil.audit("未决责任信息组装的key:{}", key);
            recordDTOListMap.put(key, recordDTO);
        });
        LogUtil.audit("未决责任信息组装后:{}", JSONObject.toJSONString(recordDTOListMap));

        Map<String,BigDecimal> ssCoinsRateMap = estimateService.getCoinsRateMap(reportNo);
        // 获取再保的保单，获取主共从共等数据
        Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(reportNo);
        //2 更新 duty_recorde 表最新修正金额
        Map<String, BigDecimal> policyPaySumMap = new HashMap<>();
        Map<String, BigDecimal> policyFeeSumMap = new HashMap<>();
        List<EstimateDutyRecordDTO> recordDTOList = new ArrayList<>();

        boolean isRiskGroupChanged = false; // 判断是否改变方案
        EstimateChangeRiskGroupDTO  riskGroup = null;
        List<EstimateChangePlanDTO> estimatePlanList = null;
        EstimateDutyRecordDTO newEstimateDutyRecord = null;
        List<EstimateDutyRecordDTO> batchNewEstimateDutyRecordList = new ArrayList<EstimateDutyRecordDTO>();
        List<EstimateDutyRecordDTO> batchUpdateEstimateDutyRecordList = new ArrayList<EstimateDutyRecordDTO>();
        for (EstimateChangePolicyDTO estimatePolicyDTO : estimateChangePolicyForm.getEstimatePolicyList()) {
        	CaseBaseEntity caseBase = caseBaseService.getCaseBaseInfo(estimatePolicyDTO.getReportNo(), estimatePolicyDTO.getPolicyNo(), estimatePolicyDTO.getCaseTimes());
        	if(caseBase == null) {
  		    	continue;
  		    }
        	if(estimatePolicyDTO.getRiskGroupList()!=null && estimatePolicyDTO.getRiskGroupList().size() == 1) {
        		riskGroup = estimatePolicyDTO.getRiskGroupList().get(0);
        		estimatePlanList = riskGroup.getEstimatePlanList();
        		isRiskGroupChanged = !StrUtil.equals(caseBase.getRiskGroupNo(), riskGroup.getRiskGroupNo());
        		if(isRiskGroupChanged) {
        			caseBaseService.updateRiskGroup(caseBase.getIdClmCaseBase(),riskGroup.getRiskGroupNo(),riskGroup.getRiskGroupName());
             		estimateDutyRecordMapper.updateEffectiveByCaseNo(ListUtil.of(caseBase.getCaseNo()),estimatePolicyDTO.getCaseTimes());
                    estimatePlanDAO.delEstimatePlanByCaseNo(ListUtil.of(caseBase.getCaseNo()),estimatePolicyDTO.getCaseTimes());
                    estimateDutyDAO.delEstimateDutyByCaseNo(ListUtil.of(caseBase.getCaseNo()),estimatePolicyDTO.getCaseTimes());
                    saveEstimatePlanDutyList(estimatePolicyDTO.getIdAhcsEstimatePolicy(), estimatePlanList, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, riskGroup.getRiskGroupNo(),riskGroup.getRiskGroupName());
                    policyPayService.deletePolicyPays(reportNo, caseTimes);
        		}
        	}else {
        		estimatePlanList = estimatePolicyDTO.getEstimatePlanList();
        		isRiskGroupChanged = false;
        	}
        	if(estimatePlanList == null) {
        		continue;
        	}
			for (EstimateChangePlanDTO estimatePlanDTO : estimatePlanList) {
				for (EstimateDutyRecordDTO estimateDutyRecordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
					if(isRiskGroupChanged) {
						newEstimateDutyRecord = new EstimateDutyRecordDTO();
			            BeanUtils.copyProperties(estimateDutyRecordDTO, newEstimateDutyRecord);
			            newEstimateDutyRecord.setCreatedBy(userId);
			            newEstimateDutyRecord.setUpdatedBy(userId);
			            newEstimateDutyRecord.setIdAhcsEstimateDutyRecord(UuidUtil.getUUID());
			            newEstimateDutyRecord.setTaskId(TacheConstants.REPORT_TRACK);
			            newEstimateDutyRecord.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
			            newEstimateDutyRecord.setPlanCode(estimatePlanDTO.getPlanCode());
			            newEstimateDutyRecord.setPolicyNo(estimatePlanDTO.getPolicyNo());
			            newEstimateDutyRecord.setRiskGroupName(riskGroup.getRiskGroupName());
			            newEstimateDutyRecord.setRiskGroupNo(riskGroup.getRiskGroupNo());
			        	recordDTOList.add(newEstimateDutyRecord);
			        	EstimateUtil.calPolicyPayAndFeeSumAmount(policyPaySumMap, policyFeeSumMap,
			        			newEstimateDutyRecord);
			        	batchNewEstimateDutyRecordList.add(newEstimateDutyRecord);
					}else {
						String key = estimateDutyRecordDTO.getPolicyNo() + estimateDutyRecordDTO.getPlanCode()
							+ estimateDutyRecordDTO.getDutyCode() + EstimateUtil.ESTIMATE_TYPE_REGISTRATION;
						// 更新为入参里的金额
						if (recordDTOListMap.containsKey(key)) {
                            Date archiveTime = new Date();
							EstimateDutyRecordDTO recordDTO = recordDTOListMap.get(key);
							recordDTO.setEstimateAmount(estimateDutyRecordDTO.getEstimateAmount());
							recordDTO.setArbitrageFee(estimateDutyRecordDTO.getArbitrageFee());
							recordDTO.setLawsuitFee(estimateDutyRecordDTO.getLawsuitFee());
							recordDTO.setCommonEstimateFee(estimateDutyRecordDTO.getCommonEstimateFee());
							recordDTO.setLawyerFee(estimateDutyRecordDTO.getLawyerFee());
							recordDTO.setExecuteFee(estimateDutyRecordDTO.getExecuteFee());
							recordDTO.setVerifyAppraiseFee(estimateDutyRecordDTO.getVerifyAppraiseFee());
							recordDTO.setInquireFee(estimateDutyRecordDTO.getInquireFee());
							recordDTO.setOtherFee(estimateDutyRecordDTO.getOtherFee());
							recordDTO.setSpecialSurveyFee(estimateDutyRecordDTO.getSpecialSurveyFee());
							recordDTO.setRiskGroupName(riskGroup != null ? riskGroup.getRiskGroupName():null);
							recordDTO.setRiskGroupNo(riskGroup != null ? riskGroup.getRiskGroupNo(): null);
							recordDTO.setUpdatedBy(userId);
							recordDTO.setChgPayValue(estimateDutyRecordDTO.getEstimateAmount()==null?new BigDecimal(0):estimateDutyRecordDTO.getEstimateAmount());
                            if (ssCoinsRateMap.containsKey(estimateDutyRecordDTO.getPolicyNo())) {
                                //0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)
                                String coinsuranceType = null;
                                List<CoinsureDTO> coinsureDTOList = coinsuranceMap.get(recordDTO.getPolicyNo());
                                if (!CollectionUtils.isEmpty(coinsureDTOList)) {
                                    coinsuranceType = coinsureDTOList.get(0).getCoinsuranceType();
                                }
                                if ("0".equals(coinsuranceType) || "1".equals(coinsuranceType)){
                                    BigDecimal coinsPayValue = recordDTO.getChgPayValue().multiply(nvl(ssCoinsRateMap.get(estimateDutyRecordDTO.getPolicyNo()), 0))
                                            .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                                    recordDTO.setSsCoinsPayValue(coinsPayValue);
                                    recordDTO.setSsCoinsChgPayValue(coinsPayValue);
                                } else {
                                    recordDTO.setSsCoinsPayValue(recordDTO.getEstimateAmount());
                                    recordDTO.setSsCoinsChgPayValue(recordDTO.getChgPayValue());
                                }

                            }
                            recordDTO.setArchiveTime(archiveTime);
							recordDTOList.add(recordDTO);
							EstimateUtil.calPolicyPayAndFeeSumAmount(policyPaySumMap, policyFeeSumMap,
									estimateDutyRecordDTO);
							batchUpdateEstimateDutyRecordList.add(recordDTO);
						} else {
							LogUtil.audit("未决修改duty_record表存在此责任: {}", key);
						}
					}
				}
			}

        }

        LogUtil.audit("未决责任-recordDTOList:{}", JSONObject.toJSONString(recordDTOList));
        if(batchNewEstimateDutyRecordList.size() > 0) {
            estimateDutyRecordMapper.addDutyRecordList(batchNewEstimateDutyRecordList);
        }

//      estimateDutyRecordMapper.batchUpdateEstimateDutyRecord(recordDTOList);
        if(batchUpdateEstimateDutyRecordList.size() > 0) {
        	estimateDutyRecordMapper.batchUpdateEstimateDutyRecord(batchUpdateEstimateDutyRecordList);
        }

        //定义 history 表 和 change 关系键
        String idFlagHistoryChange = UuidUtil.getUUID();

        //3 插入estimate_change表,本次修改的金额
        saveEstimateChange(estimateChangePolicyForm, policyPaySumMap, policyFeeSumMap, idFlagHistoryChange);

        //4 组装为history, 插入history表
        saveDutyHistory(idFlagHistoryChange, recordDTOList);

        //5 更新 estimate_record 表生效时间
        ClmsEstimateRecord record = new ClmsEstimateRecord();
        record.setEffectiveTime(new Date());
        record.setReportNo(reportNo);
        record.setCaseTimes(caseTimes);
        record.setRecordUserId(userId);
        record.setUpdatedBy(userId);
        BigDecimal sumAmount = new BigDecimal(0);
        for (Map.Entry<String, BigDecimal> entry : policyPaySumMap.entrySet()) {
            sumAmount = sumAmount.add(entry.getValue());
        }
        for (Map.Entry<String, BigDecimal> entry : policyFeeSumMap.entrySet()) {
            sumAmount = sumAmount.add(entry.getValue());
        }
        record.setEstimateAmount(sumAmount);
        estimateRecordMapper.updateEstimateRecordInfo(record);
        //更新估损信息的taskId
        lossEstimationService.updateLossEstimationTaskId(reportNo, caseTimes, idFlagHistoryChange, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW);
        /**if(StringUtils.isNotEmpty(companyId)) {
            //答复
            ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
            problemCaseRequestDTO.setCompanyId(companyId);
            RequestData requestData = new RequestData();
            requestData.setRegistNo(reportNo);
//            requestData.setCaseConclusion("2");
            requestData.setProblemNo(idFlagHistoryChange);
            requestData.setProblemType("98");
            try {
                requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
            } catch (ParseException e) {
                LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
            }
            requestData.setRemark("刷估已完成。");
            problemCaseRequestDTO.setRequestData(requestData);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",reportNo, JSON.toJSONString(problemCaseRequestDTO));
            ProblemCaseResponseDTO response = tpaFeign.response(problemCaseRequestDTO);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",reportNo, JSON.toJSONString(response));
        }*/
        LogUtil.audit("未决估损修改完成 报案号:{} ,案件号: {}",reportNo,idFlagHistoryChange);
        //返回问题件编号
        return idFlagHistoryChange;
    }

    @Override
    public List<EstimateChangePolicyDTO> getApplyEstimatePolicyList(String reportNo, Integer caseTimes) throws Exception {
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).list();
        if(CollectionUtils.isEmpty(changeApplyEntityList)){
            log.info("未查询到审批中的未决修正申请，reportNo:{}，caseTimes:{}", reportNo, caseTimes);
            throw new GlobalBusinessException("未查询到审批中的未决修正申请");
        }
        EstimateChangeApplyEntity estimateChangeApply = changeApplyEntityList.get(0);
        EstimateChangePolicyFormDTO estimateChangePolicyFormDTO = JsonUtils.toObject(estimateChangeApply.getApplyParam(), EstimateChangePolicyFormDTO.class);
        if(Objects.nonNull(estimateChangePolicyFormDTO)){
            return estimateChangePolicyFormDTO.getEstimatePolicyList();
        }
        return null;
    }

    private void saveEstimateChange(EstimateChangePolicyFormDTO estimateChangePolicyForm, Map<String, BigDecimal> policyPaySumMap, Map<String, BigDecimal> policyFeeSumMap, String idFlagHistoryChange) {
        String reportNo = estimateChangePolicyForm.getReportNo();
        Integer caseTimes = estimateChangePolicyForm.getCaseTimes();
        //获取责任级赔款信息
        List<EstimateDutyRecordDTO> dutyRecordDTOList = getDutyRecordDtoList(estimateChangePolicyForm);
        //查询每个保单号的剩余保额
        List<EstimateChangeDTO> resultList = Optional.ofNullable(estimateChangeMapper.getPolicyRegisterAmount(reportNo, caseTimes)).orElse(new ArrayList<>());
        List<EstimateChangeDTO> estimateChangeList = new ArrayList<>();
        EstimateChangeDTO estimateChangeDTO;
        for (EstimateChangeDTO dto: resultList){
            estimateChangeDTO = new EstimateChangeDTO();
            estimateChangeDTO.setReportNo(reportNo);
            estimateChangeDTO.setCaseTimes(caseTimes);
            estimateChangeDTO.setPolicyNo(dto.getPolicyNo());
            estimateChangeDTO.setReason(estimateChangePolicyForm.getEstimateChangeReason());
            estimateChangeDTO.setMaxPayAmount(dto.getMaxPayAmount());
            estimateChangeDTO.setSumDutyPayAmount(policyPaySumMap.get(dto.getPolicyNo()));
            estimateChangeDTO.setSumDutyFeeAmount(policyFeeSumMap.get(dto.getPolicyNo()));
            BigDecimal changeAmount = policyFeeSumMap.get(dto.getPolicyNo())==null?new BigDecimal(0): policyFeeSumMap.get(dto.getPolicyNo());
            String uuid = UuidUtil.getUUID();
            estimateChangeDTO.setChangeAmount(policyPaySumMap.get(dto.getPolicyNo()).add(changeAmount));
            estimateChangeDTO.setIdclmsEstimateChange(uuid);
            estimateChangeDTO.setIdFlagHistoryChange(idFlagHistoryChange);
            estimateChangeList.add(estimateChangeDTO);
        }
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).list();
        if(!changeApplyEntityList.isEmpty()){
            String idEstimateChangeApply = changeApplyEntityList.get(0).getId();
            for(EstimateChangeDTO dto: estimateChangeList){
                dto.setIdEstimateChangeApply(idEstimateChangeApply);
            }
        }
        estimateChangeService.addEstimateChangeList(estimateChangeList,dutyRecordDTOList);
    }

    private List<EstimateDutyRecordDTO> getDutyRecordDtoList(EstimateChangePolicyFormDTO estimateChangePolicyForm) {
        //险种级赔款信息
        List<EstimateChangePlanDTO> estimatePlanList = new ArrayList<>();
        //责任级赔款信息
        List<EstimateDutyRecordDTO> estimateDutyRecordDTOList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(estimateChangePolicyForm.getEstimatePolicyList())){
            List<EstimateChangePolicyDTO> estimateChangePolicyDTOList = estimateChangePolicyForm.getEstimatePolicyList();
            for(EstimateChangePolicyDTO estimateChangePolicyDTO:estimateChangePolicyDTOList){
                if(ObjectUtil.isNotEmpty(estimateChangePolicyDTO.getEstimatePlanList())){
                    estimatePlanList.addAll(estimateChangePolicyDTO.getEstimatePlanList());
                    for(EstimateChangePlanDTO estimateChangePlanDTO:estimatePlanList){
                        if(ObjectUtil.isNotEmpty(estimateChangePlanDTO.getEstimateDutyRecordList())){
                            estimateDutyRecordDTOList.addAll(estimateChangePlanDTO.getEstimateDutyRecordList());
                        }
                    }
                }
            }
        }
        return estimateDutyRecordDTOList;
    }

    private String saveDutyHistory(String idFlagHistoryChange, List<EstimateDutyRecordDTO> recordDTOList) {
        //查询 duty_record表数据后插入history和change(原因为空)，再修改的这次也插入history和change(原因为本次修改原因)
        List<EstimateDutyHistoryDTO> historyDTOList = new ArrayList<>();
        recordDTOList.stream().forEach(recordDTO->{
            String uuid = UuidUtil.getUUID();
            EstimateDutyHistoryDTO historyDTO = new EstimateDutyHistoryDTO();
            BeanUtils.copyProperties(recordDTO,historyDTO);
            historyDTO.setDutyEstimateAmount(recordDTO.getEstimateAmount());
            historyDTO.setEffectiveTime(recordDTO.getArchiveTime());//duty_record归档时间作为生效时间
            historyDTO.setIdAhcsEstimatDutyHistory(uuid);
            historyDTO.setIdFlagHistoryChange(idFlagHistoryChange);
            historyDTO.setChgPayValue(recordDTO.getEstimateAmount());
            historyDTOList.add(historyDTO);
        });
        estimateDutyHistoryMapper.addHistoryRecord(historyDTOList);
        return idFlagHistoryChange;
    }

    private void saveEstimatePlanDutyList(String estimatePolicyId, List<EstimateChangePlanDTO> estimateChangePlanList, String estimateType, String riskGroupNo, String riskGroupName){
        String userId = WebServletContext.getUserId();
        EstimatePlanDTO newEstimatePlan;
        List<EstimatePlanDTO> newEstimatePlanList = new ArrayList<>();
        EstimateDutyDTO newEstimateDuty;
        List<EstimateDutyDTO> newEstimateDutyList = new ArrayList<>();
        for (EstimateChangePlanDTO estimateChangePlan : estimateChangePlanList) {
        	newEstimatePlan = new EstimatePlanDTO();
            BeanUtils.copyProperties(estimateChangePlan,newEstimatePlan);
      		newEstimatePlan.setIdAhcsEstimatePolicy(estimatePolicyId);
        	newEstimatePlan.setCreatedBy(userId);
        	newEstimatePlan.setUpdatedBy(userId);
        	newEstimatePlan.setRiskGroupName(riskGroupName);
        	newEstimatePlan.setRiskGroupNo(riskGroupNo);
        	newEstimatePlan.setIdAhcsEstimatePlan(UuidUtil.getUUID());
            newEstimatePlanList.add(newEstimatePlan);
            for (EstimateDutyRecordDTO estimateChangeDutyRecord : estimateChangePlan.getEstimateDutyRecordList()) {
                newEstimateDuty = new EstimateDutyDTO();
                BeanUtils.copyProperties(estimateChangeDutyRecord, newEstimateDuty);
                newEstimateDuty.setIdAhcsEstimatePlan(newEstimatePlan.getIdAhcsEstimatePlan());
                newEstimateDuty.setIdAhcsEstimateDuty(UuidUtil.getUUID());
                newEstimateDuty.setRiskGroupName(riskGroupName);
                newEstimateDuty.setRiskGroupNo(riskGroupNo);
                newEstimateDuty.setEstimateType(estimateType);
                newEstimateDuty.setCreatedBy(userId);
                newEstimateDuty.setUpdatedBy(userId);
                newEstimateDuty.setPlanCode(newEstimatePlan.getPlanCode());
                newEstimateDuty.setPolicyNo(newEstimatePlan.getPolicyNo());
                newEstimateDutyList.add(newEstimateDuty);
            }
        }
        if(ListUtils.isNotEmpty(newEstimatePlanList)){
            estimatePlanDAO.addBatchEstimatePlan(newEstimatePlanList);
        }
        estimateDutyDAO.addBatchEstimateDuty(newEstimateDutyList);
    }

    private void startEstimateChangeReview(String reportNo, Integer caseTimes, BigDecimal applyTotalAmount, String taskId, String selectedUserId, String taskIdNew) throws Exception {
        String managerUserId = null;
        String managerUserName = null;
        if(!StringUtils.isEmptyStr(selectedUserId)){
            String [] parts = selectedUserId.split("-",2);
            managerUserId = parts[0];
            managerUserName = parts[1];
        }
        String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
        String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        String userId = userInfoDTO.getUserCode();
        TaskInfoDTO startTask = new TaskInfoDTO();
        startTask.setAssigner(managerUserId);
        startTask.setAssigneeName(managerUserName);
        startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
        startTask.setTaskId(taskIdNew);
        startTask.setReportNo(reportNo);
        startTask.setCaseTimes(caseTimes);
        startTask.setTaskDefinitionBpmKey(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW);
        startTask.setAssigneeTime(new Date());
        startTask.setStatus(BpmConstants.TASK_STATUS_PARALLEL_PENDING);
        startTask.setCreatedBy(userId);
        startTask.setUpdatedBy(userId);
        startTask.setApplyer(userId);
        startTask.setApplyerName(userInfoDTO.getUserName());
        startTask.setPreTaskId(taskId);

        Integer taskGrade = permissionService.getPermissionGrade(Constants.PERMISSION_REGIST, ConfigConstValues.HQ_DEPARTMENT,applyTotalAmount);
        if(taskGrade == null){
            //查不到等级，默认给2级
            LogUtil.audit("使用默认案件等级");
            taskGrade = 5;
        }

        PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_REGIST,departmentCode,taskGrade);
        Integer auditGrade = permissionUser.getGrade();
        if(auditGrade == null){
            LogUtil.audit("使用默认审批等级");
            auditGrade = taskGrade;
        }
        startTask.setTaskGrade(taskGrade);
        startTask.setAuditGrade(auditGrade);
        if(StringUtils.isEmptyStr(comCode)){
            startTask.setDepartmentCode(permissionUser.getComCode());
        } else {
            startTask.setDepartmentCode(comCode);
        }
        taskInfoMapper.addTaskInfo(startTask);

    }

    @Override
    public EstimateChangeApplyVO getAuditEstimateChangeApplyVO(String reportNo, Integer caseTimes) throws Exception {
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).list();
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(reportNo);
        taskInfoDTO.setCaseTimes(caseTimes);
        taskInfoDTO.setTaskDefinitionBpmKey(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW);
        List<TaskInfoVO> taskInfo = taskInfoMapper.getTaskInfo(taskInfoDTO);
        if(CollectionUtils.isEmpty(taskInfo) || CollectionUtils.isEmpty(changeApplyEntityList)){
            log.info("未决修正审批详情未查询到数据，reportNo：{}，caseTimes：{}", reportNo, caseTimes);
            throw new GlobalBusinessException("请求数据错误");
        }
        EstimateChangeApplyEntity estimateChangeApply = changeApplyEntityList.get(0);
        EstimateChangeApplyVO estimateChangeApplyVO = new EstimateChangeApplyVO();
        BeanUtils.copyProperties(estimateChangeApply, estimateChangeApplyVO);
        if (null != estimateChangeApplyVO.getApplyUm()) {
            estimateChangeApplyVO.setApplyorName(userInfoService.getUserInfoDTO(estimateChangeApplyVO.getApplyUm()).getUserName());
        }
        if (null != estimateChangeApplyVO.getAuditUm()) {
            estimateChangeApplyVO.setAuditorName(userInfoService.getUserInfoDTO(estimateChangeApplyVO.getAuditUm()).getUserName());
        }
        if (ConstValues.SYSTEM_UM.equalsIgnoreCase(estimateChangeApplyVO.getApplyUm())) {
            estimateChangeApplyVO.setApplyorName(ConstValues.SYSTEM_NAME);
        }
        estimateChangeApplyVO.setTaskId(taskInfo.get(0).getTaskId());
        return estimateChangeApplyVO;
    }

    @Override
    public List<EstimateChangeApplyVO> getEstimateChangeApplyVOList(String reportNo, Integer caseTimes) throws Exception {
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_2).list();
        List<EstimateChangeApplyVO> estimateChangeApplyVOList = new ArrayList<>();
        if (ListUtils.isNotEmpty(changeApplyEntityList)) {
            for (EstimateChangeApplyEntity item : changeApplyEntityList) {
                EstimateChangeApplyVO estimateChangeApplyVO = new EstimateChangeApplyVO();
                BeanUtils.copyProperties(item, estimateChangeApplyVO);
                if (null != item.getApplyUm()){
                    estimateChangeApplyVO.setApplyorName(userInfoService.getUserInfoDTO(item.getApplyUm()).getUserName());
                }
                if (null != item.getAuditUm()){
                    estimateChangeApplyVO.setAuditorName(userInfoService.getUserInfoDTO(item.getAuditUm()).getUserName());
                }
                if (ConstValues.SYSTEM_UM.equals(item.getApplyUm())) {
                    estimateChangeApplyVO.setApplyorName(ConstValues.SYSTEM_NAME);
                }
                estimateChangeApplyVOList.add(estimateChangeApplyVO);
            }
        }
        return estimateChangeApplyVOList;
    }

    @Override
    public List<EstimateChangeApplyVO> getEstimateChangeApplyById(String id) throws Exception {
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getId, id)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_2).list();
        List<EstimateChangeApplyVO> estimateChangeApplyVOList = new ArrayList<>();
        if (ListUtils.isNotEmpty(changeApplyEntityList)) {
            for (EstimateChangeApplyEntity item : changeApplyEntityList) {
                EstimateChangeApplyVO estimateChangeApplyVO = new EstimateChangeApplyVO();
                BeanUtils.copyProperties(item, estimateChangeApplyVO);
                if (null != item.getApplyUm()){
                    estimateChangeApplyVO.setApplyorName(userInfoService.getUserInfoDTO(item.getApplyUm()).getUserName());
                }
                if (null != item.getAuditUm()){
                    estimateChangeApplyVO.setAuditorName(userInfoService.getUserInfoDTO(item.getAuditUm()).getUserName());
                }
                if (ConstValues.SYSTEM_UM.equals(item.getApplyUm())) {
                    estimateChangeApplyVO.setApplyorName(ConstValues.SYSTEM_NAME);
                }
                if(ConstValues.AUDIT_AGREE_CODE.equals(item.getAuditOpinion())){
                    estimateChangeApplyVO.setAuditOpinion("同意");
                }else{
                    estimateChangeApplyVO.setAuditOpinion("不同意");
                }
                estimateChangeApplyVOList.add(estimateChangeApplyVO);
            }
        }
        return estimateChangeApplyVOList;
    }

    @Override
    @Transactional
    public List<String> sendAuditEstimateChangeApply(EstimateChangeApplyVO estimateChangeApplyVO) throws Exception {
        log.info("未决修正审批发送reportNo={},taskId={}", estimateChangeApplyVO.getReportNo(), estimateChangeApplyVO.getTaskId());
        log.info("未决修正审批获取用户, WebServletContext.getUserId()={}", WebServletContext.getUserId());
        List<String> msgList = new ArrayList<>();
        String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        String auditOpinion = estimateChangeApplyVO.getAuditOpinion();
        String reportNo = estimateChangeApplyVO.getReportNo();
        Integer caseTimes = estimateChangeApplyVO.getCaseTimes();
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).list();
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(reportNo);
        taskInfoDTO.setCaseTimes(caseTimes);
        taskInfoDTO.setTaskDefinitionBpmKey(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW);
        List<TaskInfoVO> taskInfo = taskInfoMapper.getTaskInfo(taskInfoDTO);
        if(CollectionUtils.isEmpty(taskInfo) || CollectionUtils.isEmpty(changeApplyEntityList)){
            log.info("未决修正审批详情未查询到数据，reportNo：{}，caseTimes：{}", reportNo, caseTimes);
            throw new GlobalBusinessException("请求数据错误");
        }

        boolean pass = true;
        boolean agree = ConstValues.AUDIT_AGREE_CODE.equals(auditOpinion);
        String taskIdNew = UuidUtil.getUUID();
        EstimateChangeApplyEntity estimateChangeApply = changeApplyEntityList.get(0);
        if(agree){
            pass = estimateService.checkRegistPremission(reportNo,estimateChangeApplyVO.getTaskId(),
                    loginUm,estimateChangeApply.getApplyAmount(),
                    BpmConstants.OC_ESTIMATE_CHANGE_REVIEW,null,null);
            LogUtil.audit("未决修正审批权限是否满足={}",pass);

            if(pass){
                msgList.add("未决修正完成");
                //操作记录
                operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, "通过", estimateChangeApplyVO.getAuditRemark(), loginUm);
                // 未决修正入库
                estimateChangeService.estimateChange(JsonUtils.toObject(estimateChangeApply.getApplyParam(), EstimateChangePolicyFormDTO.class));

            }else{
                msgList.add("提交成功，待上级审批人继续处理");
                //操作记录
                operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, "通过", StringUtils.isEmptyStr(estimateChangeApplyVO.getAuditRemark()) ? " 待上级审批人继续处理" : estimateChangeApplyVO.getAuditRemark().concat(" 待上级审批人继续处理"), loginUm);
                EstimateChangeApplyEntity changeApplyEntityNew = new EstimateChangeApplyEntity();
                BeanUtils.copyProperties(estimateChangeApply, changeApplyEntityNew);
                changeApplyEntityNew.setId(null);
                changeApplyEntityNew.setCreatedBy(loginUm);
                changeApplyEntityNew.setUpdatedBy(loginUm);
                changeApplyEntityNew.setApplyUm(loginUm);
                changeApplyEntityNew.setApplyTimes(estimateChangeApply.getApplyTimes());
                changeApplyEntityNew.setArchiveTime(new Date());
                estimateChangeApplyService.save(changeApplyEntityNew);
                //生成审批任务
                startEstimateChangeReview(reportNo, caseTimes, changeApplyEntityNew.getApplyAmount(),
                        estimateChangeApplyVO.getTaskId(),estimateChangeApplyVO.getSelectedUserId(),taskIdNew);
            }

        }else{
            LogUtil.audit("未决修正审批不同意, reportNo={}, caseTimes={}", reportNo, caseTimes);
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, "不通过", estimateChangeApplyVO.getAuditRemark(), loginUm);
            //未决审批中的未决修正审批不同意时添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_PENDING);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setCaseTimes(caseTimes);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            TaskInfoDTO taskDto = taskInfoMapper.getTaskDtoByTaskId(estimateChangeApplyVO.getTaskId());
            noticeService.saveNotices(noticesDTO,taskDto.getApplyer());
            //审批不同意，删除本次未决中的事故估损信息
            lossEstimationService.removeLossEstimation(reportNo, caseTimes, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW);
        }
        EstimateChangeApplyEntity changeApplyEntityUpdate = new EstimateChangeApplyEntity();
        changeApplyEntityUpdate.setId(estimateChangeApply.getId());
        changeApplyEntityUpdate.setAuditUm(loginUm);
        changeApplyEntityUpdate.setAuditDate(new Date());
        changeApplyEntityUpdate.setAuditOpinion(auditOpinion);
        changeApplyEntityUpdate.setAuditRemark(estimateChangeApplyVO.getAuditRemark());
        changeApplyEntityUpdate.setAuditStatus("2");
        estimateChangeApplyService.updateById(changeApplyEntityUpdate);

        bpmService.completeTask_oc(reportNo,caseTimes,BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, estimateChangeApplyVO.getTaskId());

        if(agree && !pass){
            unSettleApproveWaitingCenterServiceImpl.createInstance(taskIdNew);
        } else {
            // 未决修正审批同步对接办事中心
            unSettleApproveWaitingCenterServiceImpl.createInstance(estimateChangeApplyVO.getTaskId());
        }

        return msgList;
    }

    @Override
    public boolean checkEstimateChangePending(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                .eq(EstimateChangeApplyEntity::getReportNo, reportNo)
                .eq(EstimateChangeApplyEntity::getCaseTimes, caseTimes)
                .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).list();
        return !CollectionUtils.isEmpty(changeApplyEntityList);
    }

    @Override
    public EstimateChangeVo getEstimateChangeVo(String reportNo, int caseTimes, String idFlagHistoryChange, String isEstimate) {
        EstimateChangeVo estimateChangeVo = new EstimateChangeVo();
        //未立案不展示
        if (!registerCaseService.isExistRegisterRecord(reportNo, caseTimes)) {
            return estimateChangeVo;
        }
        //查询未决组装信息 start
        List<EstimateDutyHistoryDTO> historyDTOS = estimateDutyHistoryMapper.getDutyHistoryListByIdFlag(idFlagHistoryChange);
        String departmentName = getDepartmentName(reportNo);
        //3 组装上面history表和duty_record表数据
        //组装修正change表主键与history对象关联关系
        Map<String, List<EstimateDutyHistoryDTO>> idFlagPolicyNoAndHistoryListMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(historyDTOS)){
            historyDTOS.stream().forEach(historyDTO ->{
                String idFlagHistory = historyDTO.getIdFlagHistoryChange();
                if (StringUtils.isNotEmpty(idFlagHistory)) {
                    String idFlagPolicyNoKey = historyDTO.getIdFlagHistoryChange() + historyDTO.getPolicyNo();
                    List<EstimateDutyHistoryDTO> tempList = new ArrayList<>();
                    if (idFlagPolicyNoAndHistoryListMap.containsKey(idFlagPolicyNoKey)) {
                        tempList = idFlagPolicyNoAndHistoryListMap.get(idFlagPolicyNoKey);
                    }
                    tempList.add(historyDTO);
                    idFlagPolicyNoAndHistoryListMap.put(idFlagPolicyNoKey, tempList);
                }
            });
        }
        //根据报案号查询保单险种对应的名字
        List<EstimateChangePlanDTO> policyPlanInfoList = estimateDutyHistoryMapper.getEstimatePlanCodeList(reportNo,caseTimes);
        Map<String, String> policyPlanNameMap = new HashMap<>();
        policyPlanInfoList.stream().forEach(info ->{
            policyPlanNameMap.put(info.getPolicyNo()+"_"+info.getPlanCode(),info.getPlanName()+"("+info.getPlanCode()+")");
        });

        // 查change表
        List<EstimateChangeDTO> changeDTOS = estimateChangeMapper.getEstimateChangeListByIdFlagHistoryChange(reportNo,idFlagHistoryChange);
        Map<String, List<EstimateChangeDTO>> idFlagAndChangeMap = new LinkedHashMap<>();
        if(!CollectionUtils.isEmpty(changeDTOS)){
            //记录每次更改id标识和对应的保单list
            changeDTOS.stream().forEach(changeDTO->{
                String idFlag = changeDTO.getIdFlagHistoryChange();
                if (StringUtils.isNotEmpty(idFlag)){
                    List<EstimateChangeDTO> list = new ArrayList<>();
                    if (idFlagAndChangeMap.containsKey(idFlag)){
                        list = idFlagAndChangeMap.get(idFlag);
                    }
                    list.add(changeDTO);
                    idFlagAndChangeMap.put(idFlag, list);
                }
            });
        }
        //组装history数据
        for (Map.Entry<String, List<EstimateChangeDTO>> entry : idFlagAndChangeMap.entrySet()) {
            String idFlag = entry.getKey();
            List<EstimateChangeDTO> changeList = entry.getValue();
            List<EstimateChangePolicyDTO> estimatePolicyList = new ArrayList<>();
            //遍历保单号change
            for (EstimateChangeDTO changeDTO : changeList){
                String policyNo = changeDTO.getPolicyNo();
                String idFlagPolicyNoKey = idFlag + policyNo;
                List<EstimateDutyHistoryDTO> dutyHistoryDTOS = idFlagPolicyNoAndHistoryListMap.get(idFlagPolicyNoKey);
                //组装当前保单下的险种、责任信息
                Map<String, List<EstimateDutyRecordDTO>> planDutyListMap = new HashMap<>();
                Map<String,String> tmpRiskGroupNo = new HashMap<>();
                Map<String,String> tmpRiskGroupName = new HashMap<>();
                dutyHistoryDTOS.stream().forEach(result ->{
                    EstimateDutyRecordDTO dutyRecordDTO = new EstimateDutyRecordDTO();
                    dutyRecordDTO.setEstimateAmount(result.getDutyEstimateAmount());
                    dutyRecordDTO.setDutyCode(result.getDutyCode());
                    dutyRecordDTO.setDutyName(result.getDutyName());
                    dutyRecordDTO.setPolicyNo(result.getPolicyNo());
                    dutyRecordDTO.setCaseNo(result.getCaseNo());
                    dutyRecordDTO.setBaseAmountPay(result.getBaseAmountPay());
                    dutyRecordDTO.setPlanCode(result.getPlanCode());
                    dutyRecordDTO.setArbitrageFee(result.getArbitrageFee()==null?null:result.getArbitrageFee());
                    dutyRecordDTO.setLawsuitFee(result.getLawsuitFee()==null?null:result.getLawsuitFee());
                    dutyRecordDTO.setCommonEstimateFee(result.getCommonEstimateFee()==null?null:result.getCommonEstimateFee());
                    dutyRecordDTO.setLawyerFee(result.getLawyerFee()==null?null:result.getLawyerFee());
                    dutyRecordDTO.setExecuteFee(result.getExecuteFee()==null?null:result.getExecuteFee());
                    dutyRecordDTO.setVerifyAppraiseFee(result.getVerifyAppraiseFee()==null?null:result.getVerifyAppraiseFee());
                    dutyRecordDTO.setInquireFee(result.getInquireFee()==null?null:result.getInquireFee());
                    dutyRecordDTO.setOtherFee(result.getOtherFee()==null?null:result.getOtherFee());
                    dutyRecordDTO.setSpecialSurveyFee(result.getSpecialSurveyFee());
                    //组装每个险种层下的责任
                    List<EstimateDutyRecordDTO> tempPlanDutyList = new ArrayList<>();
                    String key = result.getPolicyNo() +"_"+ result.getPlanCode();
                    if (planDutyListMap.containsKey(key)){
                        tempPlanDutyList = planDutyListMap.get(key);
                    }
                    tempPlanDutyList.add(dutyRecordDTO);
                    planDutyListMap.put(key,tempPlanDutyList);
                    tmpRiskGroupNo.put(result.getPolicyNo(), result.getRiskGroupNo());
                    tmpRiskGroupName.put(result.getPolicyNo(), result.getRiskGroupName());
                });
                //组装险种
                List<EstimateChangePlanDTO> estimatePlanList = new ArrayList<>();
                planDutyListMap.forEach((key, value) -> {
                    EstimateChangePlanDTO planDTO = new EstimateChangePlanDTO();
                    String planCode = key.split("_")[1];
                    planDTO.setPlanCode(planCode);
                    planDTO.setPlanName(policyPlanNameMap.get(key));
                    planDTO.setEstimateDutyRecordList(value);
                    estimatePlanList.add(planDTO);
                });
                //组装保单
                EstimateChangePolicyDTO policyDTO = new EstimateChangePolicyDTO();
                policyDTO.setEstimatePlanList(estimatePlanList);
                policyDTO.setCaseDepartment(departmentName);
                policyDTO.setPolicyNo(policyNo);
                if(!StrUtil.isEmpty(tmpRiskGroupNo.get(policyNo))) {
                    policyDTO.setRiskGroupList(ListUtil.of(EstimateChangeRiskGroupDTO.builder()
                            .riskGroupNo(tmpRiskGroupNo.get(policyNo)).riskGroupName(tmpRiskGroupName.get(policyNo))
                            .estimatePlanList(estimatePlanList).build()));
                }else {
                    policyDTO.setEstimatePlanList(estimatePlanList);
                }
                estimatePolicyList.add(policyDTO);
                if("1".equals(isEstimate)){
                    estimateChangeVo.setIsEstimate("1");//1-立案；2-未决
                    //组装立案审批信息
                    List<EstimateChangeApplyVO> caseRegisterApplyVOs = new ArrayList<>();
                    List<CaseRegisterApplyVO> lastApplyInfos = registerCaseService.getLastCaseRegisterApplyVOList(reportNo, caseTimes);
                    for (CaseRegisterApplyVO lastApplyInfo : lastApplyInfos){
                        EstimateChangeApplyVO estimateChangeApplyVO = getEstimateChangeApplyVO(lastApplyInfo);
                        caseRegisterApplyVOs.add(estimateChangeApplyVO);
                    }
                    estimateChangeVo.setEstimateChangeApplyVOList(caseRegisterApplyVOs);
                }else{
                    estimateChangeVo.setIsEstimate("2");
                    //组装未决审批信息
                    try {
                        List<EstimateChangeApplyVO> estimateChangeApplyVOList = estimateChangeService.getEstimateChangeApplyById(changeDTO.getIdEstimateChangeApply());
                        if(!estimateChangeApplyVOList.isEmpty()){
                            estimateChangeVo.setEstimateChangeApplyVOList(estimateChangeApplyVOList);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            estimateChangeVo.setEstimatePolicyList(estimatePolicyList);
        }
        //查询未决组装信息 end
       //组装事故估损信息
        AccidentLossVo accidentLossVo = lossEstimationService.getAccisentLossVO(reportNo,caseTimes,idFlagHistoryChange);
        estimateChangeVo.setAccidentLossVo(accidentLossVo);
        return estimateChangeVo;
    }
    private EstimateChangeApplyVO getEstimateChangeApplyVO(CaseRegisterApplyVO lastApplyInfo) {
        EstimateChangeApplyVO estimateChangeApplyVO = new EstimateChangeApplyVO();
        estimateChangeApplyVO.setAuditOpinion(lastApplyInfo.getAuditOpinion());
        estimateChangeApplyVO.setAuditRemark(lastApplyInfo.getAuditRemark());
        estimateChangeApplyVO.setApplyUm(lastApplyInfo.getApplyUm());
        estimateChangeApplyVO.setAuditDate(lastApplyInfo.getAuditDate());
        estimateChangeApplyVO.setAuditUm(lastApplyInfo.getAuditUm());
        estimateChangeApplyVO.setApplyorName(lastApplyInfo.getApplyorName());
        estimateChangeApplyVO.setAuditorName(lastApplyInfo.getAuditorName());
        return estimateChangeApplyVO;
    }

    private String getDepartmentName(String reportNo) {
        return ahcsPolicyInfoMapper.getDepartmentNameByReportNo(reportNo);
    }
}
