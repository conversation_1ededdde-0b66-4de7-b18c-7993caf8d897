package com.paic.ncbs.claim.model.vo.taskdeal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("工作台查询条件")
public class WorkBenchTaskQueryVO extends EntityDTO {

    private static final long serialVersionUID = 8113155016804247983L;

    public static final String INCLUDE_SUBORDINATES_Y = "Y";

    public static final String INCLUDE_SUBORDINATES_N = "N";

    public static final String MY_CASE_Y = "Y";

    @ApiModelProperty("报案号、被保险人名称、保单号")
    private String businessNo;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔案号")
    private String caseNo;

    @ApiModelProperty("案件所属机构编码")
    private String departmentCode;

    @ApiModelProperty("是否包含下级机构，Y/N")
    private String isIncludeSubordinates;

    @ApiModelProperty("包含下级机构,机构编码右侧去0 like")
    private List<String> departmentCodes;

    @ApiModelProperty("是否本人案件，Y/N")
    private String isMyCase;

    @ApiModelProperty("本人账号")
    private String userCode;

    /**
     * 核赔、立案审批、拒赔审批、零注审批、提调审批、调查审批、
     * 支付修改审批、报案跟踪、收单、理算、调查、沟通、支付修改。
     */
    @ApiModelProperty("任务分类")
    private String taskDefinitionBpmKey;

    @ApiModelProperty("非本人案件时候根据用户权限查询可查看任务分类")
    private List<String> taskDefinitionBpmKeys;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("被保险人")
    private String insuredName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("派工时间-开始的时间")
    private Date assignStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("派工时间-当结束的时间")
    private Date assignEndTime;

    @ApiModelProperty("审核等级")
    private Integer auditGrade;

    @ApiModelProperty("方案名称")
    private String packageName;

    @ApiModelProperty("产品代码")
    private String productCode;

    @ApiModelProperty("快赔")
    private String isQuickPay;

}
