package com.paic.ncbs.claim.controller.settle;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.SettleHelper;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayBaseInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.BatchPayAmountVo;
import com.paic.ncbs.claim.model.vo.settle.PolicyPayInfoVO;
import com.paic.ncbs.claim.model.vo.settle.RiskWarningVO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;
import com.paic.ncbs.claim.model.vo.settle.SttleBatchInfoVO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.vo.settle.*;
import com.paic.ncbs.claim.service.settle.AutoSettleService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleBatchService;
import com.paic.ncbs.claim.service.settle.SettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.ClaimSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import jodd.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/ahcs/do/app/settleAction")
@Api(tags = {"理算"})
@Slf4j
public class SettleController extends BaseController {

    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private SettleBatchService settleBatchService;
    @Autowired
    private SettleService settleService;
    @Autowired
    private AutoSettleService autoSettleService;
    @Autowired
    private ClaimSettleService claimSettleService;




    @ApiOperation(value = "初始化赔付信息")
    @GetMapping(value = "/initPolicys/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<Object> initPolicy(@PathVariable("reportNo") String reportNo,
                                             @PathVariable("caseTimes") Integer caseTimes) {
        try{
//            policyPayService.initPolicyPayInfo(reportNo, caseTimes);
            claimSettleService.settleInit(reportNo, caseTimes);
            return ResponseResult.success();
        } catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @GetMapping(value = {"/getPolicyPay/{reportNo}/{caseTimes}", "/getPolicyPay/{reportNo}/{caseTimes}/{scene}"})
    @ApiOperation(value = "查询案件理算信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class),
            @ApiImplicitParam(name = "scene", value = "查询场景", dataType = "String",dataTypeClass=String.class)
    })
    public ResponseResult<Object> getPolicyPayInfo(@PathVariable("reportNo") String reportNo,
                                                   @PathVariable("caseTimes") Integer caseTimes,
                                                   @PathVariable(value = "scene", required = false) String scene) {
        try {

            log.info("scenescenescenescenescene：{}", scene);
            PolicyPayInfoVO infoVO = policyPayService.getPolicyPayInfo(reportNo, caseTimes, scene);
            return ResponseResult.success(infoVO);
        } catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    @ApiOperation("获取自动理算金额")
    @PostMapping(value = "/getAutoSettleAmount")
    public ResponseResult<Object> getAutoSettleAmount(@RequestBody DutyPayDTO dutyPayDTO) {
        try{
//            Map<String, BigDecimal> autoSettleMap = dutyPayDTO.getDutyDetailPayArr().stream().collect(Collectors.toMap(DutyDetailPayDTO::getIdAhcsDutyDetailPay, DutyDetailPayDTO::getAutoSettleAmount));
            DutyDetailPayDTO dutyDetailPayDTO = dutyPayDTO.getDutyDetailPayArr().get(0);
            if(Objects.nonNull(dutyDetailPayDTO.getSettleAmount())){
                dutyPayDTO.setDutyDetailPayArr(settleService.getAutoSettleAmount(dutyPayDTO));
                dutyPayDTO.setSettleAmount(dutyPayDTO.getDutyDetailPayArr().stream().map(DutyDetailPayDTO::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                autoSettleService.setSettleReason(dutyPayDTO);
            }else {
                SettleSelectVO settleSelectVO = new SettleSelectVO();
                settleSelectVO.setReportNo(dutyPayDTO.getReportNo());
                settleSelectVO.setCaseTimes(dutyPayDTO.getCaseTimes());
                settleSelectVO.setPlanCode(dutyDetailPayDTO.getPlanCode());
                settleSelectVO.setDutyCode(dutyDetailPayDTO.getDutyCode());
                settleSelectVO.setDutyDetailCode(dutyDetailPayDTO.getDutyDetailCode());
                ClaimCaseDTO claimCaseDTO = claimSettleService.settleSelect(settleSelectVO);
                DutyPayDTO dutyPayResult = claimCaseDTO.getPolicyPayDTOList().get(0).getPlanPayArr().get(0).getDutyPayArr().get(0);
                List<DutyDetailPayDTO> dutyDetailPayArr = dutyPayResult.getDutyDetailPayArr();
                dutyPayDTO.setSettleAmount(dutyPayDTO.getDutyDetailPayArr().stream().map(DutyDetailPayDTO::getAutoSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                dutyPayDTO.setSettleReason(dutyPayResult.getSettleReason());
                DutyDetailPayDTO detailPayDTO = dutyDetailPayArr.get(0);
                List<BIllSettleResultDTO> billSettleResultDTOList = detailPayDTO.getBillSettleResultDTOList();
                log.info("单责任理算发票结果：{}", JsonUtils.toJsonString(billSettleResultDTOList));
                if(CollectionUtils.isNotEmpty(billSettleResultDTOList)){
                    List<ClmsDutyDetailBillSettleDTO> detailBillSettleList = new ArrayList<>();
                    BigDecimal remitAmount=BigDecimal.ZERO;
                    BigDecimal reasonableAmount=BigDecimal.ZERO;
//                    int no = 1;
                    for (BIllSettleResultDTO bIllSettleResultDTO : billSettleResultDTOList) {
                        ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO = new ClmsDutyDetailBillSettleDTO();
                        BeanUtils.copyProperties(bIllSettleResultDTO, clmsDutyDetailBillSettleDTO);
                        if (SettleConst.THERAPY_TYPE_OUTPATIENT.equals(bIllSettleResultDTO.getTherapyType())) {
                            clmsDutyDetailBillSettleDTO.setTherapyType("2");
                        } else if (SettleConst.THERAPY_TYPE_INPATIENT.equals(bIllSettleResultDTO.getTherapyType())) {
                            clmsDutyDetailBillSettleDTO.setTherapyType("1");
                        }
                        clmsDutyDetailBillSettleDTO.setCanModifyFlag("Y");
//                        clmsDutyDetailBillSettleDTO.setId(String.valueOf(no));
//                        no++;
                        detailBillSettleList.add(clmsDutyDetailBillSettleDTO);
                        if (Objects.equals("0", bIllSettleResultDTO.getSettleType())) {
                            reasonableAmount = reasonableAmount.add(Objects.isNull(bIllSettleResultDTO.getReasonableAmount()) ? BigDecimal.ZERO : bIllSettleResultDTO.getReasonableAmount());
                            remitAmount = remitAmount.add(Objects.isNull(bIllSettleResultDTO.getRemitAmount()) ? BigDecimal.ZERO : bIllSettleResultDTO.getRemitAmount());
                        }
                    }
                    detailPayDTO.setReasonableAmount(reasonableAmount);
                    detailPayDTO.setRemitAmount(remitAmount);
                    detailPayDTO.setDetailBillSettleList(detailBillSettleList);
                    log.info("单责任理算发票copy结果：{}", JsonUtils.toJsonString(detailBillSettleList));
                }
                dutyPayDTO.setDutyDetailPayArr(dutyDetailPayArr);
            }


//            if(!dutyPayDTO.getDutyDetailPayArr().isEmpty()) {
//                dutyPayDTO.getDutyDetailPayArr().forEach(dto -> {
//                    if(!dto.getIsDisplay()) {
//                        dto.setAutoSettleAmount(autoSettleMap.get(dto.getIdAhcsDutyDetailPay()));
//                    }
//                });
//            }
            return ResponseResult.success(dutyPayDTO);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }

    }

    @ApiOperation(value = "查询核赔批单-赔付金额信息")
    @GetMapping(value = "/getSettleAmountsSum/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<SttleBatchInfoVO> getSettleAmountsSum(@PathVariable("reportNo") String reportNo,
                                                                @PathVariable("caseTimes") Integer caseTimes) {
        SttleBatchInfoVO vo = settleBatchService.getSettleAmountsSum(reportNo, caseTimes);
        return ResponseResult.success(vo);
    }

    @ApiOperation(value = "查询核赔批单-获取案件的批单号,保单名称,赔付金额")
    @GetMapping(value = "/getPolicyPayBaseInfo/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<List<PolicyPayBaseInfoDTO>> getPolicyPayBaseInfo(@PathVariable("reportNo") String reportNo,
                                                                           @PathVariable("caseTimes") Integer caseTimes) {
        List<PolicyPayBaseInfoDTO> policyPayBaseInfos = settleService.getPolicyPayBaseInfo(reportNo, caseTimes);
        SettleHelper.setPolicyDisabledTag(policyPayBaseInfos);
        return ResponseResult.success(policyPayBaseInfos);
    }


    @ApiOperation(value = "获取理算批单金额")
    @PostMapping(value = "/getBatchPayAmount")
    public ResponseResult<BatchPayAmountVo> getBatchPayAmount(@RequestBody SettlesFormVO formVO) {
        BatchPayAmountVo vo = settleService.getBatchPayAmount(formVO);
        LogUtil.audit("#理算·计算核赔批单金额#计算成功# ");
        return ResponseResult.success(vo);
    }

    @ApiOperation(value = "暂存理算赔付信息")
    @PostMapping(value = "/savePolicyPay")
    public ResponseResult<Object> savePolicyPay(@RequestBody SettlesFormVO settlesFormDTO) {
        LogUtil.audit("#理算·开始暂存赔付信息##报案号{}，赔付次数{}", settlesFormDTO.getReportNo(), settlesFormDTO.getCaseTimes());
        policyPayService.addSettles(settlesFormDTO);
        LogUtil.audit("#理算·暂存赔付信息成功##报案号{}，赔付次数{}", settlesFormDTO.getReportNo(), settlesFormDTO.getCaseTimes());
        return ResponseResult.success();
    }

    @ApiOperation(value = "理算发送")
    @PostMapping(value = "/addPolicyPay")
    public ResponseResult<Object> addPolicyPay(@RequestBody SettlesFormVO settlesFormDTO) throws GlobalBusinessException {
        LogUtil.audit("理算发送报案号{},入参{}",settlesFormDTO.getReportNo(), JSON.toJSONString(settlesFormDTO));
        try{
            settleService.settle(settlesFormDTO);
            return ResponseResult.success();
        } catch (GlobalBusinessException ge) {
            return ResponseResult.fail(ge.getCode(),ge.getMessage());
        } catch (Exception e){
            LogUtil.error("理算发送异常:",e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),e.getMessage());
        }
    }

    @ApiOperation(value = "查询核责风险信息")
    @GetMapping(value = "/getRiskWarning")
    public ResponseResult<Object> getRiskWarning(@RequestParam("reportNo") String reportNo,
                                               @RequestParam("caseTimes") Integer caseTimes) {
        RiskWarningVO riskWarningVO = settleService.getRiskWarning(reportNo, caseTimes);
        return ResponseResult.success(riskWarningVO);
    }

    @ApiOperation(value = "获取短信模板")
    @GetMapping(value = "/getSmsTemplate")
    public ResponseResult<ClmsSmsTemplateDTO> getSmsTemplate(@RequestParam("templateClass") String templateClass) {
        return ResponseResult.success(settleService.getSmsTemplate(templateClass));
    }

    @ApiOperation(value = "修改短信模板")
    @GetMapping(value = "/modifySmsTemplate")
    public ResponseResult<Object> modifySmsTemplate(@RequestParam("templateDesc") String templateDesc,
                                                        @RequestParam("templateClass") String templateClass) {
        settleService.modifySmsTemplate(templateDesc,templateClass);
        return ResponseResult.success();

    }
}
 