package com.paic.ncbs.claim.service.common.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimWorkFlowService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ClaimWorkFlowServiceImpl implements ClaimWorkFlowService {
    @Autowired
    private BpmService bpmService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    /**
     * 工作流处理
     * @param reportNo
     * @param caseTimes
     */
    @Override
    @Transactional
    public void dealWorkFlowData(String reportNo, int caseTimes) {
        //完成报案跟踪工作流 原先的事件改成直接调用创建任务和完成任务
        bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_REPORT_TRACK);
        bpmService.startProcess_oc(reportNo, caseTimes, BpmConstants.OC_CHECK_DUTY);
        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_ACCEPT.getCode());
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
        if (wholeCaseBaseDTO.getDocumentFullDate() == null){
            wholeCaseBaseDTO.setDocumentFullDate(new Date());
        }
        wholeCaseBaseService.modifyWholeCaseBase(wholeCaseBaseDTO);

    }
}
