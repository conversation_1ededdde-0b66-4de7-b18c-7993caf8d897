package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAdditionalDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateEvaluateDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAdditionalVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateEvaluateVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;


@MapperScan
public interface InvestigateMapper extends BaseDao<InvestigateDTO> {


    int addInvestigate(InvestigateDTO investigate);


    int modifyInvestigate(InvestigateDTO investigate);


    InvestigateVO getInvestigateById(@Param("idAhcsInvestigate") String idAhcsInvestigate);

	InvestigateDTO getNoCommitData(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	InvestigateDTO getInvestigateByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	List<InvestigateVO> getHistoryInvestigate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	InvestigateVO getFirstInvestigate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	List<InvestigateVO> getHistoryOutInvestigate(InvestigateDTO investigateDTO);

	List<AccidentSceneDto> getAccidentSceneData(@Param("collectionCode") String collectionCode);

	String getUpperValueName(@Param("valueCode") String valueCode);

	String   getSelectItemName(@Param("collectionCode") List<String> collectionCode);


	List<InvestigateDTO> countNoCommit(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    Integer hasSameInvestigate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	Integer getInvestigateState(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	Integer hasInvestigateRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	String getInvestigateStartDate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	String getInvestigationStartDate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	List<InvestigateVO> getInvestigateRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	InvestigateDTO getNoFinishInvestigateRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	String getNoFinishIdInvestigate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	int getAdditionalCountByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	int addInvestigateAdditional(InvestigateAdditionalDTO additional);


	List<InvestigateAdditionalVO> getAdditionalByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	String getLatestInvestigateId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	String getLatestInvestigateId2(@Param("reportNo") String reportNo);


	List<String> getIdRmRunBatchRecords(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("initiateInvestigateNode") String initiateInvestigateNode);


	InvestigateEvaluateVO getInvestigateEvaluateByIdAhcsInvestigate(@Param("idAhcsInvestigate") String idAhcsInvestigate);
	

	InvestigateEvaluateVO getInvestigateEvaluateVersionByIdAhcsInvestigate(@Param("idAhcsInvestigate") String idAhcsInvestigate);
	
	

	int addInvestigateEvaluate(InvestigateEvaluateDTO investigateEvaluate);
	

	int modifyInvestigateEvaluate(InvestigateEvaluateDTO investigateEvaluate);


	InvestigateDTO getNoFinishEvaluateTotalByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	List<InvestigateVO> getInvestigateByTotalInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	String getEvaluateIndexByTotalId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	int getEvaluateCountByTotalId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	int getSerialNumberByInvestigateId(InvestigateDTO dto);

	int getInitModeByInvestigateId(InvestigateDTO dto);


	int getCountInvestigateForRisk(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("list") List<String> nodes);
	

	int getCountInvestigateByreportNo(String reportNO);


	List<String> listAllInitiatorUm();



	String getIsOldInvestigateByreportNo(String reportNO);

	void transformForInvestigate(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	void transformForAdditional(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	void transformForEvaluate(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	void transformForTask(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	void transformForInfo(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	void transformForPool(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	void transformForfile(@Param("oldReportNo") String oldReportNo, @Param("newReportNo") String newReportNo);
	
	void modifyInvestigateForFee(@Param("idAhcsInvestigate") String idAhcsInvestigate, @Param("isHasAdjustingFee") String isHasAdjustingFee,
								 @Param("commonEstimateFee") BigDecimal commonEstimateFee);
	void modifyInvestigateForOption(@Param("idAhcsInvestigate") String idAhcsInvestigate, @Param("feeAuditOption") String feeAuditOption);

	void deleteInvestigateForEhis(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	void synchronizeDepartment(@Param("investigateDTOList") List<InvestigateDTO> investigateDTOList);

    Integer getIvvesigateCount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);
    
	void deleteInvestigateNoOperate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<InvestigateDTO> getInvestigateRecordByPolicyClient(@Param("reportNo") String reportNo,
                                                            @Param("caseTimes") Integer caseTimes,
                                                            @Param("investigateStatusList") List<Integer> investigateStatusList);

    List<InvestigateDTO> getAbnormalInvestigateByClientNo(@Param("clientNo") String clientNo);

	/**
	 * 查询下拉数据
	 * @return
	 */
	List<CommonParameterTinyDTO>   getParamMappingDto(CommonParameterTinyDTO commonParameterTinyDTO);
}