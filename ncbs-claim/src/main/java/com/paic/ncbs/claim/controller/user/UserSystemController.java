package com.paic.ncbs.claim.controller.user;


import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.um.constant.RedisKeys;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;
import com.paic.ncbs.um.model.dto.UserComMenuInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import com.paic.ncbs.um.service.UserCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Api(tags = "用户中心")
@RequestMapping(value = "web/user")
@RestController
@Slf4j
public class UserSystemController {

    private static final int COOKIE_TOKEN_EXPIRE_TIME = 60 * 60 * 24;

    @Autowired
    private CacheService cacheService ;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    private final static long TIMEOUT = 2 * 24;


    @ApiOperation("获取权限机构")
    @PostMapping(value = "/queryUserSystemComList")
    public ResponseResult<List<SystemComInfoDTO>>  queryUserSystemComList(HttpServletRequest httpRequest,@RequestParam String flag ) throws NcbsException {
        String userCode = httpRequest.getHeader("X-WESURE-ENAME");
        log.info("跳转理赔选择权限机构：" + userCode + "---" + flag);
        List<SystemComInfoDTO> systemComInfoDTOS = cacheService.queryUserSystemComList(userCode);
        return  ResponseResult.success(systemComInfoDTOS);
    }

    @ApiOperation("获取登录用户信息")
    @GetMapping(value = "/queryUserSystemInfo")
    public ResponseResult<UserInfoDTO>  queryUserSystemInfo() throws NcbsException {
        UserInfoDTO userInfoDTO = cacheService.queryUserInfo(WebServletContext.getUserId());
        userInfoDTO.setMobile(null);
        userInfoDTO.setPhone(null);
        return  ResponseResult.success(userInfoDTO);
    }


    @ApiOperation("获取菜单列表")
    @PostMapping(value = "/queryUserComMenuList")
    public ResponseResult<List<UserComMenuInfoDTO>>  queryUserComMenuList(@RequestParam String comCode, @RequestParam String comCodeFlag )   {
        List<UserComMenuInfoDTO> userComMenuInfoDTOS = new ArrayList<>();
        return  ResponseResult.success(userComMenuInfoDTOS);
    }


    @GetMapping(value = "/getUserInfo")
    public ResponseResult<UserInfoDTO> getUserInfo(@RequestParam String userCode)  throws NcbsException {
        return ResponseResult.success(cacheService.queryUserInfo(userCode));
    }

    @GetMapping(value = "/getUserInfoNoCache")
    public ResponseResult<UserInfoDTO> getUserInfoNoCache(@RequestParam String userCode)  throws NcbsException {
        return ResponseResult.success(userCommonService.queryUserInfo(userCode));
    }

    @GetMapping(value = "/clearCache")
    public ResponseResult<UserInfoDTO> clearCache(@RequestParam String token)  throws NcbsException {
        cacheService.clearUserCache(token);
        return ResponseResult.success(cacheService.getUserInfoFromCacheByToken(token));
    }

    @GetMapping(value = "/updateCache")
    public ResponseResult<UserInfoDTO> updateCache(@RequestParam String userCode,@RequestParam String mobile,@RequestParam String email)  throws NcbsException {
        UserInfoDTO  userInfoDTO = userCommonService.queryUserInfo(userCode);
        if(!ObjectUtils.isEmpty(userInfoDTO)) {
            userInfoDTO.setMobile(mobile);
            userInfoDTO.setEmail(email);
            redisTemplate.opsForValue().set(RedisKeys.USER_INFO_KEY+userCode, JSON.toJSONString(userInfoDTO));
            redisTemplate.expire(RedisKeys.USER_INFO_KEY+userCode, TIMEOUT , TimeUnit.HOURS);
        }
        return ResponseResult.success(cacheService.queryUserInfo(userCode));
    }

    /**
     * 塞 cookie 的 ClaimToken
     */
    @GetMapping(value = "/cookie/setClaimTokenCookie")
    public ResponseResult setClaimTokenCookie(@RequestParam String token,  HttpServletResponse response) {
//        CookieUtil.setClaimTokenCookie(response, token, COOKIE_TOKEN_EXPIRE_TIME);
        return ResponseResult.success();
    }

    /**
     * 取 cookie 的 ClaimToken
     */
    @GetMapping(value = "/cookie/getClaimTokenCookie")
    public ResponseResult<String> getClaimTokenCookie(HttpServletRequest request) {
        String userCode = request.getHeader("X-WESURE-ENAME");
        return ResponseResult.success(userCode);
    }

}
