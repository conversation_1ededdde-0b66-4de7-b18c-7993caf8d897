package com.paic.ncbs.claim.service.rule;

import com.paic.ncbs.claim.model.dto.autoclaimsettle.ClaimRuleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;

/**
 * @description 自动规则服务
 * @date 2024/03/28
 */
public interface AutoRuleService {

    /**
     * 执行核责规则服务
     * @param reportNo
     * @param caseTimes
     * @param copyPolicyPays
     * @return
     */
    void execDutyConfirmRule(String reportNo, Integer caseTimes, List<PolicyPayDTO> copyPolicyPays);

    /**
     * 执行理算规则服务
     * @param reportNo
     * @param caseTimes
     * @return
     */
    ClaimRuleResultDTO executeSettleRule(String reportNo, Integer caseTimes);

    /**
     *  执行核赔规则服务
     * @param reportNo
     * @param caseTimes
     * @return
     */
    ClaimRuleResultDTO executeVerifySettleRule(String reportNo,Integer caseTimes);
}
