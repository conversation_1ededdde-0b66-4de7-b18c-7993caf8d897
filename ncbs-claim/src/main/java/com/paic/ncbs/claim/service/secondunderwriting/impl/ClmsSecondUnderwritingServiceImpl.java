package com.paic.ncbs.claim.service.secondunderwriting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.SendUwStatusEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterDTO;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonObjectMapper;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentTypeMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsPolicySurrenderInfoMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSecondUnderwritingMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.feign.mesh.UWRequest;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.vo.senconduw.*;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUwLetterService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSeconduwPolicyConclusionService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 理赔二核申请记录表(ClmsSecondUnderwritingEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-08 15:15:58
 */
@Slf4j
@Service("clmsSecondUnderwritingService")
public class ClmsSecondUnderwritingServiceImpl implements ClmsSecondUnderwritingService {

    @Resource
    private ClmsSecondUnderwritingMapper clmsSecondUnderwritingMapper;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private PersonObjectMapper personObjectMapper;

    @Autowired
    private OcasRequest ocasRequest;

    @Autowired
    private UWRequest uwRequest;

    @Autowired
    private ClmsSeconduwPolicyConclusionService clmsSeconduwPolicyConclusionService;

    @Autowired
    private ClmsSecondUwLetterService clmsSecondUwLetterService;
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Autowired
    private DocumentTypeMapper documentTypeMapper;
    @Autowired
    private ClaimCommonQueryFileInfoService claimCommonQueryFileInfoService;
    @Autowired
    private ClmsPolicySurrenderInfoMapper clmsPolicySurrenderInfoMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;

    @Autowired
    private IOperationRecordService operationRecordService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsSecondUnderwritingEntity queryById(String id) {
        return this.clmsSecondUnderwritingMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsSecondUnderwritingEntity> queryByReportNo(String reportNo, Integer caseTimes) {
        return this.clmsSecondUnderwritingMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsSecondUnderwritingEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSecondUnderwritingEntity insert(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity) {
        this.clmsSecondUnderwritingMapper.insert(clmsSecondUnderwritingEntity);
        return clmsSecondUnderwritingEntity;
    }

    @Override
    @Transactional
    public void sendTask(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity) {
        if (riskPropertyService.displayRiskProperty(clmsSecondUnderwritingEntity.getReportNo(), null)) {
            throw new GlobalBusinessException("责任险案件不支持核保");
        }

        //TPA问题件二核不传事故日期和节点代码
        if(Objects.isNull(clmsSecondUnderwritingEntity.getAccidentDate())) {
            clmsSecondUnderwritingEntity.setAccidentDate(taskInfoMapper.getAccidentDate(clmsSecondUnderwritingEntity.getReportNo()));
        }

        if(StringUtils.isEmpty(clmsSecondUnderwritingEntity.getTaskCode())) {
            clmsSecondUnderwritingEntity.setTaskCode(taskInfoMapper.getPendingTaskCount(clmsSecondUnderwritingEntity.getReportNo(), clmsSecondUnderwritingEntity.getCaseTimes()));
        }

        long a = System.currentTimeMillis();
        Integer caseTimes = clmsSecondUnderwritingEntity.getCaseTimes();
        String reportNo = clmsSecondUnderwritingEntity.getReportNo();
        /*  delete by zjtang 取消旧校验逻辑
        //校验当前案件是否存在除过主任务外其他未完成的流程,若存在，就不能发起核保
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setCaseTimes(caseTimes);
        taskInfoDTO.setReportNo(reportNo);
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getUndoTaskInfoList(taskInfoDTO);
        if(ListUtils.isNotEmpty(taskInfoVOList) && taskInfoVOList.size() > 0) {
            throw new GlobalBusinessException("当前案件存在其他未处理任务，不能发起核保！");
        }
         */
        //校验当前流程是否有冲突
        bpmService.processCheck(reportNo,BpmConstants.OC_CLAIM_SECOND_UNDERWRITING,BpmConstants.OPERATION_INITIATE);
        List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities = queryByReportNo(reportNo, caseTimes);
        // 拼接序号
        // 1、以发起全新核保任务的次数计算主流程，从1-1开始，每个主流程依次加1，后续为2-1、3-1等
        // 2、每个核保任务中同核保交互的子流程从主流程序号开始，每个子流程计数依次加0.1，例：主流程序号1-1，则后续为1-2等
        String serialNo = getSerialNo(clmsSecondUnderwritingEntities);
        String taskCode = clmsSecondUnderwritingEntity.getTaskCode();
        clmsSecondUnderwritingEntity.setSerialNo(serialNo);
        // 发起时间
        setDefaultValue(clmsSecondUnderwritingEntity);

        //2023-11-07start删除当前报案号赔付次数下的理赔解约保存的数据，理赔解约保存的数据是核保完成后的数据，如果保存了理赔解约信息后再发起二核删除已保存的数据
        clmsPolicySurrenderInfoMapper.deleteByReportNoAndCaseTimes(reportNo,caseTimes);
        //2023-11-07-end

        this.insert(clmsSecondUnderwritingEntity);
        bpmService.suspendOrActiveTask_oc_css(reportNo, caseTimes, taskCode, true);
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_CLAIM_SECOND_UNDERWRITING, "发起", null, WebServletContext.getUserIdForLog());
        bpmService.startProcess_oc(reportNo, caseTimes, BpmConstants.OC_CLAIM_SECOND_UNDERWRITING);
        caseProcessService.updateCaseProcess(reportNo, caseTimes, getCaseProcessStatus(taskCode));

        //  调用批改接口后再组装参数调核保接口
        Map<String,String> ocasParam = new HashMap<>();
        String clientNo = personObjectMapper.getClientNoByReportNo(reportNo);
        ocasParam.put("clientNo",clientNo);
        Date accidentDate = clmsSecondUnderwritingEntity.getAccidentDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String time = simpleDateFormat.format(accidentDate);
        ocasParam.put("time",time);
        ocasParam.put("queryModel","3");
        long b = System.currentTimeMillis();
        String ocasResult = ocasRequest.queryByClientNo(ocasParam);
        Map resultMap = JSON.parseObject(ocasResult, Map.class);
        LogUtil.info("调用批改-queryByClientNo-返回结果-resultMap-" + JSON.toJSONString(resultMap));
        if (MapUtils.isEmpty(resultMap) || !GlobalResultStatus.SUCCESS.getCode().equals(resultMap.get("returnCode"))) {
            throw new GlobalBusinessException("调用批改接口失败，返回结果：" + ocasResult);
        }
        List<EffectivePolicyResponse> contractDTOList = (List<EffectivePolicyResponse>) resultMap.get("data") ;
        if (CollectionUtils.isEmpty(contractDTOList)){
            throw new GlobalBusinessException("该用户不满足二核的条件：健康险、个单、有效保单！");
        }
        long c = System.currentTimeMillis();
        creatUwTask(clmsSecondUnderwritingEntity,contractDTOList);
        long d = System.currentTimeMillis();
        LogUtil.info("sendTask:理赔内部处理耗时={}，调用批改获取contractDTOList耗时={}，调用核保人核接口耗时={}",a - b, b - c, d - c);
    }

    /**
      *
      * @Description 看方法命名就能知道含义
      * <AUTHOR>
      * @Date 2023/9/21 10:10
      **/
    private void setDefaultValue(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity) {
        clmsSecondUnderwritingEntity.setUwStartDate(new Date());
        clmsSecondUnderwritingEntity.setCreatedBy(WebServletContext.getUserId());
        clmsSecondUnderwritingEntity.setUpdatedBy(WebServletContext.getUserId());
        clmsSecondUnderwritingEntity.setCreatedDate(new Date());
        clmsSecondUnderwritingEntity.setUnderwritingStatus("01");
        clmsSecondUnderwritingEntity.setUpdatedDate(new Date());
        clmsSecondUnderwritingEntity.setId(UuidUtil.getUUID());
    }

    /**
     *
     * @Description 生成序号 如果后期有核保退回 此处逻辑需要考虑重构
     * <AUTHOR>
     * @Date 2023/9/21 10:06
     **/
    private String getSerialNo(List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities) {
        String serialNo;
        if (CollectionUtils.isEmpty(clmsSecondUnderwritingEntities)){
            serialNo = "1-1";
        } else {
            ClmsSecondUnderwritingEntity last = clmsSecondUnderwritingEntities.get(clmsSecondUnderwritingEntities.size()-1);
            String lastSerialNo = last.getSerialNo();
            String[] split = lastSerialNo.split("-");
            if ("02".equals(last.getLettersCancelStatus()) && !"26".equals(last.getConclusion())){
                // 前一次函件未回销不可重复发起
                throw new GlobalBusinessException("前一次二核任务函件未回销，不可重复发起");
            }
            if ("01".equals(last.getUnderwritingStatus())){
                // 前一次任务未完成不可重复发起
                throw new GlobalBusinessException("前一次二核任务未核保通过，不可重复发起");
            } else if ("02".equals(last.getUnderwritingStatus())){
                // 前一次是核保通过的 序号主流程加1 否则主流程不变，子流程加1
                int first = Integer.parseInt(split[0]) + 1;
                serialNo = first + "-1";
            } else {
                int second = Integer.parseInt(split[2]) + 1;
                serialNo = split[0] + "-" + second;
            }
        }
        return serialNo;
    }

    /**
      *
      * @Description 调用核保
      * <AUTHOR>
      * @Date 2023/9/14 15:21
      **/
    private void creatUwTask(ClmsSecondUnderwritingEntity clmsSecondUnderwriting, List<EffectivePolicyResponse> contractDTOList) {
        UwTaskVO param = new UwTaskVO();
        param.setBusinessKey(clmsSecondUnderwriting.getReportNo()+"_"+clmsSecondUnderwriting.getCaseTimes());
//        ManualBomVO manualBom = new ManualBomVO();
//        manualBom.setContractDTOList(contractDTOList);
        param.setManualBom(contractDTOList);
        param.setOperatorId(WebServletContext.getUserId());
        param.setOperatorName(WebServletContext.getUserName());
        String diseaseInfo = clmsSecondUnderwriting.getDiseaseInfo();
        param.setDiagnosisDTOList(DiagnosisVO.getDiagnosisVO(diseaseInfo));
        String evidenceMaterialType = clmsSecondUnderwriting.getEvidenceMaterialType();
        param.setFileTypeList(UwTaskVO.getFileTypeListByMaterialType(evidenceMaterialType));
        String materialFileId = clmsSecondUnderwriting.getMaterialFileId();
        List<String> idList = Arrays.stream(materialFileId.split(",")).collect(Collectors.toList());
        List<UwTaskVO.UwDocumentInfo> documentInfoDTOList = new ArrayList<>();
        param.setDocumentIdList(idList);
        List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(clmsSecondUnderwriting.getReportNo());
        idList.forEach(id->{
            FileDocumentDTO documentDTO = list.stream().filter(file -> id.equals(file.getUrl())).findFirst().orElseThrow(() -> new GlobalBusinessException("单证查询异常！"));
            UwTaskVO.UwDocumentInfo uwDocumentInfo = UwTaskVO.UwDocumentInfo.builder()
                    .documentBigClass(documentDTO.getDocumentClass())
                    .documentBigClassName(documentTypeMapper.getBigNameBySmallCode(documentDTO.getDocumentClass()))
                    .documentFileType(documentDTO.getDocumentFormat())
                    .documentId(documentDTO.getUrl())
                    .documentName(documentDTO.getDocumentName())
                    .documentSmallType(documentDTO.getDocumentType())
                    .documentSmallTypeName(documentTypeMapper.getSmallNameBySmallCode(documentDTO.getDocumentType()))
                    .build();
            documentInfoDTOList.add(uwDocumentInfo);
        });
        param.setDocumentInfoDTOList(documentInfoDTOList);
        param.setTaskDesc(clmsSecondUnderwriting.getUnderwritingExplain());
        param.setWaitingPeriodDays(clmsSecondUnderwriting.getWaitingPeriodDays());
        LogUtil.info("调用核保-createTask-请求信息-requestMap-" + JSON.toJSONString(param));
        String uwsResult = uwRequest.createTask(param);
        Map resultMap = JSON.parseObject(uwsResult, Map.class);
        LogUtil.info("调用核保-createTask-返回结果-resultMap-" + JSON.toJSONString(resultMap));
        Map data = (Map) resultMap.get("data");
        // 判断送人核是否成功 如失败则抛异常回滚
        if (MapUtils.isNotEmpty(data) && "1".equals(MapUtils.getString(data,"resultCode"))){
            LogUtil.info("调用核保人核成功");
        } else {
            LogUtil.info("调用核保人核失败:" + MapUtils.getString(data,"resultCode"));
            throw new GlobalBusinessException("调用核保人核失败！");
        }
    }

    /**
     * 任务节点设置子节点的值
     * @param taskCode 任务节点
     */
    private String getCaseProcessStatus(String taskCode) {
        String caseProcessStatus;
        switch (taskCode) {
            case BpmConstants.OC_REPORT_TRACK:
                caseProcessStatus = CaseProcessStatus.REPORTTRACK_SECOND_UNDERWRITING.getCode();
                break;
            case BpmConstants.OC_CHECK_DUTY:
                caseProcessStatus = CaseProcessStatus.CHECKDUTY_SECOND_UNDERWRITING.getCode();
                break;
            default:
                caseProcessStatus = CaseProcessStatus.SETTLE_SECOND_UNDERWRITING.getCode();
                break;
        }
        return caseProcessStatus;
    }

    /**
     * 修改数据
     *
     * @param clmsSecondUnderwritingEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSecondUnderwritingEntity update(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity) {
        this.clmsSecondUnderwritingMapper.update(clmsSecondUnderwritingEntity);
        return this.queryById(clmsSecondUnderwritingEntity.getId());
    }

    @Override
    public List<ClmsSecondUnderwritingVO> getUWRecords(String reportNo, Integer caseTimes) {
        List<ClmsSecondUnderwritingVO> clmsSecondUnderwritingVOs = new ArrayList<>();
        List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities = queryByReportNo(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(clmsSecondUnderwritingEntities)){
            return clmsSecondUnderwritingVOs;
        }
        clmsSecondUnderwritingEntities.forEach(clmsSecondUnderwritingEntity -> {
            ClmsSecondUnderwritingVO clmsSecondUnderwritingVO = new ClmsSecondUnderwritingVO();
            BeanUtils.copyProperties(clmsSecondUnderwritingEntity,clmsSecondUnderwritingVO);
            clmsSecondUnderwritingVO.setUnderwritingStatus(SendUwStatusEnum.getName("26".equals(clmsSecondUnderwritingVO.getConclusion()) ? "03" : clmsSecondUnderwritingVO.getUnderwritingStatus()));
            clmsSecondUnderwritingVOs.add(clmsSecondUnderwritingVO);
        });
        return clmsSecondUnderwritingVOs;
    }

    @Override
    public Integer getUWCount(String reportNo, Integer caseTimes) {
        List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities = queryByReportNo(reportNo, caseTimes);
        int count = 0;
        // 拼接序号
        // 1、以发起全新核保任务的次数计算主流程，从1-1开始，每个主流程依次加1，后续为2-1、3-1等
        // 2、每个核保任务中同核保交互的子流程从主流程序号开始，每个子流程计数依次加0.1，例：主流程序号1-1，则后续为1-2等
        if (!CollectionUtils.isEmpty(clmsSecondUnderwritingEntities)) {
            ClmsSecondUnderwritingEntity last = clmsSecondUnderwritingEntities.get(clmsSecondUnderwritingEntities.size() - 1);
            String lastSerialNo = last.getSerialNo();
            String[] split = lastSerialNo.split("-");
            count = Integer.parseInt(split[0]);
        }
        return count;
    }

    /**
     * 根据报案号 赔付次数查询二核申请记录
     * 查询的是01-送核审批中
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public ClmsSecondUnderwritingEntity getUWRecord(String reportNo, Integer caseTimes) {
        return clmsSecondUnderwritingMapper.getUWRecord(reportNo,caseTimes);
    }

    /***
     * 根据id查询核保详细信息
     * @param id
     * @return
     */
    @Override
    public ClmsSecondUnderwritingVO getUwDetailInfo(String id) {
        log.info("getUwDetailInfo入参={}", id);
        //1:根据主键id查询理赔二核记录表信息
        ClmsSecondUnderwritingVO vo = new ClmsSecondUnderwritingVO();
        ClmsSecondUnderwritingEntity entity = this.queryById(id);
        if(ObjectUtil.isEmpty(entity)){
           return vo;
        }
        BeanUtils.copyProperties(entity,vo);
//        String materialFileId = vo.getMaterialFileId();
//        List<String> idList = Arrays.stream(materialFileId.split(",")).collect(Collectors.toList());
//        List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(entity.getReportNo());
//        StringJoiner stringJoiner = new StringJoiner(",");
//        idList.forEach(fileId->{
//            FileDocumentDTO documentDTO = list.stream().filter(file -> id.equals(file.getUrl())).findFirst().orElseThrow(() -> new GlobalBusinessException("单证查询异常！"));
//            stringJoiner.add(documentDTO.getDocumentName());
//        });
//        vo.setMaterialFileId(stringJoiner.toString());
        List<ClmsSecondUwLetterDTO> letterDTOList =  clmsSecondUwLetterService.getLists(id);
        if(CollectionUtils.isNotEmpty(letterDTOList)){
            FileInfoDTO letterFileDtoParams = new FileInfoDTO();
            letterFileDtoParams.setReportNo(entity.getReportNo());
            List<UwLetterVO> voList =  BeanUtil.copyToList(letterDTOList,UwLetterVO.class);
            for (UwLetterVO letter : voList) {
                //查询核保函件编码
                String letterCode = clmsSecondUwLetterService.getUwsLetterCode(vo.getManualInfoId());
                letter.setLetterCode(letterCode);
                letterFileDtoParams.setFileId(letter.getFileId());
                FileInfoDTO resultFileDto =  claimCommonQueryFileInfoService.getFileInfo(letterFileDtoParams);
                letter.setLetterFileInfoDto(resultFileDto);
                letter.setLetterUrl(resultFileDto.getFileUrl());
                //查询回销文件的预览url
                FileInfoDTO uploadFileDtoParams = new FileInfoDTO();
                uploadFileDtoParams.setFileId(letter.getUploadFileId());
                uploadFileDtoParams.setReportNo(entity.getReportNo());
                FileInfoDTO uploadFileDto =  claimCommonQueryFileInfoService.getFileInfo(uploadFileDtoParams);
                letter.setUploadFileUrl(uploadFileDto.getFileUrl());
                letter.setUwAdvice(entity.getUwAdvice());
            }
            vo.setUwLetterVOList(voList);
        }


        //封装诊断信息
        //诊断信息存储样式：1_诊断信息_2023-09-15#2_诊断信息_2023-09-18#3_诊断信息_2023-09-19
        if(StrUtil.isNotEmpty(entity.getDiseaseInfo())){
            List<DiagnosisVO> voList =   DiagnosisVO.getDiagnosisVO(entity.getDiseaseInfo());
            vo.setDiagnosisVOList(voList);
        }
        //查询保单层级核保结论信息
        List<ClmsSeconduwPolicyConclusionVO> policyConclusionVOList = clmsSeconduwPolicyConclusionService.getPolicyConclusionVOList(entity.getId());
        vo.setPolicyConclusionVOList(policyConclusionVOList);
        //文件反显：根据文件id查询上传的单证信息给前端查看上传的资料
        String materialFileId = vo.getMaterialFileId();
        List<String> idList = Arrays.stream(materialFileId.split(",")).collect(Collectors.toList());
        List<FileInfoDTO> infoDTOList =new ArrayList<>();
        FileInfoDTO fileDtoParams = new FileInfoDTO();
        fileDtoParams.setReportNo(entity.getReportNo());
        for (String fileid : idList) {
            fileDtoParams.setFileId(fileid);
            log.info("查询单证入参{},claimCommonQueryFileInfoService", JsonUtils.toJsonString(fileDtoParams));
            FileInfoDTO resultFileDto =  claimCommonQueryFileInfoService.getFileInfo(fileDtoParams);
            infoDTOList.add(resultFileDto);
        }
        vo.setFileInfoDTOs(infoDTOList);
        String policyNo = policyConclusionVOList.stream().map(ClmsSeconduwPolicyConclusionVO::getPolicyNo).collect(Collectors.joining(";"));
        if(ListUtils.isNotEmpty(vo.getUwLetterVOList())) {
            vo.getUwLetterVOList().forEach(i-> {
                i.setPolicyNo(policyNo);
            });
        }
        return vo;
    }

    /**
     * 根据保单号+被保险人查询命中的ICD集合 是否都已经发起过二核
     *
     * @param reportNo
     * @param caseTimes
     * @param hitICDList 命中的ICD集合
     * @return
     */
    @Override
    public boolean isAllSecondUW(String reportNo, Integer caseTimes, List<String> hitICDList) {
        if (CollectionUtils.isEmpty(hitICDList)) {
            return true;
        }
        List<String> statusList = Arrays.asList(SendUwStatusEnum.STATUS_02.getCode(),
                SendUwStatusEnum.STATUS_03.getCode());
        List<ClmsSecondUnderwritingEntity> secondUnderwritingEntityList =
                clmsSecondUnderwritingMapper.getSecondUWRecord(reportNo, caseTimes,statusList);
        if (CollectionUtils.isEmpty(secondUnderwritingEntityList)) {
            return false;
        }
        log.info("案件：{},健康告知命中ICD：{}", reportNo, JsonUtils.toJsonString(hitICDList));
        boolean result = true;
        for (String icdCode : hitICDList) {
            List<DiagnosisVO> diagnosisVOList =
                    secondUnderwritingEntityList.stream().map(item -> DiagnosisVO.getDiagnosisVO(item.getDiseaseInfo()))
                            .flatMap(List::stream).collect(Collectors.toList());
            result = diagnosisVOList.stream().anyMatch(item -> StringUtils.startsWith(item.getDiagnosisCode(),
                    icdCode));
            if (!result) {
                log.info("案件：{},健康告知命中ICD,还未二核的是：{}", reportNo, icdCode);
                break;
            }
        }
        return result;
    }

    /**
     * 根据保单号+被保险人查询出存在二核未完成的案件号集合。
     *
     * @param reportNo
     * @param caseTimes
     */
    @Override
    public List<String> getUnfinishedSecondUWOther(String reportNo, Integer caseTimes) {
        List<String> statusList = Arrays.asList(SendUwStatusEnum.STATUS_01.getCode());
        List<ClmsSecondUnderwritingEntity> secondUnderwritingEntityList =
                clmsSecondUnderwritingMapper.getSecondUWRecord(reportNo, caseTimes, statusList);
        List<String> reportNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(secondUnderwritingEntityList)) {
            reportNoList = secondUnderwritingEntityList.stream()
                    .map(ClmsSecondUnderwritingEntity::getReportNo)
                    .distinct().collect(Collectors.toList());
        }

        return reportNoList;
    }

    /**
     * 根据被保险人客户号查询所有存在二核任务结果为拒保的案件号集合。
     *
     * @param reportNo
     */
    @Override
    public List<String> getRejectSecondUW(String reportNo) {
        ReportCustomerInfoEntity reportCustomerInfoEntity = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
        String clientNo = reportCustomerInfoEntity.getClientNo();
        List<String> reportNoList = new ArrayList<>();
        if(StringUtils.isNotBlank(clientNo)){
            // conclusion 属性值等于 1标体:，2拒保、3除外
            List<ClmsSecondUnderwritingEntity> secondUnderwritingEntityList =
                    clmsSecondUnderwritingMapper.getRejectSecondUWByClientNo(clientNo);

            if (CollectionUtils.isNotEmpty(secondUnderwritingEntityList)) {
                reportNoList = secondUnderwritingEntityList.stream()
                        .map(ClmsSecondUnderwritingEntity::getReportNo)
                        .distinct().collect(Collectors.toList());
            }
        }
        return reportNoList;
    }

    @Override
    public Integer getUnBackUWCount(String reportNo, Integer caseTimes) {
        List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities = queryByReportNo(reportNo, caseTimes);
        clmsSecondUnderwritingEntities = clmsSecondUnderwritingEntities.stream().filter(i-> !"26".equals(i.getConclusion())).collect(Collectors.toList());
        int count = 0;
        // 拼接序号
        // 1、以发起全新核保任务的次数计算主流程，从1-1开始，每个主流程依次加1，后续为2-1、3-1等
        // 2、每个核保任务中同核保交互的子流程从主流程序号开始，每个子流程计数依次加0.1，例：主流程序号1-1，则后续为1-2等
        if (!CollectionUtils.isEmpty(clmsSecondUnderwritingEntities)) {
            ClmsSecondUnderwritingEntity last = clmsSecondUnderwritingEntities.get(clmsSecondUnderwritingEntities.size() - 1);
            String lastSerialNo = last.getSerialNo();
            String[] split = lastSerialNo.split("-");
            count = Integer.parseInt(split[0]);
        }
        return count;
    }
}
