package com.paic.ncbs.claim.service.user;

import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.user.GradeVO;
import com.paic.ncbs.claim.model.vo.user.PermissionTypeVO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import com.paic.ncbs.claim.model.vo.user.PermissionVO;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PermissionService {

    void addPermissionList(PermissionVO permissionVO);

    PermissionVO getPermissionList(String typeCode,String deptCode);

    /**
     * 根据金额获取案件等级(任务等级task_grade)
     * @param typeCode
     * @param deptCode
     * @param amount
     * @return
     */
    Integer getPermissionGrade(String typeCode, String deptCode, BigDecimal amount);

    PermissionUserVO getPermissionUserInfo(String userId);

    List<PermissionTypeVO> getRoleTypeList(String userId,String comCode);

    void addPermissionUser(PermissionUserVO permissionUserVO);

    List<GradeVO> getUserGradeList(String typeCode);

    List<SystemComInfoDTO> getPermissionSystemCom(String userId);

    List<PermissionUserDTO> getPermissionUserList(PermissionUserVO permissionUserVO);

    void updatePermissionUserList(List<PermissionUserDTO> permissionUserList);

    void removePermissionUser(String idClmsPermissionUser);


    /**
     * 根据等级获取用户
     * @param typeCode
     * @param grade
     * @return
     */
    List<String> getUserList(String typeCode,String comCode,Integer grade);

    /**
     * 获取用户的等级
     * @param comCode
     * @param typeCode
     * @param userId
     * @return
     */
    Integer getUserGrade(String typeCode,String comCode,String userId);

    /**
     * 根据权限机构和等级查找最近的一个等级，直至总部
     * @param typeCode
     * @param comCode
     * @return
     */
    PermissionUserDTO getLatestGrade(String typeCode,String comCode,Integer grade);

    List<PermissionTypeVO> getManageTypeList(String userId, String comCode);
    /**
     * 查询等级高的用户
     */
    List<PermissionUserDTO> getVerifyUserList(String reportNo,Integer caseTimes,String taskId);

    PermissionUserVO getUserPermission(PermissionUserDTO permissionUserDTO);

}
