package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.settle.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class CustomerServiceImpl extends BaseServiceImpl<ReportCustomerInfoEntity> implements CustomerService {

    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;


    @Override
    public BaseDao<ReportCustomerInfoEntity> getDao() {
        return reportCustomerInfoMapper;
    }

    @Override
    public ReportCustomerInfoEntity getReportCustomerInfoByReportNo(String reportNo) {
        ReportCustomerInfoEntity entity =reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
        String lastPolicyNo = null;
        if(Objects.nonNull(entity)){
//            String lastPolicyNo= ahcsPolicyInfoMapper.getLastPolicyNo(reportNo);
            AhcsPolicyInfoEntity policyEntity = new AhcsPolicyInfoEntity();
            policyEntity.setReportNo(reportNo);
            List<AhcsPolicyInfoEntity> policyInfoList = ahcsPolicyInfoMapper.getList(policyEntity);
            if(!policyInfoList.isEmpty()){
                AhcsPolicyInfoEntity policyInfo = policyInfoList.get(0);
                lastPolicyNo= policyInfo.getLastPolicyNo();
                entity.setIsTransferInsure("1".equals(policyInfo.getIsTransferInsure()) ? "是" : "否");
                entity.setRiskGroupType(policyInfo.getRiskGroupType());
            }

            // 上一年保单号
            boolean isShowLastPolicyNo = true;
            if("200".equals(entity.getClientCluster()) || "020".equals(entity.getClientCluster())){
                // 虚拟被保险人
                isShowLastPolicyNo = false;
            }else {
                // 抄单被保险人是新保 0-新保
                List<Integer> personNatures = ahcsInsuredPresonMapper.getAhcsInsuredPersionNatureByClientNoAndReportNo(entity.getClientNo(),reportNo);
                if(personNatures!=null) {
                    for(Integer personNature:personNatures) {
                        if(personNature != null && personNature.equals(0)) {
                            isShowLastPolicyNo = false;
                            break;
                        }
                    }
                }
            }
            if(isShowLastPolicyNo) {
                if(StringUtils.isNotEmpty(lastPolicyNo)){
                    entity.setIsRenew(Constants.YES_FLAG);
                    entity.setLastPolicyNo(lastPolicyNo);
                }else {
                    entity.setIsRenew(Constants.NOT_FLAG);
                }
            }

        }
        return entity;
    }

    @Override
    public List<ReportCustomerInfoEntity> getReportCustomerInfo(String clientCertificateNo, String reportNo) {
        return reportCustomerInfoMapper.getReportCustomerInfo(clientCertificateNo, reportNo);
    }

}
