package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleAccidentVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;

@MapperScan
public interface PersonAccidentMapper {

    public void savePersonAccident(PersonAccidentDTO personAccidentDTO);

    public void removePersonAccident(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);

    void updateEffective(PersonAccidentDTO personAccidentDTO);

    public PersonAccidentDTO getPersonAccident(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public PersonAccidentDTO getPersonAccidentByReportNo(@Param("reportNo") String reportNo, @Param("taskId") String taskId, @Param("caseTimes") Integer caseTimes);

    public SettleAccidentVO getSettleAccident(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public PersonAccidentDTO getPersonAccidentInfo(@Param("reportNo") String reportNo, @Param("caseTimes") String caseTimes, @Param("taskId") String taskId, @Param("status") String status);

    public PersonAccidentDTO getPersonAccidentDTO(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public List<PersonAccidentDTO> getReportPersonAccidentList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public List<PersonAccidentDTO> getPersonAccidentListByIdChannel(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public void addPersonAccidentList(@Param("personAccidentList") List<PersonAccidentDTO> personAccidentList, @Param("caseTimes") Integer caseTimes,
                                      @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public String getInjuryMechanismCode(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public String getAccidentTimeByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    /**
     * 获取事故时间
     * @param reportNo
     * @return
     */
    public List<PersonAccidentDTO> getPersonAccidentDate(CaseReopenCopyDTO dto);

}
