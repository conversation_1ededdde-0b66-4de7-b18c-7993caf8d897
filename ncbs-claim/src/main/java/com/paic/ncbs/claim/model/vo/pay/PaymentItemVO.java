package com.paic.ncbs.claim.model.vo.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class PaymentItemVO {
    private String idClmPaymentItem;
    private String policyNo;
    private String reportNo;
    private String caseNo;
    private String idClmBatch;
    private Integer caseTimes;
    private String claimType;
    private Integer subTimes;
    private String paymentType;
    private String idClmPaymentInfo;
    private String collectPaySign;
    private BigDecimal paymentAmount;
    private String paymentCurrencyCode;
    private String clientName;
    private String clientCertificateType;
    private String clientCertificateNo;
    private String clientBankCode;
    private String clientBankName;
    private String clientBankAccount;
    private String clientMobile;
    private String clientType;
    private String provinceName;
    private String cityName;
    private String regionCode;
    private String collectPayApproach;
    private String bankAccountAttribute;
    private String mergeSign;
    private String paymentItemStatus;
    private String remark;
    private String isCoinsure;
    private String collectPayNo;
    private String hisIdClmPaymentNotice;
    private BigDecimal exchangeRate;
    private BigDecimal convertAmount;
    private String extendInfo;
    private String bankDetail ;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date createDate ;
    private String paymentTypeName;
    private String organizeCode;
    private String collectPaySignName;
    // 支付日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date payDate;
    // 归档
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date archiveDate;
    // 支付退回日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date payBackDate ;

    private String clientRelation;

    private String bankDetailCode;

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * 领款方式为微信零钱的时候必传
     */
    private String openId;

    /**
     * 领款方式：1 微信零钱 2银行转账
     */
    private String payType;

    @ApiModelProperty("共保标志 0非共保1共保")
    private String coinsuranceMark;

    @ApiModelProperty("是否主承保 0否1是")
    private String acceptInsuranceFlag;

    @ApiModelProperty("共保公司编码")
    private String coinsuranceCompanyCode;

    @ApiModelProperty("共保公司名称")
    private String coinsuranceCompanyName;

    @ApiModelProperty("共保比例")
    private BigDecimal coinsuranceRatio;

    @ApiModelProperty("是否全额给付 0否1是")
    private String isFullPay;

    @ApiModelProperty("共保实付金额")
    private BigDecimal coinsuranceActualAmount;

    @ApiModelProperty("财务支付金额")
    private BigDecimal financePaymentAmount;

    @ApiModelProperty("支付凭证地址")
    private String paymentVoucherUrl;

    public String getBankDetailCode() {
        return bankDetailCode;
    }

    public void setBankDetailCode(String bankDetailCode) {
        this.bankDetailCode = bankDetailCode;
    }

    public String getClientRelation() {
        return clientRelation;
    }

    public void setClientRelation(String clientRelation) {
        this.clientRelation = clientRelation;
    }

    public Date getArchiveDate() {
        return archiveDate;
    }

    public void setArchiveDate(Date archiveDate) {
        this.archiveDate = archiveDate;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public Date getPayBackDate() {
        return payBackDate;
    }

    public void setPayBackDate(Date payBackDate) {
        this.payBackDate = payBackDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getBankDetail() {
        return bankDetail;
    }

    public void setBankDetail(String bankDetail) {
        this.bankDetail = bankDetail;
    }

    public String getIdClmPaymentItem() {
        return idClmPaymentItem;
    }

    public void setIdClmPaymentItem(String idClmPaymentItem) {
        this.idClmPaymentItem = idClmPaymentItem;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getIdClmBatch() {
        return idClmBatch;
    }

    public void setIdClmBatch(String idClmBatch) {
        this.idClmBatch = idClmBatch;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getClaimType() {
        return claimType;
    }

    public void setClaimType(String claimType) {
        this.claimType = claimType;
    }

    public Integer getSubTimes() {
        return subTimes;
    }

    public void setSubTimes(Integer subTimes) {
        this.subTimes = subTimes;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getIdClmPaymentInfo() {
        return idClmPaymentInfo;
    }

    public void setIdClmPaymentInfo(String idClmPaymentInfo) {
        this.idClmPaymentInfo = idClmPaymentInfo;
    }

    public String getCollectPaySign() {
        return collectPaySign;
    }

    public void setCollectPaySign(String collectPaySign) {
        this.collectPaySign = collectPaySign;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getPaymentCurrencyCode() {
        return paymentCurrencyCode;
    }

    public void setPaymentCurrencyCode(String paymentCurrencyCode) {
        this.paymentCurrencyCode = paymentCurrencyCode;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public String getClientCertificateNo() {
        return clientCertificateNo;
    }

    public void setClientCertificateNo(String clientCertificateNo) {
        this.clientCertificateNo = clientCertificateNo;
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public String getClientMobile() {
        return clientMobile;
    }

    public void setClientMobile(String clientMobile) {
        this.clientMobile = clientMobile;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getCollectPayApproach() {
        return collectPayApproach;
    }

    public void setCollectPayApproach(String collectPayApproach) {
        this.collectPayApproach = collectPayApproach;
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute;
    }

    public String getMergeSign() {
        return mergeSign;
    }

    public void setMergeSign(String mergeSign) {
        this.mergeSign = mergeSign;
    }

    public String getPaymentItemStatus() {
        return paymentItemStatus;
    }

    public void setPaymentItemStatus(String paymentItemStatus) {
        this.paymentItemStatus = paymentItemStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsCoinsure() {
        return isCoinsure;
    }

    public void setIsCoinsure(String isCoinsure) {
        this.isCoinsure = isCoinsure;
    }

    public String getCollectPayNo() {
        return collectPayNo;
    }

    public void setCollectPayNo(String collectPayNo) {
        this.collectPayNo = collectPayNo;
    }

    public String getHisIdClmPaymentNotice() {
        return hisIdClmPaymentNotice;
    }

    public void setHisIdClmPaymentNotice(String hisIdClmPaymentNotice) {
        this.hisIdClmPaymentNotice = hisIdClmPaymentNotice;
    }
    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getConvertAmount() {
        return convertAmount;
    }

    public void setConvertAmount(BigDecimal convertAmount) {
        this.convertAmount = convertAmount;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public String getCollectPaySignName() {
        return collectPaySignName;
    }

    public void setCollectPaySignName(String collectPaySignName) {
        this.collectPaySignName = collectPaySignName;
    }

    public String getCoinsuranceMark() {
        return coinsuranceMark;
    }

    public void setCoinsuranceMark(String coinsuranceMark) {
        this.coinsuranceMark = coinsuranceMark;
    }

    public String getAcceptInsuranceFlag() {
        return acceptInsuranceFlag;
    }

    public void setAcceptInsuranceFlag(String acceptInsuranceFlag) {
        this.acceptInsuranceFlag = acceptInsuranceFlag;
    }

    public String getCoinsuranceCompanyCode() {
        return coinsuranceCompanyCode;
    }

    public void setCoinsuranceCompanyCode(String coinsuranceCompanyCode) {
        this.coinsuranceCompanyCode = coinsuranceCompanyCode;
    }

    public String getCoinsuranceCompanyName() {
        return coinsuranceCompanyName;
    }

    public void setCoinsuranceCompanyName(String coinsuranceCompanyName) {
        this.coinsuranceCompanyName = coinsuranceCompanyName;
    }

    public BigDecimal getCoinsuranceRatio() {
        return coinsuranceRatio;
    }

    public void setCoinsuranceRatio(BigDecimal coinsuranceRatio) {
        this.coinsuranceRatio = coinsuranceRatio;
    }

    public String getIsFullPay() {
        return isFullPay;
    }

    public void setIsFullPay(String isFullPay) {
        this.isFullPay = isFullPay;
    }

    public BigDecimal getCoinsuranceActualAmount() {
        return coinsuranceActualAmount;
    }

    public void setCoinsuranceActualAmount(BigDecimal coinsuranceActualAmount) {
        this.coinsuranceActualAmount = coinsuranceActualAmount;
    }

    public BigDecimal getFinancePaymentAmount() {
        return financePaymentAmount;
    }

    public void setFinancePaymentAmount(BigDecimal financePaymentAmount) {
        this.financePaymentAmount = financePaymentAmount;
    }

    public String getPaymentVoucherUrl() {
        return paymentVoucherUrl;
    }

    public void setPaymentVoucherUrl(String paymentVoucherUrl) {
        this.paymentVoucherUrl = paymentVoucherUrl;
    }
}
