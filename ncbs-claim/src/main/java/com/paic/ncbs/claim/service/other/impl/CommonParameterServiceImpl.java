package com.paic.ncbs.claim.service.other.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.AccidentReasonConst;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.LossClassConstant;
import com.paic.ncbs.claim.common.enums.AccidentReasonTypeEnum;
import com.paic.ncbs.claim.common.enums.PropertyLossReasonEnum;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.*;
import com.paic.ncbs.claim.model.vo.duty.LossTypeVO;
import com.paic.ncbs.claim.model.vo.other.CountryVO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.other.CityDefineService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CommonParameterServiceImpl extends BaseServiceImpl<CommonParameterEntity> implements CommonParameterService {

    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @Override
    public BaseDao<CommonParameterEntity> getDao() {
        return commonParameterMapper;
    }

    @Autowired
    private CityDefineService cityDefineService;

    @Autowired
    private CommonParameterService commonService;

    @Autowired
    private OcasMapper ocasMapper;

    @Override
    public List<CommonParameterTinyDTO> getCommonParameterList(String[] collectionCodeList) {
        return commonParameterMapper.getCommonParameterList(collectionCodeList);
    }

    @Override
    public Map<String, List<CommonParameterTinyDTO>> getDataDictList(String[] collectionCodeList) {
        List<CommonParameterTinyDTO> list = commonParameterMapper.getCommonParameterList(collectionCodeList);
        if (list != null && list.size() > 0) {
            String collectionCode = list.get(0).getCollectionCode();

            Map<String, List<CommonParameterTinyDTO>> resultMap = new HashMap<String, List<CommonParameterTinyDTO>>();
            resultMap.put(collectionCode, new ArrayList<>());

            for (CommonParameterTinyDTO dto : list) {
                if (dto.getCollectionCode().equals(collectionCode)) {
                    resultMap.get(dto.getCollectionCode()).add(dto);
                } else {
                    collectionCode = dto.getCollectionCode();
                    resultMap.put(dto.getCollectionCode(), new ArrayList<>());
                    resultMap.get(dto.getCollectionCode()).add(dto);
                }
            }
            return resultMap;
        }
        return null;
    }

    private boolean isClaimMadeFromPlan(String productCode) {
        String isClaimMade = commonParameterMapper.getClaimMadeProductAmount(productCode);
        boolean isClaimMadeFromPlan = !BaseConstant.STRING_0.equals(isClaimMade);
        LogUtil.info("是否索赔发生制险种:{}", isClaimMadeFromPlan);
        return isClaimMadeFromPlan;
    }

    @Override
    public String getTotalSwitch(){
        return commonParameterMapper.getTotalSwitch();
    }

    @Override
    public List<String> getCommonParameterByCode(String collectionCode){
        return commonParameterMapper.getCommonParameterByCode(collectionCode);
    }

    @Override
    public String getValueChineseName(String valueCode,String collectionCode) throws GlobalBusinessException {
        return commonParameterMapper.getNameByCode(valueCode,collectionCode);
    }

    @Override
    public AdressSearchDto getDetailAdressFormCode(AdressSearchDto adressSearchDto) {
        if (adressSearchDto !=null){
            if (String.valueOf(adressSearchDto.getOverseasOccur()).equals("0") && !StringUtils.isEmptyStr(String.valueOf(adressSearchDto.getAccidentProvinceCode()) )
                    && !StringUtils.isEmptyStr(String.valueOf(adressSearchDto.getAccidentCityCode()))
                    && !StringUtils.isEmptyStr(String.valueOf(adressSearchDto.getAccidentCountyCode()))  ) {
                List<CommonParameterTinyDTO> pcs = commonService.getDataDictList(new String[]{"PC00"}).get("PC00");
                List<CityDefineDTO> citys = cityDefineService.getCityDefineDTOList(adressSearchDto.getAccidentProvinceCode(), "province");
                List<CityDefineDTO> countys = cityDefineService.getCityDefineDTOList(adressSearchDto.getAccidentCityCode(), "city");
                pcs.forEach(e -> {
                    if (e.getValueCode().equals(adressSearchDto.getAccidentProvinceCode())) {
                        adressSearchDto.setAccidentProvinceName(e.getValueChineseName());
                        if (adressSearchDto.getAccidentCityCode().equals(BaseConstant.OTHER_CITY_CODE)){
                            adressSearchDto.setAccidentCityName("其它");
                            adressSearchDto.setAccidentCountyName("其它");
                            return;
                        }
                        for (CityDefineDTO cityDefineDTO : citys) {
                            if (cityDefineDTO.getCityCode().equals(adressSearchDto.getAccidentCityCode())) {
                                adressSearchDto.setAccidentCityName(cityDefineDTO.getCityChineseName());
                            }
                        }
                        for (CityDefineDTO cityDefineDTO2 : countys) {
                            if (cityDefineDTO2.getCityCode().equals(adressSearchDto.getAccidentCountyCode())) {
                                adressSearchDto.setAccidentCountyName(cityDefineDTO2.getCityChineseName());
                            }
                        }
                    }
                });
            }else if (String.valueOf(adressSearchDto.getOverseasOccur()).equals("1")  && !StringUtils.isEmptyStr(String.valueOf(adressSearchDto.getAccidentAreaCode())) && !StringUtils.isEmptyStr(String.valueOf(adressSearchDto.getAccidentNationCode())) ) {
                List<CommonParameterTinyDTO> areaInfos= commonService.getDataDictList(new String[]{"AHCS_CONTINENT_CODE"}).get("AHCS_CONTINENT_CODE");
                List<CountryVO> countryByContinent = cityDefineService.getCountryByContinent(adressSearchDto.getAccidentAreaCode());
                areaInfos.forEach(e->{
                    if (e.getValueCode().equals(adressSearchDto.getAccidentAreaCode())){
                        adressSearchDto.setAccidentAreaName(e.getValueChineseName());
                    }
                });
                countryByContinent.forEach(x->{
                    if (x.getCountryCode().equals(adressSearchDto.getAccidentNationCode())){
                        adressSearchDto.setAccidentNationName(x.getCountryName());
                    }
                });
            } else if (String.valueOf(adressSearchDto.getOverseasOccur()).equals("0") && !StringUtils.isEmptyStr(String.valueOf(adressSearchDto.getAccidentProvinceCode()))) {
                List<CommonParameterTinyDTO> pcs = commonService.getDataDictList(new String[]{"PC00"}).get("PC00");
                pcs.forEach(e -> {
                    if (e.getValueCode().equals(adressSearchDto.getAccidentProvinceCode())) {
                        adressSearchDto.setAccidentProvinceName(e.getValueChineseName());
                        if (StringUtil.isNotBlank(adressSearchDto.getAccidentCityCode())) {
                            if (adressSearchDto.getAccidentCityCode().equals(BaseConstant.OTHER_CITY_CODE)){
                                adressSearchDto.setAccidentCityName("其它");
                                adressSearchDto.setAccidentCountyName("其它");
                                return;
                            }
                            List<CityDefineDTO> citys = cityDefineService.getCityDefineDTOList(adressSearchDto.getAccidentProvinceCode(), "province");
                            for (CityDefineDTO cityDefineDTO : citys) {
                                if (cityDefineDTO.getCityCode().equals(adressSearchDto.getAccidentCityCode())) {
                                    adressSearchDto.setAccidentCityName(cityDefineDTO.getCityChineseName());
                                }
                            }
                            if (StringUtil.isNotBlank(adressSearchDto.getAccidentCityCode())) {
                                List<CityDefineDTO> countys = cityDefineService.getCityDefineDTOList(adressSearchDto.getAccidentCityCode(), "city");
                                for (CityDefineDTO cityDefineDTO2 : countys) {
                                    if (cityDefineDTO2.getCityCode().equals(adressSearchDto.getAccidentCountyCode())) {
                                        adressSearchDto.setAccidentCountyName(cityDefineDTO2.getCityChineseName());
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }
        return  adressSearchDto ;
    }

    /**
     * 查询出险原因下拉 大类数据
     * @param
     * @return
     */
    @Override
    public List<AccidentReasonTypeInfoDTO> getAccidentReason() {
        List<String> paramsList= Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_REASON_TYPE.getCode(),AccidentReasonTypeEnum.ACCIDENT_000.getCode(),
                AccidentReasonTypeEnum.ACCIDENT_001.getCode(),AccidentReasonTypeEnum.ACCIDENT_002.getCode(),AccidentReasonTypeEnum.ACCIDENT_003.getCode()
        ,AccidentReasonTypeEnum.ACCIDENT_004.getCode());
        List<CommonParameterTinyDTO> list = commonParameterMapper.getAccidentReasonList(paramsList);

        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<AccidentReasonTypeInfoDTO>();
        }
        List<AccidentReasonTypeInfoDTO> returnList = new ArrayList<>();
        List<CommonParameterTinyDTO> typeList = list.stream().filter(dto->AccidentReasonTypeEnum.ACCIDENT_REASON_TYPE.getCode().equals(dto.getCollectionCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(typeList)){
            for (CommonParameterTinyDTO dto : typeList) {
                AccidentReasonTypeInfoDTO reasonTypeInfoDTO =new AccidentReasonTypeInfoDTO();
                reasonTypeInfoDTO.setAccidentReasonTypeCode(dto.getValueCode());
                reasonTypeInfoDTO.setAccidentReasonTypeName(dto.getValueChineseName());
                List<CommonParameterTinyDTO> detailList = list.stream().filter(comdto->reasonTypeInfoDTO.getAccidentReasonTypeCode().equals(comdto.getCollectionCode())).collect(Collectors.toList());
                reasonTypeInfoDTO.setAccidentReasonDTOList(dealList(detailList));
                returnList.add(reasonTypeInfoDTO);
            }
        }

        return returnList;
    }

    /**
     * 处理出险原因小类
     * @param detailList
     */
    private List<AccidentReasonDTO> dealList(List<CommonParameterTinyDTO> detailList) {
        List<AccidentReasonDTO> list=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(detailList)){
            for (CommonParameterTinyDTO dto : detailList) {
                AccidentReasonDTO reasonDTO = new AccidentReasonDTO();
                reasonDTO.setAccidentReasonCode(dto.getValueCode());
                reasonDTO.setAccidentReasonName(dto.getValueChineseName());
                list.add(reasonDTO);
            }
        }
        return list;
    }


    /**
     * 查询财产损失原因信息
     * @return
     */
    public List<PropertyLossReasonDTO> getPropertyLossReason(){
        List<String> paramsList= Arrays.asList(PropertyLossReasonEnum.PROPERTY_LOSS_REASON_01.getCode(),
                PropertyLossReasonEnum.PROPERTY_LOSS_REASON_02.getCode(),
                PropertyLossReasonEnum.PROPERTY_LOSS_REASON_99.getCode(),
                PropertyLossReasonEnum.PROPERTY_LOSS_REASON_TYPE.getCode());
        List<CommonParameterTinyDTO> list = commonParameterMapper.getPropertyLossReasonList(paramsList);

        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<PropertyLossReasonDTO>();
        }
        List<PropertyLossReasonDTO> returnList = new ArrayList<>();
        List<CommonParameterTinyDTO> typeList = list.stream().filter(dto->PropertyLossReasonEnum.PROPERTY_LOSS_REASON_TYPE.getCode().equals(dto.getCollectionCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(typeList)){
            for (CommonParameterTinyDTO dto : typeList) {
                PropertyLossReasonDTO propertyLossReasonDTO = new PropertyLossReasonDTO();
                propertyLossReasonDTO.setLossReasonTypeCode(dto.getValueCode());
                propertyLossReasonDTO.setLossReasonTypeName(dto.getValueChineseName());
                List<CommonParameterTinyDTO> detailList = list.stream().filter(comdto->propertyLossReasonDTO.getLossReasonTypeCode().equals(comdto.getCollectionCode())).collect(Collectors.toList());
                propertyLossReasonDTO.setLossDetailReasonDTOList(dealLossList(detailList));

                returnList.add(propertyLossReasonDTO);
            }
        }

        return returnList;


    }
    /**
     * 处理财产损失原因小类
     * @param detailList
     * @return
     */
    private List<PropertyLossDetailReasonDTO> dealLossList(List<CommonParameterTinyDTO> detailList) {
        List<PropertyLossDetailReasonDTO> list=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(detailList)){
            for (CommonParameterTinyDTO dto : detailList) {
                PropertyLossDetailReasonDTO propertyLossDetailReasonDTO = new PropertyLossDetailReasonDTO();
                propertyLossDetailReasonDTO.setLossReasonDetTypeCode(dto.getValueCode());
                propertyLossDetailReasonDTO.setLossReasonDetTypeName(dto.getValueChineseName());
                list.add(propertyLossDetailReasonDTO);
            }
        }
        return list;
    }

    /**
     * 查询救援类型
     * @return
     */
   public  List<SuccourTypeDTO> getSusccourTypeList(){
       List<CommonParameterTinyDTO> list = commonParameterMapper.getSuccourTypeList();
       if(CollectionUtil.isEmpty(list)){
           return new ArrayList<SuccourTypeDTO>();
       }
       List<SuccourTypeDTO> succourTypeDTOList=new ArrayList<>();
       for (CommonParameterTinyDTO dto : list) {
           SuccourTypeDTO succourTypeDTO=new SuccourTypeDTO();
           succourTypeDTO.setSuccourTypeCode(dto.getValueCode());
           succourTypeDTO.setSuccourTypeName(dto.getValueChineseName());
           succourTypeDTOList.add(succourTypeDTO);
       }

       return succourTypeDTOList;
   }

    @Override
    public List<AccidentReasonTypeInfoDTO> getAccidentReason(String certificateNo, String policyList, String insuredName) {

        List<String> planClassList = ocasMapper.getPlanClassByInsured(certificateNo, Arrays.asList(policyList.split(",")), insuredName);
        Set<String> accidentReasonSet = new HashSet<>();

        for (String planClass : planClassList) {
            List<String> reasons = AccidentReasonConst.PRODUCT_CLASS_ACCIDENT_REASON_MAP.get(planClass);
            if (reasons != null) {
                accidentReasonSet.addAll(reasons);
            }
        }
        List<AccidentReasonTypeInfoDTO> returnList = this.getAccidentReason();
        if (CollectionUtil.isNotEmpty(accidentReasonSet)) {
            returnList = returnList.stream().filter(dto -> accidentReasonSet.contains(dto.getAccidentReasonTypeCode())).collect(Collectors.toList());
        }
        return returnList;
    }

    /**
     * 查询重点项目
     * @return
     */
    public  List<KeyProjectsDTO> getKeyProjectsList(){
        List<CommonParameterTinyDTO> list = commonParameterMapper.getKeyProjectList();
        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<KeyProjectsDTO>();
        }
        List<KeyProjectsDTO> keyProjectsDTOS=new ArrayList<>();
        for (CommonParameterTinyDTO dto : list) {
            KeyProjectsDTO keyProjectsDTO=new KeyProjectsDTO();
            keyProjectsDTO.setKeyProjectsCode(dto.getValueCode());
            keyProjectsDTO.setKeyProjectsName(dto.getValueChineseName());
            keyProjectsDTO.setDepartmentCode(dto.getDepartmentCode());
            keyProjectsDTOS.add(keyProjectsDTO);
        }

        return keyProjectsDTOS;
    }
    @Override
    public List<LossTypeVO> getLossClass(String certificateNo, String policyList, String insuredName) {

        List<String> planClassList = ocasMapper.getPlanClassByInsured(certificateNo, Arrays.asList(policyList.split(",")), insuredName);
        Set<String> LossSet = new HashSet<>();

        for (String planClass : planClassList) {
            List<String> reasons = LossClassConstant.PRODUCT_CLASS_LOSS_MAP.get(planClass);
            if (reasons != null) {
                LossSet.addAll(reasons);
            }
        }
        List<LossTypeVO> returnList=getLossClass();
        if (CollectionUtil.isNotEmpty(LossSet)) {
            returnList = returnList.stream().filter(dto -> LossSet.contains(dto.getCode())).collect(Collectors.toList());

        }
        return returnList;
    }
    @Override
    public List<LossTypeVO> getLossClass() {
        List<CommonParameterTinyDTO> commonParameterList = commonParameterMapper.getCommonParameterList(new String[]{"LOSS_TYPE_CODE"});
            List<LossTypeVO> returnList=new ArrayList<>();
            if(CollectionUtil.isNotEmpty(commonParameterList)){
                for (CommonParameterTinyDTO dto : commonParameterList) {
                    LossTypeVO lossTypeVO = new LossTypeVO();
                    lossTypeVO.setCode(dto.getValueCode());
                    lossTypeVO.setName(dto.getValueChineseName());
                    returnList.add(lossTypeVO);
                }
            }
        return returnList;
    }
}
