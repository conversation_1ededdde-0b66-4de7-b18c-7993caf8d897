package com.paic.ncbs.claim.service.settle.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.ClientTypePersonalEnum;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.*;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.vo.settle.EpcisRequestVO;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import com.paic.ncbs.claim.model.vo.settle.PolicyEnendorsementVO;
import com.paic.ncbs.claim.service.antimoneylaundering.AntiMoneyLaunderingService;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsAmlCompanyInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleValidateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;

@RefreshScope
@Service("settleValidateService")
@Transactional
@Slf4j
public class SettleValidateServiceImpl implements SettleValidateService {

    @Value("${switch.check:N}")
    private String switchCheck;
    @Autowired
    private MaxPayService maxPayServiceImpl;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private DutyPayMapper dutyPayDao;

    @Autowired
    private AntiMoneyLaunderingService antiMoneyLaunderingService;

    @Autowired
    private ClmsAmlCompanyInfoService amlCompanyInfoService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private PolicyPayMapper policyPayDao;

    @Autowired
    private PaymentInfoMapper paymentInfoMapper;

    /**
     * 理算校验
     */
    @Override
    public void checkSettle(List<PolicyPayDTO> policyPays, List<PaymentItemComData> paymentItems,
                            String reportNo, Integer caseTimes) throws GlobalBusinessException {
        //获取赔付金额 页面责任明细理算金额的合计
        BigDecimal settleAmount = BigDecimal.ZERO;
        for(PolicyPayDTO dto:policyPays){
            settleAmount = settleAmount.add(dto.getSettleAmount());
        }
        // 总的支付金额
        BigDecimal sumPay = paymentItems.stream().map(p -> DutyPayDTO.nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 校验责任明细理算金额 和 总的支付金额必须相等
        if (settleAmount.compareTo(sumPay) != 0){
            throw new GlobalBusinessException("理算金额和赔款赔付金额不一致，请排查！");
        }
        //设置最大赔付额
        maxPayServiceImpl.initPoliciesPayMaxPay(policyPays, null);
        log.info("checkSettle设置最大赔付额完成报案号={}",reportNo);
        checkSetteAmountGtMaxPay(policyPays);
        log.info("checkSetteAmountGtMaxPay完成报案号={}",reportNo);
    }

    @Override
    public void checkSettled(List<PolicyPayDTO> policyPays, List<PaymentItemComData> paymentItems) throws GlobalBusinessException {
        //如果没有理算金额和费用项目
        if (!hasSettleAmount(policyPays) && !hasFeeItem(paymentItems)) {
            throw new GlobalBusinessException(ErrorCode.Settle.NO_SETTLE);
        }
    }

    @Override
    public void checkEndorsement(List<PolicyPayDTO> policyPays, EndorsementDTO endorsement) throws GlobalBusinessException {
        //如果赔付金额为0，直接返回
        if (!hasSettlePay(policyPays)) {
            return;
        }
        if (null == endorsement) {
            throw new GlobalBusinessException(ErrorCode.Settle.SETTLE_EMPTY_ENDORSEMENT);
        }
        String endorsementRemark = endorsement.getEndorsementRemark();

        if (StringUtils.isEmpty(endorsementRemark) || "[]".equals(endorsementRemark)) {
            throw new GlobalBusinessException(ErrorCode.Settle.SETTLE_EMPTY_ENDORSEMENT);
        }
        //批单信息json串转对象
        if(endorsementRemark.startsWith("[") && endorsementRemark.endsWith("]")){
            List<PolicyEnendorsementVO> policyEnendorsements = JSONObject.parseArray(endorsementRemark, PolicyEnendorsementVO.class);
            if (!hasEnendorsement(policyEnendorsements)) {
                throw new GlobalBusinessException(ErrorCode.Settle.SETTLE_EMPTY_ENDORSEMENT);
            }
        }

    }


    @Override
    public void validDutyDetailNull(String reportNo, Integer caseTimes) {
        // 查询被保人及保单共享信息
        // 校验责任明细信息及责任明细基本保额不能为空
        List<PolicyPayDTO> copyPolicys = policyPayService.selectFromPolicyCopy(reportNo, caseTimes);
        for (PolicyPayDTO policy : copyPolicys) {
            String policyNo = policy.getPolicyNo();
            for (PlanPayDTO plan : policy.getPlanPayArr()) {
                String planCode = plan.getPlanCode();
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    String dutyCode = duty.getDutyCode();
                    List<DutyDetailPayDTO> details = duty.getDutyDetailPayArr();
                    if (CollectionUtils.isEmpty(details)) {
                        LogUtil.info("保单{}的险种code{}的责任code{}的责任明细为空，不能理算完成，请在IT问题管理系统上报补充数据", policyNo, planCode, dutyCode);
                        throw new GlobalBusinessException(ErrorCode.Settle.DUTY_DETAIL_NULL, policyNo);
                    }
                    for (DutyDetailPayDTO detail : details) {
                        String detailCode = detail.getDutyDetailCode();
                        BigDecimal baseAmountPay = detail.getBaseAmountPay();
                        if (null == baseAmountPay) {
                            LogUtil.info("责任明细基本保额为空,报案号:{},赔付次数:{},保单号:{},险种:{},责任:{},责任明细:{}", reportNo, caseTimes,
                                    policyNo, planCode, dutyCode, detailCode);
                            throw new GlobalBusinessException(ErrorCode.Settle.DETAIL_BASEAMOUNT_NULL, policyNo);
                        }
                    }
                }
            }
        }
    }

    private void checkShareDetailAmount(List<DutyDetailPayDTO> dutyDetailPays, BigDecimal shareDetailAmount) throws GlobalBusinessException {
        for (DutyDetailPayDTO detail : dutyDetailPays) {
            BigDecimal detailmaxAmoutPay = detail.getMaxAmountPay();
            if (shareDetailAmount.compareTo(detailmaxAmoutPay) > 0) {
                String detailCode = detail.getDutyDetailCode();
                LogUtil.info("责任明细编码{}的共享险种的责任明细理算金额总和{}大于其最大给付额{}",
                        detailCode, shareDetailAmount.toString(), detailmaxAmoutPay.toString());
                throw new GlobalBusinessException(ErrorCode.Settle.SHARE_DETAIL_AMOUNT_GT_MAX_AMOUNT, detailCode, shareDetailAmount.toString(), detailmaxAmoutPay.toString());
            }
        }
    }

    private void checkSetteAmountGtMaxPay(List<PolicyPayDTO> policyPays) throws GlobalBusinessException {
        for (PolicyPayDTO policyPayDTO : policyPays) {
            List<EpcisPlanDTO> epcisPlans = getEpicsPrePayPlans(policyPayDTO);
            // 记录所有险种的责任信息
            List<DutyPayDTO> allDutyPayList = new ArrayList<>();
            for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                allDutyPayList.addAll(planPayDTO.getDutyPayArr());
                checkDutySetteAmount(planPayDTO, policyPayDTO, epcisPlans);
            }
            checkDutyShareAmount(allDutyPayList);
        }

    }

    private List<EpcisPlanDTO> getEpicsPrePayPlans(PolicyPayDTO policy) {
        PolicyClaimCaseDTO policyClaimCase = Optional.ofNullable(policy.getPolicyClaimCase()).orElseGet(PolicyClaimCaseDTO::new);
        if (StringUtils.isEmpty(policyClaimCase.getInsuredCode()) || StringUtils.isEmpty(policyClaimCase.getSubpolicyNo())) {
            EpcisRequestVO vo = policyPayService.getEpicsRequest(policyClaimCase.getPartyNo(), policyClaimCase.getPolicyNo(), policy.getReportNo());
            if (null != vo) {
                policyClaimCase.setInsuredCode(vo.getInsuredCode());
                policyClaimCase.setSubpolicyNo(vo.getSubpolicyNo());
            }
        }
        return new ArrayList<>();
    }

    private void checkDutySetteAmount(PlanPayDTO planPayDTO, PolicyPayDTO policyPay, List<EpcisPlanDTO> epcisPlans) throws GlobalBusinessException {
        List<DutyPayDTO> dutyPays = planPayDTO.getDutyPayArr();
        List<EpcisDutyDTO> epcisDutys = getEpicsDutys(epcisPlans, planPayDTO);

        dutyPays.forEach(dutyPayDTO->{
            List<DutyAttributeDTO>  dutyAttributeDTOS = dutyPayDTO.getAttributes();
            String limitType="";
            if(!CollectionUtils.isEmpty(dutyAttributeDTOS)){
                for (DutyAttributeDTO dto : dutyAttributeDTOS) {
                    if(Objects.equals(DutyAttributeConst.PAY_LIMIT_TYPE,dto.getAttrCode())&&Objects.equals("2",dto.getAttrValue())){
                        limitType="2";
                        break;
                    }
                }
            }
            if(!Objects.equals("2",limitType)){
                for (DutyDetailPayDTO dutyDetailPayDTO :dutyPayDTO.getDutyDetailPayArr()) {
                    BigDecimal detailPayLmit = dutyDetailPayDTO.getPayLimit();
                    if (detailPayLmit != null){
                        BigDecimal settleAmount = nvl(dutyDetailPayDTO.getSettleAmount(), 0);
                        if (settleAmount.compareTo(detailPayLmit) > 0) {
                            String dutyDetailCode = dutyDetailPayDTO.getDutyDetailCode();
                            LogUtil.info(String.format("责任明细理算金额大于责任明细的赔偿限额,责任明细理算金额为%s,赔偿限额%s,责任明细名称为:%s",
                                    settleAmount.toString(), detailPayLmit.toString(), dutyDetailPayDTO.getDutyDetailName()));
                            throw new GlobalBusinessException(ErrorCode.Settle.DUTY_SETTLE_AMOUNT_GT_LIMIT_AMOUNT, dutyDetailCode + "责任明细理算金额大于责任明细的赔偿限额");
                        }
                    }
                }
            }
//            // 赔偿限额校验 责任理算不能大于赔偿限额
//            dutyPayDTO.getDutyDetailPayArr().forEach(dutyDetailPayDTO -> {
//                BigDecimal detailPayLmit = dutyDetailPayDTO.getPayLimit();
//                if (detailPayLmit != null){
//                    BigDecimal settleAmount = nvl(dutyDetailPayDTO.getSettleAmount(), 0);
//                    if (settleAmount.compareTo(detailPayLmit) > 0) {
//                        String dutyDetailCode = dutyDetailPayDTO.getDutyDetailCode();
//                        LogUtil.info(String.format("责任明细理算金额大于责任明细的赔偿限额,责任明细理算金额为%s,赔偿限额%s,责任明细名称为:%s",
//                                settleAmount.toString(), detailPayLmit.toString(), dutyDetailPayDTO.getDutyDetailName()));
//                        throw new GlobalBusinessException(ErrorCode.Settle.DUTY_SETTLE_AMOUNT_GT_LIMIT_AMOUNT, dutyDetailCode + "责任明细理算金额大于责任明细的赔偿限额");
//                    }
//                }
//            });
            //2024年02-02月 经与吴思佳确认  这个校验去掉，
//            if ("Y".equals(switchCheck)) {
//                BigDecimal payLimit = dutyPayDTO.getPayLimit(); //TODO 因为日限额在责任属性层，需要后面更改
//                if (payLimit != null) {
//                    BigDecimal settleAmount = nvl(dutyPayDTO.getSettleAmount(), 0);
//                    if (settleAmount.compareTo(payLimit) > 0) {
//                        String dutyCode = dutyPayDTO.getDutyCode();
//                        LogUtil.info(String.format("责任理算金额大于责任的赔偿限额,责任理算金额为%s,赔偿限额%s,责任名称为:%s",
//                                settleAmount.toString(), payLimit.toString(),
//                                dutyPayDTO.getDutyName()));
//                        throw new GlobalBusinessException(ErrorCode.Settle.DUTY_SETTLE_AMOUNT_GT_LIMIT_AMOUNT, dutyCode + "责任理算金额大于责任的赔偿限额");
//                    }
//                }
//            }
        });

        //非责任共享保额
        List<DutyPayDTO> normalList = dutyPays.stream().filter(duty->!duty.getIsDutyShareAmount()).collect(Collectors.toList());
        for (DutyPayDTO dutyPayDTO : normalList) {
            if (!ModelConsts.CLAIM_TYPE_PAY.equals(dutyPayDTO.getClaimType())) {
                continue;
            }
            BigDecimal maxAmountPay = nvl(dutyPayDTO.getMaxAmountPay(), 0);
            BigDecimal settleAmount = nvl(dutyPayDTO.getSettleAmount(), 0);
            if (settleAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (settleAmount.compareTo(maxAmountPay) > 0) {
                String dutyCode = dutyPayDTO.getDutyCode();
                LogUtil.info(String.format("责任理算金额大于责任最大给付额,责任理算金额为%s,责任剩余给付额为%s,责任名称为:%s",
                        settleAmount.toString(), maxAmountPay.toString(),
                        dutyPayDTO.getDutyName()));
                throw new GlobalBusinessException(ErrorCode.Settle.DUTY_SETTLE_AMOUNT_GT_MAX_AMOUNT, dutyCode);
            }
            // policyClaimCase
            PolicyClaimCaseDTO policyClaimCase =Optional.ofNullable(policyPay.getPolicyClaimCase()).orElseGet(()->{
                PolicyClaimCaseDTO p=new PolicyClaimCaseDTO();
                p.setReportNo(policyPay.getReportNo());
                p.setCaseNo(policyPay.getCaseNo());
                return p;
            });
            BigDecimal dutyPrePay = getNoEndCasePreDutyPays(policyClaimCase, dutyPayDTO);
            BigDecimal epcisDutyPrePay = getEpicsPrePayDutyAmount(epcisDutys, dutyPayDTO);
            if (null != epcisDutyPrePay) {
                dutyPrePay = sum(dutyPrePay, epcisDutyPrePay);
            }
            if (sum(settleAmount, dutyPrePay).compareTo(maxAmountPay) > 0) {
                String dutyCode = dutyPayDTO.getDutyCode();
                LogUtil.info(String.format("责任理算金额+未结案案件的历次预赔赔款金额(包括老系统预赔付金额)大于责任最大给付额,责任缮制金额为%s,责任最大给付额为%s,责任预赔付金额为%s,责任名称为:%s",
                        settleAmount.toString(), maxAmountPay.toString(), dutyPrePay,
                        dutyPayDTO.getDutyName()));
                throw new GlobalBusinessException(ErrorCode.Settle.DUTY_AMOUNT_GT_MAX_PAY, dutyPayDTO.getPolicyNo(), dutyPayDTO.getPlanCode(), dutyCode, settleAmount.toString(),
                        maxAmountPay.toString(), nvl(dutyPrePay, 0).toString());
            }
            List<DutyDetailPayDTO> dutyDetailPays = dutyPayDTO.getDutyDetailPayArr();
            checkDutyDetailAmount(dutyDetailPays);
        }
    }

    private void checkDutyShareAmount(List<DutyPayDTO> dutyPayDTOList){
        //责任共享保额
        Map<String,List<DutyPayDTO>> shareDuty = dutyPayDTOList.stream().filter(DutyPayDTO::getIsDutyShareAmount).collect(Collectors.groupingBy(DutyPayDTO::getShareDutyGroup));
        if (!shareDuty.isEmpty()){
            shareDuty.forEach((k,v)->{
                BigDecimal maxAmountPay = nvl(v.get(0).getMaxAmountPay(),0);
                BigDecimal settleAmount = v.stream().map(duty-> nvl(duty.getSettleAmount(), 0)).reduce(BigDecimal.ZERO,BigDecimal::add);
                if (settleAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                if (settleAmount.compareTo(maxAmountPay) > 0) {
                    LogUtil.info("责任共享保额的责任理算金额总和大于责任共享的最大给付额,责任理算金额总和为{},责任剩余给付额为{},责任编码为:{}", settleAmount, maxAmountPay, k);
                    throw new GlobalBusinessException(ErrorCode.Settle.DUTY_SETTLE_AMOUNT_GT_MAX_AMOUNT,
                            "责任编码为:"+ k +"的理算金额总和:["+ settleAmount +"]不能大于责任共享的最大给付额：["+ maxAmountPay +"]");
                }
                v.forEach(duty->{
                    List<DutyDetailPayDTO> dutyDetailPays = duty.getDutyDetailPayArr();
                    checkDutyDetailAmount(dutyDetailPays);
                });
            });
        }
    }

    private List<EpcisDutyDTO> getEpicsDutys(List<EpcisPlanDTO> epcisPlans, PlanPayDTO planPay) {
        if (CollectionUtils.isEmpty(epcisPlans)) {
            return new ArrayList<>();
        }
        for (EpcisPlanDTO epcisPlan : epcisPlans) {
            if (planPay.getPlanCode().equals(epcisPlan.getPlanCode())) {
                return epcisPlan.getDutyInfo();
            }
        }
        return new ArrayList<>();
    }

    private BigDecimal getNoEndCasePreDutyPays(PolicyClaimCaseDTO policyClaimCase, DutyPayDTO dutyPayDTO) {
        MaxPayParam dutyMaxPayParam = new MaxPayParam();
        dutyMaxPayParam.setReportNo(policyClaimCase.getReportNo());
        dutyMaxPayParam.setPolicyNo(dutyPayDTO.getPolicyNo());
        dutyMaxPayParam.setPlanCode(dutyPayDTO.getPlanCode());
        dutyMaxPayParam.setDutyCode(dutyPayDTO.getDutyCode());
        dutyMaxPayParam.setDutyBaseAmount(dutyPayDTO.getBaseAmountPay());
        dutyMaxPayParam.setInsuredCode(policyClaimCase.getInsuredCode());
        dutyMaxPayParam.setSubpolicyNo(policyClaimCase.getSubpolicyNo());
        dutyMaxPayParam.setCaseNo(policyClaimCase.getCaseNo());
        return dutyPayDao.getNoEndCasePreDutyAmount(dutyMaxPayParam);
    }

    private void checkDutyDetailAmount(List<DutyDetailPayDTO> dutyDetailPays) throws GlobalBusinessException {
        for (DutyDetailPayDTO dutyDetailPayDTO : dutyDetailPays) {
            BigDecimal manualSettleAmount = dutyDetailPayDTO.getSettleAmount();
            BigDecimal autoSettleAmount = dutyDetailPayDTO.getAutoSettleAmount();
            BigDecimal detailMaxAmountPay = dutyDetailPayDTO.getMaxAmountPay();
            BigDecimal detailSettleAmount = (BigDecimalUtils.isNullBigDecimal(manualSettleAmount) ? autoSettleAmount : manualSettleAmount);
            if (null == detailSettleAmount) {
                continue;
            }
            if (detailSettleAmount.compareTo(detailMaxAmountPay) > 0) {
                String detailCode = dutyDetailPayDTO.getDutyDetailCode();
                LogUtil.info(String.format("责任明细理算金额大于责任明细最大给付额,责任明细缮制金额为%s,责任明细最大给付额为%s,责任明细名称为:%s",
                        detailSettleAmount.toString(), detailMaxAmountPay.toString(),
                        dutyDetailPayDTO.getDutyDetailName()));
                throw new GlobalBusinessException(ErrorCode.Settle.DETAIL_SETTLE_AMOUNT_GT_MAX_AMOUNT, detailCode);
            }
        }
    }

    private BigDecimal getEpicsPrePayDutyAmount(List<EpcisDutyDTO> epcisDutys, DutyPayDTO dutyPay) {
        if (CollectionUtils.isEmpty(epcisDutys)) {
            return null;
        }
        for (EpcisDutyDTO epcisDuty : epcisDutys) {
            if (dutyPay.getDutyCode().equals(epcisDuty.getDutyCode())) {
                return epcisDuty.getDutyHistoryPrepayAmount();
            }
        }
        return null;
    }

    private boolean hasSettleAmount(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return false;
        }
        for (PolicyPayDTO policy : policyPays) {
            for (PlanPayDTO plan : policy.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                        if (null != detail.getSettleAmount() || null != detail.getAutoSettleAmount()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private boolean hasFeeItem(List<PaymentItemComData> paymentItems) {
        if (CollectionUtils.isEmpty(paymentItems)) {
            return false;
        }
        for (PaymentItemComData paymentItem : paymentItems) {
            String paymentType = paymentItem.getPaymentType();
            if (SettleConst.PAYMENT_TYPE_FEE.equals(paymentType)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean hasSettlePay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return false;
        }
        for (PolicyPayDTO policy : policyPays) {
            if (nvl(policy.getSettleAmount(), 0).compareTo(BigDecimal.ZERO) != 0) {
                return true;
            }
        }
        return false;
    }

    private boolean hasEnendorsement(List<PolicyEnendorsementVO> policyEnendorsements) {
        if (CollectionUtils.isEmpty(policyEnendorsements)) {
            return false;
        }
        for (PolicyEnendorsementVO policyEnendorsement : policyEnendorsements) {
            if (!CollectionUtils.isEmpty(policyEnendorsement.getDetailArr())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void checkCoinsureRadio(CoinsureInfoDTO coinsureInfo) throws GlobalBusinessException {
        String isCoinsureFee = coinsureInfo.getIsCoinsureFee();
        String isCoinsurePay = coinsureInfo.getIsCoinsurePay();
        String policyNo = coinsureInfo.getPolicyNo();
        if(SettleConst.IS_COINSURE_N.equals(isCoinsureFee) && SettleConst.IS_COINSURE_N.equals(isCoinsurePay)){
            return;
        }
        BigDecimal coinsureRadio = BigDecimal.ZERO;
        for (CoinsureDTO coinsure : coinsureInfo.getCoinsureDtos()) {
            coinsureRadio = BigDecimalUtils.sum(coinsureRadio,coinsure.getReinsureScale());
        }
        if(!(BigDecimal.ONE.compareTo(coinsureRadio)==0)){
            throw new GlobalBusinessException(ErrorCode.Settle.COINSURE_RADIO_ILLEGAL,policyNo);
        }
    }

    @Override
    public void checkPolicyPay(List<PolicyPayDTO> policyPays, String reportNo,Integer caseTimes) {
        //获取赔付金额 页面责任明细理算金额的合计
        BigDecimal settleAmount = BigDecimal.ZERO;
        for(PolicyPayDTO dto:policyPays){
            settleAmount = settleAmount.add(dto.getSettleAmount());
        }
        //校验支付方式：如果支付方式为微信零钱，赔付金额不能超过20000
        PaymentInfoDTO paymentInfoDTO =new PaymentInfoDTO();
        paymentInfoDTO.setReportNo(reportNo);
        paymentInfoDTO.setCaseTimes(caseTimes);
        List<PaymentInfoDTO> paymentInfoDTOS = paymentInfoMapper.getPaymentInfo(paymentInfoDTO);
        if(CollectionUtils.isEmpty(paymentInfoDTOS)){
            throw new GlobalBusinessException("PaymentItemDTO没有查询到支付信息");
        }
        if(Objects.equals("1",paymentInfoDTOS.get(0).getPayType())){
            if(settleAmount.compareTo(new BigDecimal(20000))>0){
                throw new GlobalBusinessException("赔付金额超过2万，不能选择领款“微信零钱”领款方式，微信零钱限额2万");
            }
        }

        if (caseTimes > 1) {
            // 重开理算金额校验，必须大于等于上一次不为零的理算金额
            BigDecimal lastPolicyPayAmount = policyPayService.getLastPolicyPayAmount(reportNo, caseTimes);
            if (lastPolicyPayAmount.compareTo(settleAmount) > 0) {
                throw new GlobalBusinessException(ErrorCode.Settle.CHECK_SETTLE_LAST_PAY,"重开赔付金额不能小于上一次理算金额");
            }
        }
        // 校验赔付金额必须大于预赔金额
        BigDecimal prePayAmount = nvl(policyPayDao.getPrePayAmount(reportNo, caseTimes),0);
        if (settleAmount.compareTo(prePayAmount) < 0 ){
            throw new GlobalBusinessException(ErrorCode.Settle.POLICY_PAY_OVERSTEP_ESTIMATEAMOUNT,"赔付金额不能小于预赔金额");
        }
        //如果赔付金额为0，直接返回
        if (!hasSettlePay(policyPays)) {
            return;
        }
        //通过单号查询立案金额
        BigDecimal estimateAmount = dutyPayDao.getEstimateAmount(reportNo,caseTimes);

        //判断赔付金额与立案金额大小
        if(settleAmount.compareTo(estimateAmount)>0){
            throw new GlobalBusinessException(ErrorCode.Settle.POLICY_PAY_OVERSTEP_ESTIMATEAMOUNT,"赔付金额不能大于立案金额");
        }

    }

    @Override
    public void checkAntiMoneyLaundering(String reportNo, Integer caseTimes, List<PaymentItemComData> paymentItemVOList, Map<String, PaymentInfoDTO> paymentInfoMap) {
        if (CollectionUtils.isEmpty(paymentItemVOList)) {
            return;
        }

        Map<String, List<PaymentItemComData>> companyPaymentItemMap = Maps.newHashMap();
        Map<String, List<PaymentItemComData>> personalPaymentItemMap = Maps.newHashMap();
        for (PaymentItemComData paymentItem : paymentItemVOList) {
            PaymentInfoDTO paymentInfo = paymentInfoMap.get(paymentItem.getIdClmPaymentInfo());
            if (BaseConstant.STRING_0.equals(paymentInfo.getBankAccountAttribute())) {
                // 公司
                String key = paymentInfo.getClientName() + Constants.SEPARATOR_LINE + paymentInfo.getOrganizeCode();
                List<PaymentItemComData> dataList = companyPaymentItemMap.get(key);
                if (CollectionUtils.isEmpty(dataList)) {
                    dataList = Lists.newArrayList();
                    companyPaymentItemMap.put(key, dataList);
                }
                dataList.add(paymentItem);
            } else {
                // 个人
                String key = paymentInfo.getClientName() + Constants.SEPARATOR_LINE + paymentInfo.getClientCertificateType() + Constants.SEPARATOR_LINE + paymentInfo.getClientCertificateNo();
                List<PaymentItemComData> dataList = personalPaymentItemMap.get(key);
                if (CollectionUtils.isEmpty(dataList)) {
                    dataList = Lists.newArrayList();
                    personalPaymentItemMap.put(key, dataList);
                }
                dataList.add(paymentItem);
            }
        }

        StringBuilder builder = new StringBuilder();
        BigDecimal decimal = new BigDecimal(10000);
        ClmsAntiMoneyLaunderingInfoDto queryDTO = new ClmsAntiMoneyLaunderingInfoDto();
        queryDTO.setReportNo(reportNo);
        queryDTO.setCaseTimes(caseTimes);

        // 校验公司
        for (Map.Entry<String, List<PaymentItemComData>> entry : companyPaymentItemMap.entrySet()) {
            List<PaymentItemComData> dataList = entry.getValue();
            BigDecimal sumPay = dataList.stream().map(p -> DutyPayDTO.nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (decimal.compareTo(sumPay) > 0) {
                dataList = dataList.stream().filter(i-> ClientTypePersonalEnum.CLIENT_TYPE_09.getCode().equals(i.getClientType()) && PaymentTypeEnum.PAY.getType().equals(i.getPaymentType())).collect(Collectors.toList());
            }

            for (PaymentItemComData paymentItem : dataList) {
                PaymentInfoDTO paymentInfo = paymentInfoMap.get(paymentItem.getIdClmPaymentInfo());
                queryDTO.setCustomerNo(paymentInfo.getCustomerNo());
                log.info("checkAntiMoneyLaundering company queryDTO: {}", JSON.toJSONString(queryDTO));
                ClmsAntiMoneyLaunderingInfoDto amlCompanyInfo = amlCompanyInfoService.getAmlCompanyInfo(queryDTO);
                log.info("checkAntiMoneyLaundering amlCompanyInfo: {}", JSON.toJSONString(amlCompanyInfo));
                if (Objects.nonNull(amlCompanyInfo)) {
                    // 校验数据是否完整
                    boolean checkFail = antiMoneyLaunderingInfoIntegrityCheck(amlCompanyInfo, paymentInfo.getBankAccountAttribute());
                    if (checkFail) {
                        builder.append(paymentInfo.getClientName()).append("、");
                    }
                } else {
                    // 未录入过反洗钱信息，需要判断反洗钱信息是否完整，如不完整，则依然提示需要完善信息
                    builder.append(paymentInfo.getClientName()).append("、");
                }
            }
        }

        // 校验个人
        for (Map.Entry<String, List<PaymentItemComData>> entry : personalPaymentItemMap.entrySet()) {
            List<PaymentItemComData> dataList = entry.getValue();
            BigDecimal sumPay = dataList.stream().map(p -> DutyPayDTO.nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (decimal.compareTo(sumPay) > 0) {
                dataList = dataList.stream().filter(i-> ClientTypePersonalEnum.CLIENT_TYPE_09.getCode().equals(i.getClientType()) && PaymentTypeEnum.PAY.getType().equals(i.getPaymentType())).collect(Collectors.toList());
            }

            for (PaymentItemComData paymentItem : dataList) {
                PaymentInfoDTO paymentInfo = paymentInfoMap.get(paymentItem.getIdClmPaymentInfo());
                queryDTO.setCustomerNo(paymentInfo.getCustomerNo());
                log.info("checkAntiMoneyLaundering personal queryDTO: {}", JSON.toJSONString(queryDTO));
                ClmsAntiMoneyLaunderingInfoDto amlPersonalInfo = antiMoneyLaunderingService.getClmsAntiMoneyLaunderingInfo(queryDTO);
                log.info("checkAntiMoneyLaundering amlPersonalInfo: {}", JSON.toJSONString(amlPersonalInfo));
                if (Objects.nonNull(amlPersonalInfo)) {
                    // 校验数据是否完整
                    boolean checkFail = antiMoneyLaunderingInfoIntegrityCheck(amlPersonalInfo, paymentInfo.getBankAccountAttribute());
                    if (checkFail) {
                        builder.append(paymentInfo.getClientName()).append("、");
                    }
                } else {
                    // 未录入过反洗钱信息，需要判断反洗钱信息是否完整，如不完整，则依然提示需要完善信息
                    builder.append(paymentInfo.getClientName()).append("、");
                }
            }
        }

        if(StringUtils.isNotEmpty(builder)){
            builder.setLength(builder.length() - 1);
            String errMsg = "请完善领款人:" + builder.toString() + "反洗钱相关信息!";
            log.info("checkAntiMoneyLaundering errMsg: {}", errMsg);
            throw new GlobalBusinessException(ErrorCode.Settle.CHECK_ANTI_MONEY_LAUNDERING, errMsg);
        }
    }

    @Override
    public void checkBankDetail(List<PaymentItemComData> paymentItemVOList, Map<String, PaymentInfoDTO> paymentInfoMap) throws GlobalBusinessException {
        //赔付信息集合，如果集合为空，直接返回，不做校验
        if(!ListUtils.isEmptyList(paymentItemVOList)){
            // 因重开页面逻辑过于复杂，故都是按照整案理算的信息进行赔付信息填充

            //step1：同银行账户 汇总支付总额
            Map<String, List<PaymentItemComData>> itemGroupByType = paymentItemVOList.stream().collect(Collectors.groupingBy(PaymentItemComData::getClientBankAccount));
            StringBuilder builder = new StringBuilder();
            itemGroupByType.forEach((k,v)->{
                //step3: 校验同一用户名汇总金额是否大于等于1000000.
                BigDecimal sumPay = v.stream().map(p -> DutyPayDTO.nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (new BigDecimal(1000000).compareTo(sumPay) <= 0 ){
                    v.forEach(item->{
                        if (StringUtils.isEmpty(paymentInfoMap.get(item.getIdClmPaymentInfo()).getBankDetailCode())){
                            builder.append(k).append("的银行账号").append("、");
                        }
                    });
                }
            });
            //step2：检查是否有满足条件的数据，如有给出提示，如无校验通过
            if(StringUtils.isNotEmpty(builder)){
                builder.setLength(builder.length() -1 );
                throw new GlobalBusinessException(ErrorCode.Settle.CHECK_SETTLE_BANK_DETAIL,
                        "请完善领款人:" + builder.toString() + "银行开户行明细!");
            }

        }
    }


    @Override
    public void checkLastPaymentItem(List<PaymentItemComData> paymentItemVOList, String reportNo, Integer caseTimes) throws GlobalBusinessException {
        //赔付信息集合，如果集合为空，直接返回，不做校验
        if (CollectionUtils.isEmpty(paymentItemVOList)){
            throw new GlobalBusinessException(ErrorCode.Settle.NO_SETTLE,"赔付信息不能为空!");
        }
        Map<String, List<PaymentItemComData>> itemGroupByType = paymentItemVOList.stream()
                .collect(Collectors.groupingBy(paymentItem -> paymentItem.getClientName() +  "-" + paymentItem.getClientBankAccount()));
        itemGroupByType.forEach((k,v)->{
            String[] split = k.split("-");
            if (split.length != 2) {
                v.forEach(paymentItem -> {
                    //查询支付方式，微信零钱不需要验证银行账号
                    PaymentInfoDTO paymentInfo = paymentInfoMapper.getPaymentInfoById(paymentItem.getIdClmPaymentInfo());
                    if(!"1".equals(paymentInfo.getPayType())) {
                        throw new GlobalBusinessException(ErrorCode.Settle.NO_SETTLE,"领款人信息异常!");
                    }
                });
            }
            String clientName = split[0];
            BigDecimal paymentAmount = v.stream().map(p -> DutyPayDTO.nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (int i = caseTimes - 1; i > 0; i--) {
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setClientName(clientName);
                paymentItemDTO.setReportNo(reportNo);
                paymentItemDTO.setCaseTimes(i);
                if(split.length == 2) {
                    paymentItemDTO.setClientBankAccount(split[1]);
                }
                BigDecimal lastPaymentAmount  = nvl(paymentItemService.getLastPaymentAmount(paymentItemDTO),0);
                if (lastPaymentAmount.compareTo(paymentAmount) > 0) {
                    throw new GlobalBusinessException(ErrorCode.Settle.NO_SETTLE,clientName+"赔付信息不能比上一次的少!");
                }
            }
        });
    }

    /**
     * 校验个人反洗钱数据
     * @param amlInfo 反洗钱信息
     * @param bankAccountAttribute 帐号类型:个人帐号=1,公司帐号=0
     * @return 校验结果
     */
    private boolean antiMoneyLaunderingInfoIntegrityCheck(ClmsAntiMoneyLaunderingInfoDto amlInfo, String bankAccountAttribute) {
        // 校验公司反洗钱信息
        if (BaseConstant.STRING_0.equals(bankAccountAttribute)) {
            ClmsAmlCompanyInfoVerification companyCheckDTO = new ClmsAmlCompanyInfoVerification();
            BeanUtils.copyProperties(amlInfo, companyCheckDTO);
            log.info("antiMoneyLaunderingInfoIntegrityCheck companyCheckDTO: {}", JSON.toJSONString(companyCheckDTO));
            if (!isAllFieldNotNull(companyCheckDTO)) {
                return true;
            }

            if (CollectionUtils.isEmpty(amlInfo.getShareholderInfoDtoList())) {
                return true;
            }

            for (ClmsShareholderInfoDto shareholderInfoDto : amlInfo.getShareholderInfoDtoList()) {
                ClmsShareholderInfoVerification holderCheckDTO = new ClmsShareholderInfoVerification();
                BeanUtils.copyProperties(shareholderInfoDto, holderCheckDTO);
                log.info("antiMoneyLaunderingInfoIntegrityCheck holderCheckDTO: {}", JSON.toJSONString(holderCheckDTO));
                if (!isAllFieldNotNull(holderCheckDTO)) {
                    return true;
                }
            }
        }

        // 校验个人反洗钱信息
        if (BaseConstant.STRING_1.equals(bankAccountAttribute)) {
            ClmsAntiMoneyLaunderingInfoVerification personalCheckDTO = new ClmsAntiMoneyLaunderingInfoVerification();
            BeanUtils.copyProperties(amlInfo, personalCheckDTO);
            log.info("antiMoneyLaunderingInfoIntegrityCheck personalCheckDTO: {}", JSON.toJSONString(personalCheckDTO));
            return !isAllFieldNotNull(personalCheckDTO);
        }

        return false;
    }

    /**
     * 校验对象属性是否都不为空，存在为空返回false，否则返回true
     * @param obj 入参对象
     * @return 校验结果
     */
    private static boolean isAllFieldNotNull(Object obj) {
        Class stuCla = obj.getClass();
        Field[] fs = stuCla.getDeclaredFields();
        for(Field f : fs){
            ReflectionUtils.makeAccessible(f);
            Object val;
            try {
                val = f.get(obj);
                if(val == null || "".equals(val)){
                    return false;
                }
            } catch (IllegalAccessException e) {
                log.info("校验异常");
            }

        }
        return true;
    }
}
