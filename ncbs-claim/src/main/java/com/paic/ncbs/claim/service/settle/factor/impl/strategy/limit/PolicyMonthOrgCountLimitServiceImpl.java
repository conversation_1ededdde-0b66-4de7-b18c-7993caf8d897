package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleTemplateDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 定制化产品方案月赔付次数实现
 */
@Slf4j
@Service
@Order(5)
@RefreshScope
public class PolicyMonthOrgCountLimitServiceImpl implements ExtendedLimitService {
    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;

    /**
     * 保单月限额配置方案
     */
    @Value("#{${policyLimit.monthOrgCount}}")
    private Map<String, Integer> policyMonthOrgCountLimitMap;

    @Value("${special.productPackage}")
    private String specialProductPackage;

    /**
     * 执行保单是否匹配的本扩展
     *
     * @param policyPayDTO
     * @return
     */
    @Override
    public boolean isMatch(PolicyPayDTO policyPayDTO) {
        return policyMonthOrgCountLimitMap != null && policyMonthOrgCountLimitMap.containsKey(policyPayDTO.getProductPackage());
    }

    @Override
    public void cumulativeLimit(PolicyPayDTO policyPayDTO) {
        if (!isMatch(policyPayDTO)) {
            return;
        }
        log.info("案件:{},配置了保单月机构赔付次数开始处理！", policyPayDTO.getReportNo());
        Integer limitCount = policyMonthOrgCountLimitMap.get(policyPayDTO.getProductPackage());

        Map<Integer, List<String>> limitMap = new HashMap<>();
        List<PolicyMonthDto> monthDtoList = policyPayDTO.getMonthDtoList();
        ClmsDutyDetailBillSettleDTO billSettleDTOQuery = new ClmsDutyDetailBillSettleDTO();
        billSettleDTOQuery.setPolicyNo(policyPayDTO.getPolicyNo());
        List<ClmsDutyDetailBillSettleDTO> clmsPayBillSettleList = clmsDutyDetailBillSettleMapper.getClmsPayBillSettleList(billSettleDTOQuery);
        clmsPayBillSettleList = clmsPayBillSettleList.stream().filter(i -> !i.getReportNo().equals(policyPayDTO.getReportNo()) && (null == i.getSettleAmount() || BigDecimal.ZERO.compareTo(i.getSettleAmount()) != 0)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(clmsPayBillSettleList)) {
            for (ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO : clmsPayBillSettleList) {
                PolicyMonthDto startEndDate = getStartEndDate(clmsDutyDetailBillSettleDTO.getBillDate(), monthDtoList);
                if (null == startEndDate) {
                    continue;
                }
                List<String> hospitalList = limitMap.get(startEndDate.getMonth());
                if (CollectionUtils.isEmpty(hospitalList)) {
                    hospitalList = new ArrayList<>();
                    hospitalList.add(clmsDutyDetailBillSettleDTO.getBillDate() + clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getHospitalName());
                    limitMap.put(startEndDate.getMonth(), hospitalList);
                } else {
                    boolean matchResult = false;
                    for (int i = 0; i < hospitalList.size(); i++) {
                        String matchKey = hospitalList.get(i);
                        if(matchKey.contains(clmsDutyDetailBillSettleDTO.getBillDate().toString())){
                            if (matchKey.contains(clmsDutyDetailBillSettleDTO.getHospitalCode()) || matchKey.contains(clmsDutyDetailBillSettleDTO.getHospitalName())) {
                                matchResult = true;
                                String newMatchKey = matchKey + clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getHospitalName();
                                hospitalList.set(i, newMatchKey);
                                break;
                            }
                        }
                    }
                    if (!matchResult) {
                        hospitalList.add(clmsDutyDetailBillSettleDTO.getBillDate() + clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getHospitalName());
                        limitMap.put(startEndDate.getMonth(), hospitalList);
                    }
                }
            }
        }


        List<PlanPayDTO> plans = policyPayDTO.getPlanPayArr();
        for (PlanPayDTO planPayDTO : plans) {
            List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr();
            for (DutyPayDTO dutyPayDTO : dutyPayDTOS) {
                List<DutyDetailPayDTO> details = dutyPayDTO.getDutyDetailPayArr();
                for (DutyDetailPayDTO detailPayDTO : details) {
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getBillSettleResultDTOList())) {
                        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO = detailPayDTO.getDetailSettleReasonTemplateDTO();
                        BigDecimal sumAutoAmount = BigDecimal.ZERO;
                        List<EverySettleTemplateDTO> everySetttleList = detailSettleReasonTemplateDTO.getEverySetttleList();
                        Set<Date> everySettleSet = new HashSet<>();
                        Map<Date, BigDecimal> dayPayAmountMap = new HashMap<>();
                        List<BIllSettleResultDTO> billSettleResultDTOList = detailPayDTO.getBillSettleResultDTOList();
                        billSettleResultDTOList.sort(new Comparator<BIllSettleResultDTO>() {
                            @Override
                            public int compare(BIllSettleResultDTO h1, BIllSettleResultDTO h2) {
                                // 第一优先级：按医院性质和名称分组（0为优先组，1为非优先组）
                                int priority1 = ("公立".equals(h1.getHospitalPropertyDes()) &&
                                        null != h1.getHospitalName() &&
                                        !h1.getHospitalName().contains("牙") &&
                                        !h1.getHospitalName().contains("口腔")) ? 0 : 1;
                                int priority2 = ("公立".equals(h2.getHospitalPropertyDes()) &&
                                        null != h2.getHospitalName() &&
                                        !h2.getHospitalName().contains("牙") &&
                                        !h2.getHospitalName().contains("口腔")) ? 0 : 1;

                                // 先比较优先级
                                if (priority1 != priority2) {
                                    return priority1 - priority2;
                                }

                                // 优先级相同后，按金额降序（使用BigDecimal的compareTo方法）
                                return h2.getAutoSettleAmount().compareTo(h1.getAutoSettleAmount());
                            }
                        });
                        for (BIllSettleResultDTO bIllSettleResultDTO : billSettleResultDTOList) {
                            if (!"0".equals(bIllSettleResultDTO.getSettleType())) {
                                continue;
                            }
                            List<String> hospitalList = limitMap.get(bIllSettleResultDTO.getMonth());
                            if (CollectionUtils.isEmpty(hospitalList)) {
                                hospitalList = new ArrayList<>();
                                hospitalList.add(bIllSettleResultDTO.getBillDate() + bIllSettleResultDTO.getHospitalCode() + bIllSettleResultDTO.getHospitalName());
                                limitMap.put(bIllSettleResultDTO.getMonth(), hospitalList);
                                sumAutoAmount = sumAutoAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                BigDecimal dayPayAmount = dayPayAmountMap.get(bIllSettleResultDTO.getBillDate());
                                if (null == dayPayAmount) {
                                    dayPayAmount = BigDecimal.ZERO;
                                }
                                dayPayAmount = dayPayAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                dayPayAmountMap.put(bIllSettleResultDTO.getBillDate(), dayPayAmount);
                            } else {
                                boolean matchResult = false;
                                for (int i = 0; i < hospitalList.size(); i++) {
                                    String matchKey = hospitalList.get(i);
                                    if(matchKey.contains(bIllSettleResultDTO.getBillDate().toString())){
                                        if (matchKey.contains(bIllSettleResultDTO.getHospitalCode()) || matchKey.contains(bIllSettleResultDTO.getHospitalName())) {
                                            matchResult = true;
                                            String newMatchKey = matchKey + bIllSettleResultDTO.getHospitalCode() + bIllSettleResultDTO.getHospitalName();
                                            hospitalList.set(i, newMatchKey);
                                            break;
                                        }
                                    }
                                }
                                if (!matchResult) {
                                    Integer limitCountTemp = limitCount;
                                    //长鹅AB升级产品特殊处理
                                    if (specialProductPackage.contains(detailPayDTO.getProductPackage()) && 0 == bIllSettleResultDTO.getMonth()) {
                                        limitCountTemp = 2;
                                    }
                                    if (hospitalList.size() >= limitCountTemp) {
                                        updateBillSettleResult(bIllSettleResultDTO, limitCountTemp, everySettleSet);
                                    } else {
                                        hospitalList.add(bIllSettleResultDTO.getBillDate() + bIllSettleResultDTO.getHospitalCode() + bIllSettleResultDTO.getHospitalName());
                                        limitMap.put(bIllSettleResultDTO.getMonth(), hospitalList);
                                        sumAutoAmount = sumAutoAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                        BigDecimal dayPayAmount = dayPayAmountMap.get(bIllSettleResultDTO.getBillDate());
                                        if (null == dayPayAmount) {
                                            dayPayAmount = BigDecimal.ZERO;
                                        }
                                        dayPayAmount = dayPayAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                        dayPayAmountMap.put(bIllSettleResultDTO.getBillDate(), dayPayAmount);
                                    }
                                }else {
                                    sumAutoAmount = sumAutoAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                    BigDecimal dayPayAmount = dayPayAmountMap.get(bIllSettleResultDTO.getBillDate());
                                    if (null == dayPayAmount) {
                                        dayPayAmount = BigDecimal.ZERO;
                                    }
                                    dayPayAmount = dayPayAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                    dayPayAmountMap.put(bIllSettleResultDTO.getBillDate(), dayPayAmount);
                                }
                            }
                        }
                        billSettleResultDTOList = billSettleResultDTOList.stream().sorted(Comparator.comparing(BIllSettleResultDTO::getBillDate)).collect(Collectors.toList());
                        detailPayDTO.setBillSettleResultDTOList(billSettleResultDTOList);
                        // 每个责任明细的总金额
                        detailPayDTO.setAutoSettleAmount(sumAutoAmount);
                        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(sumAutoAmount));
                        if (!CollectionUtils.isEmpty(everySettleSet)) {
                            setEverySetttleListValue(everySetttleList, everySettleSet, detailPayDTO.getBillSettleResultDTOList());
                        }
                        if (!CollectionUtils.isEmpty(dayPayAmountMap)) {
                            List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList = detailPayDTO.getDutyBillLimitInfoDTOList();
                            if (CollectionUtils.isEmpty(dutyBillLimitInfoDTOList)) {
                                log.warn("月度一日一院获取发票限额列表为空，reportNo:{},dutyCode:{}", policyPayDTO.getReportNo(), dutyPayDTO.getDutyCode());
                                continue;
                            }
                            Set<Date> dateSet = dayPayAmountMap.keySet();
                            for (Date date : dateSet) {
                                BigDecimal dayPayAmount = dayPayAmountMap.get(date);
                                List<DutyBillLimitInfoDTO> collect = dutyBillLimitInfoDTOList.stream().filter(i -> date.equals(i.getBillDate())).collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(collect)) {
                                    log.warn("月度一日一院过滤发票限额列表结果为空，reportNo:{},dutyCode:{}，billDate:{}", policyPayDTO.getReportNo(), dutyPayDTO.getDutyCode(), date);
                                    continue;
                                }
                                for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : collect) {
                                    dutyBillLimitInfoDTO.setSettleClaimAmount(dayPayAmount);
                                }
                            }

                            List<DutyBillLimitInfoDTO> collect = dutyBillLimitInfoDTOList.stream().filter(i -> !dateSet.contains(i.getBillDate())).collect(Collectors.toList());
                            if(!CollectionUtils.isEmpty(collect)){
                                for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : collect) {
                                    dutyBillLimitInfoDTO.setSettleClaimAmount(BigDecimal.ZERO);
                                }
                            }
                        }
                    }
                }
            }
        }


    }


    /**
     * 更新责任明细发票数据
     */
    private void updateBillSettleResult(BIllSettleResultDTO bIllSettleResultDTO, Integer limitCount, Set<Date> everySettleSet) {
        bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
        bIllSettleResultDTO.setExceedMothPayDays("Y");
        bIllSettleResultDTO.setSettleType("3");
        bIllSettleResultDTO.setRemark("案件所在合同月累计赔付次数已达月限次数" + limitCount + "次，本次可赔付金额为0");
        log.info("报案号={},所在合同月已超月限次 该发票责任明细发票理算数据为0", bIllSettleResultDTO.getReportNo());
        everySettleSet.add(bIllSettleResultDTO.getBillDate());
    }

    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if (billDate.compareTo(monthDto.getStartDate()) >= 0 && billDate.compareTo(monthDto.getEndDate()) <= 0) {
                return monthDto;
            }
        }
        return null;
    }

    /**
     * 更新理算依据对象
     */
    private void setEverySetttleListValue(List<EverySettleTemplateDTO> everySetttleList, Set<Date> everySettleSet, List<BIllSettleResultDTO> billSettleResultDTOList) {
        if (CollectionUtil.isEmpty(everySetttleList)) {
            return;
        }
        for (Date date : everySettleSet) {
            String strbillDate = DateUtils.dateFormat(date, DateUtils.SIMPLE_DATE_STR);
            for (EverySettleTemplateDTO e : everySetttleList) {
                if (Objects.equals(e.getStrBillDate(), strbillDate)) {
                    e.setExceedMonthOrgLimit("Y");
                    BigDecimal reduce = billSettleResultDTOList.stream().filter(i -> date.equals(i.getBillDate())).map(BIllSettleResultDTO::getAutoSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    e.setExceedMonthOrgLimitAmount(BigDecimalUtils.toString(reduce));
                }
            }
        }

    }

}
