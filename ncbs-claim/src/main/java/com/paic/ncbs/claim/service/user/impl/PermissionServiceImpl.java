package com.paic.ncbs.claim.service.user.impl;

import com.github.pagehelper.PageHelper;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.user.GradeVO;
import com.paic.ncbs.claim.model.vo.user.PermissionTypeVO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import com.paic.ncbs.claim.model.vo.user.PermissionVO;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.Constants.PERMISSION_REGIST;

@Service("permissionService")
public class PermissionServiceImpl implements PermissionService {
    @Autowired
    private PermissionMapper permissionMapper;
    @Autowired
    private PermissionUserMapper permissionUserMapper;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private TaskInfoService taskInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermissionList(PermissionVO permissionVO){
        List<PermissionDTO> permissionList = permissionVO.getPermissionList();
        String typeCode = permissionVO.getTypeCode();
        String deptCode = permissionVO.getDeptCode();
        if(StringUtils.isEmptyStr(typeCode) || StringUtils.isEmptyStr(deptCode) || ListUtils.isEmptyList(permissionList)){
            throw new GlobalBusinessException(GlobalResultStatus.EX_USER_945014);
        }
        String userId = WebServletContext.getUserId();
        List<PermissionDTO> insertList = new ArrayList<>();
        List<PermissionDTO> updateList = new ArrayList<>();
        for (PermissionDTO dto : permissionList) {
            if(dto.getMinAmount()== null || dto.getMaxAmount()==null){
                throw new GlobalBusinessException(GlobalResultStatus.EX_USER_945014);
            }

            dto.setCreatedBy(userId);
            if(StringUtils.isEmptyStr(dto.getIdClmsPermission())){
                //新增
                dto.setIdClmsPermission(UuidUtil.getUUID());
                dto.setTypeCode(typeCode);
                dto.setDeptCode(deptCode);
                insertList.add(dto);
            }else{
                //更新
                updateList.add(dto);
            }
        }

        if(insertList.size() > 0){
            permissionMapper.addPermissionList(insertList);
        }

        if(updateList.size() > 0){
            for (PermissionDTO dto : updateList) {
                permissionMapper.updatePermission(dto);
            }
        }
    }

    @Override
    public PermissionVO getPermissionList(String typeCode,String deptCode){
        if(StringUtils.isEmptyStr(typeCode) || StringUtils.isEmptyStr(deptCode)){
            throw new GlobalBusinessException(GlobalResultStatus.EX_USER_945014);
        }

        List<PermissionDTO> permissionList = getPermissionListOrDefault(typeCode,deptCode);
        Map<Integer, String> gradeMap = Constants.PERMISSION_MAP.getOrDefault(typeCode,new HashMap<>());
        for (PermissionDTO dto : permissionList) {
            dto.setGradeName(gradeMap.getOrDefault(dto.getGrade(),""));
        }
        PermissionVO vo = new PermissionVO();
        vo.setTypeCode(typeCode);
        vo.setDeptCode(deptCode);
        vo.setPermissionList(permissionList);
        vo.setMaxRows(permissionList.size());
        return vo;
    }

    /**
     * 本机构没有则获取总部的配置，总部配置通过脚本初始化
     * @param typeCode
     * @param deptCode
     * @return
     */
    private List<PermissionDTO> getPermissionListOrDefault(String typeCode,String deptCode){
        PermissionDTO param = new PermissionDTO(deptCode,typeCode);
        List<PermissionDTO> permissionList = permissionMapper.getPermissionList(param);
        if(ListUtils.isEmptyList(permissionList)){
            //照抄一份总部的等级，机构替换为本机构，id和金额置空
            param.setDeptCode(ConfigConstValues.HQ_DEPARTMENT);
            permissionList = permissionMapper.getPermissionList(param);
            for (PermissionDTO dto : permissionList) {
                dto.setDeptCode(deptCode);
                dto.setIdClmsPermission(null);
                dto.setMinAmount(null);
                dto.setMaxAmount(null);
            }
        }
        return permissionList;
    }

    @Override
    public Integer getPermissionGrade(String typeCode, String deptCode, BigDecimal amount) {
        if(amount == null || BigDecimal.ZERO.compareTo(amount) == 0){
            return 1;
        }
        return permissionMapper.getPermissionGrade(typeCode, deptCode, amount);
    }

    @Override
    public PermissionUserVO getPermissionUserInfo(String userId) {

        UserInfoDTO user = null;
        try {
            user = Optional.ofNullable(cacheService.queryUserInfo(userId)).orElse(new UserInfoDTO());
        } catch (Exception e) {
            throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"请核实该用户账号是否已在用户中心配置");
        }
        if(StringUtils.isEmptyStr(user.getUserCode())){
            throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"请核实该用户账号是否已在用户中心配置");
        }
        PermissionUserVO vo = new PermissionUserVO();
        vo.setUserId(user.getUserCode());
        vo.setUserName(user.getUserName());
        try {
            List<SystemComInfoDTO> sysComList = cacheService.queryUserSystemComList(vo.getUserId());
            if(ListUtils.isNotEmpty(sysComList)){
                String currentDeptCode = WebServletContext.getDepartmentCode();
                vo.setDepartmentList(departmentDefineMapper.filterLevelDeptCode(currentDeptCode,sysComList));
            }
        } catch (Exception e) {
            LogUtil.error("查询用户权限机构失败",e);
            throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"请核实该用户账号是否已配置权限机构");
        }
        return vo;
    }

    @Override
    public List<PermissionTypeVO> getRoleTypeList(String userId, String comCode) {
        List<PermissionTypeVO> typeList = new ArrayList<>();
        try {
            List<UserGradeInfoDTO> userGradeList = cacheService.queryUserGradeList(userId, comCode);
            if(ListUtils.isEmptyList(userGradeList)){
                LogUtil.audit("岗位为空");
            }
            Set<String> gradeNames = userGradeList.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toSet());
            for (String name : gradeNames) {
                if(NcbsConstant.SETTLE_REVIEW_GRADE_NAME.equals(name) || NcbsConstant.SETTLE_REVIEW_GRADE_NAME_NEW.equals(name)
                        || NcbsConstant.CLAIM_MANAGER_GRADE_NAME_NEW.equals(name) || NcbsConstant.TPA_MANAGER_GRADE_NAME_NEW.equals(name) ){
                    typeList.add(Constants.PERMISSION_ROLE_LIST.get(0));
                }
                if(NcbsConstant.REGISTER_REVIEW_GRADE_NAME.equals(name) || NcbsConstant.SETTLE_REVIEW_GRADE_NAME_NEW.equals(name)
                        || NcbsConstant.CLAIM_MANAGER_GRADE_NAME_NEW.equals(name) || NcbsConstant.TPA_MANAGER_GRADE_NAME_NEW.equals(name)
                        || NcbsConstant.TPA_OPERATOR_GRADE_NAME_NEW.equals(name) || NcbsConstant.CLAIM_OPERATOR_GRADE_NAME_NEW.equals(name)){
                    typeList.add(Constants.PERMISSION_ROLE_LIST.get(1));
                }
                if(typeList.size() == 2){
                    break;
                }
            }
        } catch (Exception e) {
            LogUtil.error("获取用户岗位失败",e);
        }

        return typeList;
    }

    @Override
    public void addPermissionUser(PermissionUserVO permissionUserVO) {
        String typeCode = permissionUserVO.getTypeCode();
        String userId = permissionUserVO.getUserId();
        Integer grade = permissionUserVO.getGrade();
        String comCode = permissionUserVO.getComCode();
        if(StringUtils.isEmptyStr(typeCode) || StringUtils.isEmptyStr(userId) || StringUtils.isEmptyStr(comCode) || null == grade){
            LogUtil.audit("参数不能为空");
            throw new GlobalBusinessException(GlobalResultStatus.EX_USER_945014);
        }

        if(permissionUserMapper.getUserGradeCount(typeCode,comCode,userId) > 0){
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"已配置相同权限，请确认");
        }

        String currUser = WebServletContext.getUserId();
        PermissionUserDTO permissionUserDTO = new PermissionUserDTO();
        permissionUserDTO.setCreatedBy(currUser);
        permissionUserDTO.setIdClmsPermissionUser(UuidUtil.getUUID());
        permissionUserDTO.setTypeCode(typeCode);
        permissionUserDTO.setComCode(comCode);
        permissionUserDTO.setGrade(grade);
        permissionUserDTO.setUserId(userId);
        permissionUserDTO.setUserName(permissionUserVO.getUserName());
        permissionUserMapper.addPermissionUser(permissionUserDTO);

    }

    @Override
    public List<GradeVO> getUserGradeList(String typeCode) {
        List<GradeVO> gradeList = new ArrayList<>();
        PermissionDTO param = new PermissionDTO(ConfigConstValues.HQ_DEPARTMENT,typeCode);
        List<PermissionDTO> permissionList = permissionMapper.getPermissionList(param);
        if(ListUtils.isNotEmpty(permissionList)){
            Map<Integer, String> gradeMap = Constants.PERMISSION_MAP.getOrDefault(typeCode,new HashMap<>());
            for (PermissionDTO permission : permissionList) {
                GradeVO grade = new GradeVO(permission.getGrade(),gradeMap.getOrDefault(permission.getGrade(),""),permission.getMaxAmount());
                // 三星新增时候剔除立案一级
                //if (!(PERMISSION_REGIST.equals(permission.getTypeCode()) && 1 == grade.getGrade())) {
                    gradeList.add(grade);
               // }
            }
        }
        return gradeList;
    }

    @Override
    public List<SystemComInfoDTO> getPermissionSystemCom(String userId) {
        try {
            List<SystemComInfoDTO> comList = cacheService.queryUserSystemComList(userId);
            if(ListUtils.isEmptyList(comList)){
                return new ArrayList<>();
            }
            if(comList.size() == 1){
                return comList;
            }

            String currentDepartmentCode = Optional.ofNullable(WebServletContext.getDepartmentCode()).orElse("");
            //当前登录的权限机构放在首位
            List<SystemComInfoDTO> resultList = new ArrayList<>();
            for (SystemComInfoDTO dto : comList) {
                if(currentDepartmentCode.equals(dto.getComCode())){
                    resultList.add(dto);
                    comList.remove(dto);
                    break;
                }
            }
            resultList.addAll(comList);
            return resultList;

        } catch (Exception e) {
            LogUtil.error("查询用户权限机构失败",e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<PermissionUserDTO> getPermissionUserList(PermissionUserVO vo) {
        String userId = vo.getUserId();
        if(StringUtils.isNotEmpty(userId)){
            vo.setUserId(userId);
        }
        if(ConstValues.YES.equals(vo.getContains())){
            //包含下级
            if(!ConfigConstValues.HQ_DEPARTMENT.equals(vo.getComCode())){
                List<String> allChildCodeList= new ArrayList<>();
                allChildCodeList.add(vo.getComCode());
                List<String> parentCodeList = new ArrayList<>();
                parentCodeList.add(vo.getComCode());
                for (int i=2;i<6;i++){
                    //查询子机构代码， 从2级机构开始最多查询到6级机构
                    List<String> childCodeList = departmentDefineMapper.getChildCodeList(parentCodeList);
                    if(ListUtils.isEmptyList(childCodeList)){
                        break;
                    }else{
                        allChildCodeList.addAll(childCodeList);
                        parentCodeList = childCodeList;
                    }
                }
                vo.setQueryDeptCodeList(allChildCodeList);
            }
            vo.setComCode(null);
        }
        Pager pager = vo.getPager();
        PageHelper.startPage(pager.getPageIndex(),pager.getPageRows(),true);
        List<PermissionUserDTO> userOwnGradeList = permissionUserMapper.getPermissionUserList(vo);

        if(ListUtils.isNotEmpty(userOwnGradeList)){
            for (PermissionUserDTO dto : userOwnGradeList) {
                dto.setTypeName(Constants.PERMISSION_TYPE_MAP.getOrDefault(dto.getTypeCode(),""));
                PermissionDTO param = new PermissionDTO(ConfigConstValues.HQ_DEPARTMENT,dto.getTypeCode());
                List<PermissionDTO> permissionList = permissionMapper.getPermissionList(param);
                List<GradeVO> gradeList = new ArrayList<>();
                if(ListUtils.isNotEmpty(permissionList)){
                    Map<Integer, String> gradeMap = Constants.PERMISSION_MAP.getOrDefault(dto.getTypeCode(),new HashMap<>());
                    for (PermissionDTO permission : permissionList) {
                        GradeVO grade = new GradeVO(permission.getGrade(),gradeMap.getOrDefault(permission.getGrade(),""),permission.getMaxAmount());
                        // 三星新增时候剔除立案一级
                        //if (!(PERMISSION_REGIST.equals(permission.getTypeCode()) && 1 == grade.getGrade())) {
                            gradeList.add(grade);
                       // }
                    }
                }
                dto.setGradeList(gradeList);
            }
        }

        return userOwnGradeList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePermissionUserList(List<PermissionUserDTO> permissionUserList) {
        String userId = WebServletContext.getUserId();
        for (PermissionUserDTO dto : permissionUserList) {
            if(StringUtils.isEmptyStr(dto.getIdClmsPermissionUser()) || dto.getGrade() == null){
                throw new GlobalBusinessException(GlobalResultStatus.PARAM_ERROR);
            }
            if(permissionUserMapper.getSamePermissionUserCount(dto.getGrade(), dto.getIdClmsPermissionUser()) > 0){
                String msg = "["+dto.getUserName()+","+dto.getTypeName()+","+dto.getDeptName()+"]已有相同权限不能修改";
                throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),msg);
            }
            dto.setUpdatedBy(userId);
            permissionUserMapper.updatePermissionUser(dto);
        }

    }

    @Override
    public void removePermissionUser(String idClmsPermissionUser) {
        permissionUserMapper.removePermissionUser(idClmsPermissionUser);
    }

    @Override
    public List<String> getUserList(String typeCode,String comCode, Integer grade) {
        return permissionUserMapper.getUserList(typeCode,comCode,grade);
    }

    @Override
    public Integer getUserGrade(String typeCode,String comCode,String userId) {
        return permissionUserMapper.getUserGrade(typeCode,comCode, userId);
    }

    @Override
    public PermissionUserDTO getLatestGrade(String typeCode, String comCode,Integer grade) {
        //先查询本机构是否有>=grade的等级
        Integer localGrade = permissionUserMapper.getGradeByComCode(typeCode, comCode, grade);
        PermissionUserDTO permissionUser = new PermissionUserDTO();
        permissionUser.setComCode(ConfigConstValues.HQ_DEPARTMENT);
        if(localGrade != null){
            //本机构有配置则返回
            LogUtil.audit("本机构{}配置等级={}",comCode,localGrade);
            permissionUser.setComCode(comCode);
            permissionUser.setGrade(localGrade);
            return permissionUser;
        }

        if(ConfigConstValues.HQ_DEPARTMENT.equals(comCode)){
            //已经是总部就不往上找了
            LogUtil.audit("已是总部不再找上级机构");
            return permissionUser;
        }

        String queryComCode = comCode;
        for (int i=0;i<7;i++){
            //找上级机构>=grade的等级，直至总部,最多循环7次，避免死循环。
            String upperDepartment = permissionUserMapper.getParentComCode(queryComCode);
            Integer upperGrade = permissionUserMapper.getGradeByComCode(typeCode,upperDepartment, grade);
            if(upperGrade != null){
                permissionUser.setComCode(upperDepartment);
                permissionUser.setGrade(upperGrade);
                LogUtil.audit("追溯到{}有大于等于{}级的权限",upperDepartment,grade);
                return permissionUser;
            }
            LogUtil.audit("上级机构={}",upperDepartment);
            if(ConfigConstValues.HQ_DEPARTMENT.equals(upperDepartment)){
                LogUtil.audit("追溯到总部都没配置大于等于{}级的权限",grade);
                return permissionUser;
            }
            queryComCode = upperDepartment;
        }

        LogUtil.audit("所有机构都没配置大于等于{}级的权限",grade);
        return permissionUser;
    }

    /**
     * 获取岗位权限
     * @param userId
     * @param comCode
     * @return
     */
    @Override
    public List<PermissionTypeVO> getManageTypeList(String userId, String comCode) {
        List<PermissionTypeVO> typeList = new ArrayList<>();
        try {
            List<UserGradeInfoDTO> userGradeList = cacheService.queryUserGradeList(userId, comCode);
            if(ListUtils.isEmptyList(userGradeList)){
                LogUtil.audit("岗位为空");
            }
            PermissionTypeVO permissionTypeVO = new PermissionTypeVO(BaseConstant.STRING_0,"管理看板");
            Set<String> gradeNames = userGradeList.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toSet());
            for (String name : gradeNames) {
                if(NcbsConstant.SETTLE_REVIEW_GRADE_NAME_NEW.equals(name)
                        || NcbsConstant.CLAIM_MANAGER_GRADE_NAME_NEW.equals(name) || NcbsConstant.CLAIM_OPERATOR_GRADE_NAME_NEW.equals(name)){
                    permissionTypeVO.setTypeCode(BaseConstant.STRING_1);
                    break;
                }
            }
            typeList.add(permissionTypeVO);
        } catch (Exception e) {
            LogUtil.error("获取用户岗位失败",e);
        }

        return typeList;
    }

    @Override
    public List<PermissionUserDTO> getVerifyUserList(String reportNo,Integer caseTimes,String taskId) {
        if(StringUtils.isEmptyStr(taskId)
                || StringUtils.isEmptyStr(reportNo)
                || caseTimes == null){
            LogUtil.audit("校验权限参数错误！reportNo:{},caseTimes:{},taskId:{}",reportNo,caseTimes,taskId);
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        LogUtil.audit("当前任务id={}",taskId);
        TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
        String taskDefinitionBpmKey = "";
        if(taskDto == null){
            taskDefinitionBpmKey = taskId;
        } else {
            taskDefinitionBpmKey = taskDto.getTaskDefinitionBpmKey();
        }
        String userId = WebServletContext.getUserId();
        String departmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
        String typeCode = null;
        if(BpmConstants.OC_SETTLE_REVIEW.equals(taskDefinitionBpmKey)){
            typeCode = Constants.PERMISSION_VERIFY;
        } else {
            typeCode = Constants.PERMISSION_REGIST;
        }
        List<String> list = new ArrayList<>();
        if("1".equals(departmentCode)){
            list.add(departmentCode);
        } else {
            list.add(departmentCode);
            list.add("1");
        }
        return permissionUserMapper.getVerifyUserList(userId,list,typeCode);
    }

    @Override
    public PermissionUserVO getUserPermission(PermissionUserDTO permissionUserDTO) {
        PermissionUserVO vo = permissionUserMapper.getUserPermission(permissionUserDTO);
        if(vo == null){
            vo = new PermissionUserVO();
            vo.setMaxAmount(BigDecimal.ZERO);
        }
        return vo;
    }
}
