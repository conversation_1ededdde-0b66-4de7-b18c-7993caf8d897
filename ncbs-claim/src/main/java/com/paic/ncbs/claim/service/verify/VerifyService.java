package com.paic.ncbs.claim.service.verify;

import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyTaskDTO;
import com.paic.ncbs.claim.model.vo.settle.VerifyInfoVO;

import java.util.List;

public interface VerifyService {

	void addVerify(VerifyInfoVO verifyInfoVO, List<String> msg) throws Exception;

	void autoVerify(String reportNo, Integer caseTimes) throws Exception;

	void insertVerify(VerifyDTO verify);

    void splitPlanAndDuty(List<PolicyPayDTO> policyPayList, List<PaymentItemDTO> allItems);

    void splitPlanAndDuty4Fee(List<EstimatePolicyDTO> estimatePolicyDTOS, List<PaymentItemDTO> allItems);

    VerifyDTO getVerify(String reportNo, Integer caseTimes);

	List<VerifyDTO> getVerifyList(String reportNo,Integer caseTimes);

	void updateVerify(VerifyDTO verify);

	void checkVerifyUserGrade(String taskId, List<String> msg);

	VerifyTaskDTO queryVerifyTask(String reportNo, Integer caseTimes, String taskId);
}
