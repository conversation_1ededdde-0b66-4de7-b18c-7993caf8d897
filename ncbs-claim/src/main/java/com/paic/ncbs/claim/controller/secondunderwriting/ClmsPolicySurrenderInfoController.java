package com.paic.ncbs.claim.controller.secondunderwriting;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicyCalculateAgreeDTO;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicySurrenderInfoDTO;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicySurrenderInfoVO;
import com.paic.ncbs.claim.model.vo.senconduw.SurrenderCheckVO;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 理赔保单解约信息表(ClmsPolicySurrenderInfo)表控制层
 *
 * <AUTHOR>
 * @since 2023-11-03 14:49:00
 */
@RestController
@RequestMapping("/surrender")
public class ClmsPolicySurrenderInfoController {
    /**
     * 服务对象
     */
    @Resource
    private ClmsPolicySurrenderInfoService clmsPolicySurrenderInfoService;


    /**
     * 通过报案号，赔付次数查询保单解约信息
     *
     * @param reportNo caseTimes主键
     * @return 单条数据
     */
    @GetMapping("/getSurrenderInfo/{reportNo}/{caseTimes}")
    public ResponseResult<ClmsPolicySurrenderInfoDTO> getSurrenderInfo(@PathVariable("reportNo") String reportNo,
                                                                       @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(this.clmsPolicySurrenderInfoService.getSurrenderInfo(reportNo,null));
    }

    /**
     * 新增数据
     *
     * @param dto 实体
     * @return 新增结果
     */
    @PostMapping("/add")
    public ResponseResult<Object> add(@RequestBody ClmsPolicySurrenderInfoDTO dto) {
        this.clmsPolicySurrenderInfoService.saveData(dto);
        return ResponseResult.success();
    }


    /**
     * 解约按钮的飘红显示查询
     *
     * @param
     * @return 解约次数
     */
    @GetMapping("/getCount/{reportNo}/{caseTimes}")
    public ResponseResult<Integer> getSurrenderCount(@PathVariable("reportNo") String reportNo,
                                            @PathVariable("caseTimes") Integer caseTimes) {
        Integer uwCount = clmsPolicySurrenderInfoService.getSurrenderCount(reportNo, null);
        return ResponseResult.success(uwCount);
    }

    /**
     * 理算、拒赔、零注的提交判断是否有解约，有的话做二次弹框提示
     *
     * @param
     * @return 解约次数
     */
    @GetMapping("/checkIsSurrender/{reportNo}/{caseTimes}")
    public ResponseResult<SurrenderCheckVO> checkIsSurrender(@PathVariable("reportNo") String reportNo,
                                                             @PathVariable("caseTimes") Integer caseTimes) {
        SurrenderCheckVO surrenderCheckVO = clmsPolicySurrenderInfoService.checkIsSurrender(reportNo, caseTimes);
        return ResponseResult.success(surrenderCheckVO);
    }

    /**
     * 解约保费试算
     *
     * @param policyNo 保单号
     *        surrenderDate 解约日期
     * @return 解约保费试算
     */
    @GetMapping("/calculateAgree")
    public ResponseResult<ClmsPolicyCalculateAgreeDTO> calculateAgree(@RequestParam(value = "policyNo") String policyNo,
                                                                      @RequestParam(value = "surrenderDate") String surrenderDate) {
        ClmsPolicyCalculateAgreeDTO calculateAgree = clmsPolicySurrenderInfoService.calculateAgree(policyNo, surrenderDate);
        return ResponseResult.success(calculateAgree);
    }

    /**
     * 理赔在途
     * @param policyNo
     * @param reportNo
     * @return
     */
    @GetMapping("/getClaimingInfo/{policyNo}/{reportNo}")
    public ResponseResult<String> getClaimingInfo(@PathVariable(value = "policyNo") String policyNo, @PathVariable(value = "reportNo") String reportNo){
        String resutlt = clmsPolicySurrenderInfoService.getClaimingInfo(policyNo,reportNo);
        return ResponseResult.success(resutlt);
    }


}

