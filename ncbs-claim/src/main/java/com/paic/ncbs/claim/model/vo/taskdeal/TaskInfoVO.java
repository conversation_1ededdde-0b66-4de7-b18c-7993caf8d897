package com.paic.ncbs.claim.model.vo.taskdeal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.convert.BeanConvert;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;


import java.util.Date;
import java.util.List;

@Data
@ApiModel("任务信息")
public class TaskInfoVO extends EntityDTO {

    private static final long serialVersionUID = 319169589087892850L;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("任务节点")
    private String taskDefinitionBpmKey;

    @ApiModelProperty("任务实例id")
    private String taskId;

    @ApiModelProperty("任务分类（任务名称）")
    private String taskName;

    @ApiModelProperty("任务状态 0：待处理 1：已处理 3：挂起")
    private String status;

    @ApiModelProperty("案件处理机构编码")
    private String departmentCode;

    @ApiModelProperty("案件处理机构名称")
    private String departmentName;

    @ApiModelProperty("案件所属机构编码（保单所属机构）")
    private String policyDepartmentCode;

    @ApiModelProperty("案件所属机构编码（保单所属机构）")
    private String policyDepartmentName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("派工时间-当前任务分派到人的时间")
    private Date assignTime;

    @ApiModelProperty("处理人id")
    private String assignee;

    @ApiModelProperty("处理人姓名")
    private String assigneeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("开始处理时间-当前任务生成时间")
    private Date startDealTime;

    @ApiModelProperty("花费时常")
    private String costTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("任务完成时间 -")
    private Date completeTime;

    @ApiModelProperty("保单类型")
    private String policyType;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "上一节点提交人姓名")
    private String applyerName;
    @ApiModelProperty(value = "上一节点提交人")
    private String applyer;

    @ApiModelProperty(value = "产品代码")
    private String productCode;

    @ApiModelProperty(value = "方案名称")
    private String packageName;

    @ApiModelProperty(value = "快赔")
    private String isQuickPay;

    @ApiModelProperty("任务id列表")
    private List<String> taskIdList;

    @ApiModelProperty("限制保单处理标识：Y-限制处理，N-不限制处理")
    private String limitPolicyDealFlag;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("订单号-查询调查审批时间用")
    private String orderNo;

    @ApiModelProperty("子任务流程互斥标签")
    private String subProcessFlag;

    //跟踪人员
    private String tracePerson;

    //任务主键
    private String idAhcsTaskInfo;

    public TaskInfoDTO convertToDTO(){
        TaskInfoVOConvert convert = new TaskInfoVOConvert();
        return convert.convert(this);
    }

    private static class TaskInfoVOConvert implements BeanConvert<TaskInfoVO, TaskInfoDTO>{

        @Override
        public TaskInfoDTO convert(TaskInfoVO taskInfoVO) {
            TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
            BeanUtils.copyProperties(taskInfoVO,taskInfoDTO);
            taskInfoDTO.setAssigner(taskInfoVO.getAssignee());
            return taskInfoDTO;
        }
    }

}
