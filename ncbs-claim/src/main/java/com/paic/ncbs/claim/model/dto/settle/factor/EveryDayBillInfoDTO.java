package com.paic.ncbs.claim.model.dto.settle.factor;

import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *每日发票信息
 */
@Data
public class EveryDayBillInfoDTO {
    /**
     * 发票日期
     */
    private Date billDate;
    /**
     * 经医保结算的标志：Y-是  N-否
     */
    private String medicalSettleFlag;
    /**
     * 等待期发票标志：Y-是 N-否
     */
    private String waitFlag;
    /**
     * 发票在保单有效期标记 Y-是 ，N-否
     */
    private String effectiveFlag;
    /**
     * 发票日期所在合同月
     */
    private Integer month;
    /**
     * 保单合同月
     */
    private PolicyMonthDto policyMonthDto;
    /**
     * 序号：作用 再计算合理费用时 根据是否经医保结算做出优先级标记，未经医保的序号为1，经医保的值为2
     * 这个字段再做免赔额抵扣的时候要用，优先抵扣未经医保结算的 所以此处做个优先级
     */
    private Integer serialNo;

    /**
     * 超每月赔付天数标识 ：Y-是
     */
    private String exceedMothPayDays;
    /**
     * 超年度赔付天数标识 ：Y-是
     */
    private String exceedYearlyPayDays;
    /**
     * 账单金额
     */
    private BigDecimal billAmount;
    /**
     * 自费金额
     */
    private BigDecimal deductibleAmount;
    /**
     * 第三方支付金额
     */
    private BigDecimal prepaidAmount;
    /**
     * 不合理金额
     */
    private BigDecimal immoderateAmount;
    /**
     * 部分自费金额
     */
    private BigDecimal partialDeductible;
    /**
     * 发票号
     */
    private String billNo;
    /**
     * id
     */
    private String id;
    /**
     * 就诊类型
     */
    private String therapyType;
    /**
     * 治疗医院
     */
    private String hospitalCode;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 剩余日免赔
     */
    private BigDecimal remaindDayDeductible;

    /**
     * 医院性质
     */
    private String hospitalPropertyDes;


}
