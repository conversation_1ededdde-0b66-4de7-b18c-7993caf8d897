package com.paic.ncbs.claim.dao.mapper.pet;

import com.paic.ncbs.claim.model.dto.pet.PetInjureExDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PetInjureExMapper {

    void addPetInjureExList(@Param("petInjureExList") List<PetInjureExDTO> petInjureExList);

    List<PetInjureExDTO> getPetInjureExList(@Param("idClmsPetInjure")String idClmsPetInjure);

    void delPetInjureExList(@Param("idClmsPetInjure")String idClmsPetInjure,@Param("updatedBy")String updatedBy);

}