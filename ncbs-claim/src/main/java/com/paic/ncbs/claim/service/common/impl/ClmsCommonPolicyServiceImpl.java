package com.paic.ncbs.claim.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.service.common.ClmsCommonPolicyService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ClmsCommonPolicyServiceImpl implements ClmsCommonPolicyService {
    @Autowired
    private PolicyPayMapper policyPayDao;

    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;

    @Autowired
    private PlanPayMapper planPayDao;

    @Autowired
    private DutyPayMapper dutyPayDao;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayDao;

    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;
    @Autowired
    private ClmsDutyDetailBillSettleService clmsDutyDetailBillSettleService;

    /**
     * 更新保单、险种、责任、责任明细信息
     */
    @Override
    @Transactional
    public void updatePolicyPays(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }

        String userId = WebServletContext.getUserId();
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
        List<DutyDetailPayDTO> detailPayArr = new ArrayList<>();

        if (!CollectionUtils.isEmpty(policyPays)) {
            for (PolicyPayDTO policyPay : policyPays) {
                planPayArr.addAll(policyPay.getPlanPayArr());
                policyPay.setUpdatedBy(userId);
                policyPayDao.updatePolicyPayInfoList(policyPay);
            }
        }
        if (!CollectionUtils.isEmpty(planPayArr)) {
            for (PlanPayDTO planPayDTO : planPayArr) {
                dutyPayArr.addAll(planPayDTO.getDutyPayArr());
                planPayDTO.setUpdatedBy(userId);
                planPayDao.updatePlanPayInfoList(planPayDTO);

            }
        }
        if (!CollectionUtils.isEmpty(dutyPayArr)) {
            for (DutyPayDTO dutyPayDTO : dutyPayArr) {
                detailPayArr.addAll(dutyPayDTO.getDutyDetailPayArr());
                dutyPayDTO.setUpdatedBy(userId);
                dutyPayDao.updateDutyPayInfoList(dutyPayDTO);
            }
        }
        if (!CollectionUtils.isEmpty(detailPayArr)) {
            for (DutyDetailPayDTO dutyDetailPayDTO : detailPayArr) {
                dutyDetailPayDTO.setUpdatedBy(userId);
                dutyDetailPayDao.updateDutyDetailPayList(dutyDetailPayDTO);
            }
        }
        saveDutyLimitData(policyPays);
    }

    /**
     * 更新保单、险种、责任、责任明细信息for后台
     */
    @Override
    @Transactional
    public void updatePolicyPaysForAdmin(List<PolicyPayDTO> policyPays, String reportNo, Integer caseTimes, String settleAutoSubmit) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }

        String userId = WebServletContext.getUserId();
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
        List<DutyDetailPayDTO> detailPayArr = new ArrayList<>();

        if (!CollectionUtils.isEmpty(policyPays)) {
            for (PolicyPayDTO policyPay : policyPays) {
                planPayArr.addAll(policyPay.getPlanPayArr());
                policyPay.setUpdatedBy(userId);
                policyPayDao.updatePolicyPayInfoList(policyPay);
            }
        }
        if (!CollectionUtils.isEmpty(planPayArr)) {
            for (PlanPayDTO planPayDTO : planPayArr) {
                dutyPayArr.addAll(planPayDTO.getDutyPayArr());
                planPayDTO.setUpdatedBy(userId);
                planPayDao.updatePlanPayInfoList(planPayDTO);

            }
        }
        if (!CollectionUtils.isEmpty(dutyPayArr)) {
            for (DutyPayDTO dutyPayDTO : dutyPayArr) {
                detailPayArr.addAll(dutyPayDTO.getDutyDetailPayArr());
                dutyPayDTO.setUpdatedBy(userId);
                if(Constants.YES_FLAG.equals(settleAutoSubmit)){
                    dutyPayDao.updateDutyPayInfoListByAuto(dutyPayDTO);
                }else {
                    dutyPayDao.updateDutyPayInfoList(dutyPayDTO);
                }
            }
        }
        if (!CollectionUtils.isEmpty(detailPayArr)) {
            for (DutyDetailPayDTO dutyDetailPayDTO : detailPayArr) {
                dutyDetailPayDTO.setUpdatedBy(userId);
                // 更新clms_duty_detail_pay 责任明细表数据
                dutyDetailPayDao.updateDutyDetailPayList(dutyDetailPayDTO);
            }
        }
        List<ClmsDutyDetailBillSettleDTO> saveDetailBillSettleList = new ArrayList<>();
        for (PolicyPayDTO dto :policyPays) {
            List<DutyBillLimitInfoDTO> lists =new ArrayList<>();
            for (PlanPayDTO plan :dto.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    for (DutyDetailPayDTO detail :duty.getDutyDetailPayArr()) {
                        if(CollectionUtils.isNotEmpty(detail.getDutyBillLimitInfoDTOList())){
                            lists.addAll(detail.getDutyBillLimitInfoDTOList());
                        }
                        //责任明细发票理算信息
                        if (CollectionUtils.isNotEmpty(detail.getDetailBillSettleList())){
                            List<ClmsDutyDetailBillSettleDTO> detailBillSettleList = detail.getDetailBillSettleList();
                            LogUtil.info("人工理算提交更新责任明细发票信息报案号={},更新数据为={}",detail.getReportNo(), JsonUtils.toJsonString(detailBillSettleList));
                            saveDetailBillSettleList.addAll(detailBillSettleList);
                            //更新日限额数据
                            updateDutyLimitData(detailBillSettleList);
                        }
                    }
                }
            }
            //处理日限额
            if(CollectionUtils.isNotEmpty(lists)){
                dutyBillLimitInfoService.saveList(lists);
            }
        }
        if(CollectionUtils.isNotEmpty(saveDetailBillSettleList)) {
            clmsDutyDetailBillSettleMapper.deleteByReportNo(reportNo, caseTimes);
            saveDetailBillSettleList.stream().forEach(clmsDutyDetailBillSettleDTO -> {
                clmsDutyDetailBillSettleDTO.setId(UuidUtil.getUUID());
                clmsDutyDetailBillSettleDTO.setApprovalStatus("0");
                clmsDutyDetailBillSettleDTO.setIsDeleted("0");
                if(Objects.isNull(clmsDutyDetailBillSettleDTO.getRemitAmount())){
                    clmsDutyDetailBillSettleDTO.setRemitAmount(BigDecimal.ZERO);
                }
                if(Objects.isNull(clmsDutyDetailBillSettleDTO.getReasonableAmount())){
                    clmsDutyDetailBillSettleDTO.setReasonableAmount(BigDecimal.ZERO);
                }
                clmsDutyDetailBillSettleDTO.setCreatedBy(StringUtils.isEmptyStr(WebServletContext.getUserId()) ? "system" : WebServletContext.getUserId());
                clmsDutyDetailBillSettleDTO.setUpdatedBy(StringUtils.isEmptyStr(WebServletContext.getUserId()) ? "system" : WebServletContext.getUserId());
            });
            clmsDutyDetailBillSettleMapper.batchSaveData(saveDetailBillSettleList);
        }
    }

    private void saveDutyLimitData(List<PolicyPayDTO> policyPays) {
        for (PolicyPayDTO dto :policyPays) {
            List<DutyBillLimitInfoDTO> lists =new ArrayList<>();
            for (PlanPayDTO plan :dto.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    for (DutyDetailPayDTO detail :duty.getDutyDetailPayArr()) {
                        if(CollectionUtils.isNotEmpty(detail.getDutyBillLimitInfoDTOList())){
                            lists.addAll(detail.getDutyBillLimitInfoDTOList());
                        }
                        //责任明细发票理算信息
                        if (CollectionUtils.isNotEmpty(detail.getDetailBillSettleList())){
                            LogUtil.info("人工理算提交更新责任明细发票信息报案号={},更新数据为={}",detail.getReportNo(), JsonUtils.toJsonString(detail.getDetailBillSettleList()));
                            clmsDutyDetailBillSettleService.updateListById(detail.getDetailBillSettleList());
                            //更新日限额数据
                            updateDutyLimitData(detail.getDetailBillSettleList());
                        }
                    }
                }
            }
            //处理日限额
            if(CollectionUtils.isNotEmpty(lists)){
                dutyBillLimitInfoService.saveList(lists);
            }
        }

    }

    private void updateDutyLimitData(List<ClmsDutyDetailBillSettleDTO> detailBillSettleList) {
        DutyBillLimitInfoDTO dto =new DutyBillLimitInfoDTO();
        dto.setReportNo(detailBillSettleList.get(0).getReportNo());
        dto.setCaseTimes(detailBillSettleList.get(0).getCaseTimes());
        dto.setPolicyNo(detailBillSettleList.get(0).getPolicyNo());
        List<DutyBillLimitInfoDTO> dutyLimitdtoList = dutyBillLimitInfoService.getDutyLimitData(dto);
        if(CollectionUtils.isEmpty(dutyLimitdtoList)){
            return;
        }
        List<DutyBillLimitInfoDTO> updateList=new ArrayList<>();
        Map<Date,List<ClmsDutyDetailBillSettleDTO>> billdateMap= detailBillSettleList.stream().collect(Collectors.groupingBy(ClmsDutyDetailBillSettleDTO::getBillDate));
        for (Map.Entry<Date,List<ClmsDutyDetailBillSettleDTO>> entry :billdateMap.entrySet()) {
            Date billDate =entry.getKey();
            List<ClmsDutyDetailBillSettleDTO> billList= entry.getValue();
            BigDecimal settleAmount=getBillDateSettleAmount(billList);
            //责任明细下一个日期只会有一条数据
            List<DutyBillLimitInfoDTO> updateDutLimit =  dutyLimitdtoList.stream().filter(dutyBillLimitInfoDTO -> Objects.equals(dutyBillLimitInfoDTO.getPlanCode(),billList.get(0).getPlanCode()) && Objects.equals(dutyBillLimitInfoDTO.getDutyCode(),billList.get(0).getDutyCode()) && Objects.equals(dutyBillLimitInfoDTO.getDutyDetailCode(),billList.get(0).getDutyDetailCode()) && Objects.equals(dutyBillLimitInfoDTO.getBillDate(),billDate)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(updateDutLimit)){
               continue;
            }
            DutyBillLimitInfoDTO updateDto =updateDutLimit.get(0);
            updateDto.setSettleClaimAmount(settleAmount);
            updateDto.setUpdatedDate(new Date());
            updateList.add(updateDto);
        }
        if(CollectionUtils.isNotEmpty(updateList)){
            LogUtil.info("人工理算提交更新日限额报案号={},更新数据={}",detailBillSettleList.get(0).getReportNo(),JsonUtils.toJsonString(updateList));
            dutyBillLimitInfoService.updateDutyBillLimitAmount(updateList);
        }
        
    }

    private BigDecimal getBillDateSettleAmount(List<ClmsDutyDetailBillSettleDTO> billList) {
        BigDecimal sum=BigDecimal.ZERO;
        for (ClmsDutyDetailBillSettleDTO dto :billList) {
            BigDecimal amount = BigDecimal.ZERO;
            if(Objects.nonNull(dto.getSettleAmount())){
                amount = dto.getSettleAmount();
            }else if(Objects.nonNull(dto.getAutoSettleAmount())){
                amount = dto.getAutoSettleAmount();
            }
            sum = sum.add(amount);
        }
        return sum;
    }
}
