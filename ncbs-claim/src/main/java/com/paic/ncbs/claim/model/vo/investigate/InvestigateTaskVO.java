package com.paic.ncbs.claim.model.vo.investigate;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "调查信息")
public class InvestigateTaskVO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "调查任务协助信息")
    private List<InvestigateAssistVO> investigateAssistVOs;

    @ApiModelProperty(value = "审核记录信息")
    private List<InvestigateTaskAuditVO> investigateTaskAuditVOs;
    
    @ApiModelProperty(value = "调查经过信息")
    private List<InvestigateProcessVO> investigateProcessVOs;
    
    @ApiModelProperty(value = "调查审批信息")
    private InvestigateAuditVO investigateAuditVO;
    
    @ApiModelProperty(value = "ahcs_investigate_assist表主键")
    private String idAhcsInvestigateAssist;

    @ApiModelProperty(value = "主键")
    private String idAhcsInvestigateTask;

    @ApiModelProperty(value = "ahcs_investigate表主键")
    private String idAhcsInvestigate;

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "赔付次数")
    private Integer caseTimes;

    @ApiModelProperty(value = "调查人（UM）")
    private String investigatorUm;

    @ApiModelProperty(value = "调查人（Name）")
    private String investigatorName;

    @ApiModelProperty(value = "分配人（UM）")
    private String dispatchUm;

    @ApiModelProperty(value = "分配人（Nmae）")
    private String dispatchName;

    @ApiModelProperty(value = "被保险人姓名")
    private String customerName;

    @ApiModelProperty(value = "任务状态")
    private String taskStatus;

    @ApiModelProperty(value = "调查机构")
    private String investigateDepartment;

    @ApiModelProperty(value = "是否为主调查任务")
    private String isPrimaryTask;

    @ApiModelProperty(value = "是否为异地调查任务")
    private String isOffsiteTask;

    @ApiModelProperty(value = "分配信息/协查事项/分配意见")
    private String dispatchOpinion;

    @ApiModelProperty(value = "完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date finishDate;

    @ApiModelProperty(value = "调查结论")
    private String investigateConclusion;

    @ApiModelProperty(value = "调查定性")
    private String investigateQualitative;

    @ApiModelProperty(value = "异常子项")
    private String abnormalDetail;

    @ApiModelProperty(value = "证据资料")
    private String hasEvidence;

    @ApiModelProperty(value = "证据资料子项")
    private String evidenceDetail;

    @ApiModelProperty(value = "调查人姓名")
    private String investigatorUmName;

    @ApiModelProperty(value = "分配人姓名")
    private String dispatchUmName;

    @ApiModelProperty(value = "调查机构名")
    private String investigateDepartmentName;
    

    private String aging;
    
    @ApiModelProperty(value = "调查定性中文名称")
    private String investigateQualitativeName;

    @ApiModelProperty(value = "异常子项中文名称")
    private String abnormalDetailName;
    
    @ApiModelProperty(value = "AHCS_INVESTIGATE_AUDIT表主键")
    private String idAhcsInvestigateAudit;


    private Integer returnTimes;

    @ApiModelProperty(value = "查勘类型")
    private String surveyType;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公估费")
    private BigDecimal commonEstimateFee  ;

    @ApiModelProperty(value = "是否包含公估费")
    private String isHasAdjustingFee;

    @ApiModelProperty(value = "公估师姓名")
    private String adjusterName;

    @ApiModelProperty(value = "公估师电话")
    private String adjusterPhone;

    @ApiModelProperty(value = "减损金额")
    private BigDecimal lossReductionAmount;

    public String getAdjusterName() {
        return adjusterName;
    }

    public void setAdjusterName(String adjusterName) {
        this.adjusterName = adjusterName;
    }

    public String getAdjusterPhone() {
        return adjusterPhone;
    }

    public void setAdjusterPhone(String adjusterPhone) {
        this.adjusterPhone = adjusterPhone;
    }

    public BigDecimal getLossReductionAmount() {
        return lossReductionAmount;
    }

    public void setLossReductionAmount(BigDecimal lossReductionAmount) {
        this.lossReductionAmount = lossReductionAmount;
    }

    public BigDecimal getCommonEstimateFee() {
        return commonEstimateFee;
    }

    public void setCommonEstimateFee(BigDecimal commonEstimateFee) {
        this.commonEstimateFee = commonEstimateFee;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }


    private String surveyTypeDesc;

    public String getSurveyType() {
        return surveyType;
    }

    public void setSurveyType(String surveyType) {
        this.surveyType = surveyType;
    }
    public String getSurveyTypeDesc() {
        return surveyTypeDesc;
    }

    public void setSurveyTypeDesc(String surveyTypeDesc) {
        this.surveyTypeDesc = surveyTypeDesc;
    }


    private String taskName;

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Integer getReturnTimes() {
        return returnTimes;
    }

    public void setReturnTimes(Integer returnTimes) {
        this.returnTimes = returnTimes;
    }


    
    public String getIdAhcsInvestigateAudit() {
		return idAhcsInvestigateAudit;
	}

	public void setIdAhcsInvestigateAudit(String idAhcsInvestigateAudit) {
		this.idAhcsInvestigateAudit = idAhcsInvestigateAudit;
	}

    public String getInvestigateQualitativeName() {
		return investigateQualitativeName;
	}

	public void setInvestigateQualitativeName(String investigateQualitativeName) {
		this.investigateQualitativeName = investigateQualitativeName;
	}

	public String getAbnormalDetailName() {
		return abnormalDetailName;
	}

	public void setAbnormalDetailName(String abnormalDetailName) {
		this.abnormalDetailName = abnormalDetailName;
	}

	public String getAging() {
		return aging;
	}

	public void setAging(String aging) {
		this.aging = aging;
	}

	public String getInvestigatorUmName() {
		return investigatorUmName;
	}

	public void setInvestigatorUmName(String investigatorUmName) {
		this.investigatorUmName = investigatorUmName;
	}

	public String getDispatchUmName() {
		return dispatchUmName;
	}

	public void setDispatchUmName(String dispatchUmName) {
		this.dispatchUmName = dispatchUmName;
	}

	public String getInvestigateDepartmentName() {
		return investigateDepartmentName;
	}

	public void setInvestigateDepartmentName(String investigateDepartmentName) {
		this.investigateDepartmentName = investigateDepartmentName;
	}

	public String getIdAhcsInvestigateTask() {
        return idAhcsInvestigateTask;
    }

    public void setIdAhcsInvestigateTask(String idAhcsInvestigateTask) {
        this.idAhcsInvestigateTask = idAhcsInvestigateTask;
    }

    public String getIdAhcsInvestigate() {
        return idAhcsInvestigate;
    }

    public void setIdAhcsInvestigate(String idAhcsInvestigate) {
        this.idAhcsInvestigate = idAhcsInvestigate;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getInvestigatorUm() {
        return investigatorUm;
    }

    public void setInvestigatorUm(String investigatorUm) {
        this.investigatorUm = investigatorUm;
    }

    public String getDispatchUm() {
        return dispatchUm;
    }

    public void setDispatchUm(String dispatchUm) {
        this.dispatchUm = dispatchUm;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getInvestigateDepartment() {
        return investigateDepartment;
    }

    public void setInvestigateDepartment(String investigateDepartment) {
        this.investigateDepartment = investigateDepartment;
    }

    public String getIsPrimaryTask() {
        return isPrimaryTask;
    }

    public void setIsPrimaryTask(String isPrimaryTask) {
        this.isPrimaryTask = isPrimaryTask;
    }

    public String getIsOffsiteTask() {
        return isOffsiteTask;
    }

    public void setIsOffsiteTask(String isOffsiteTask) {
        this.isOffsiteTask = isOffsiteTask;
    }

    public String getDispatchOpinion() {
        return dispatchOpinion;
    }

    public void setDispatchOpinion(String dispatchOpinion) {
        this.dispatchOpinion = dispatchOpinion;
    }

    public String getInvestigateConclusion() {
        return investigateConclusion;
    }

    public void setInvestigateConclusion(String investigateConclusion) {
        this.investigateConclusion = investigateConclusion;
    }

    public String getInvestigateQualitative() {
        return investigateQualitative;
    }

    public void setInvestigateQualitative(String investigateQualitative) {
        this.investigateQualitative = investigateQualitative;
    }

    public String getAbnormalDetail() {
        return abnormalDetail;
    }

    public void setAbnormalDetail(String abnormalDetail) {
        this.abnormalDetail = abnormalDetail;
    }

    public String getHasEvidence() {
        return hasEvidence;
    }

    public void setHasEvidence(String hasEvidence) {
        this.hasEvidence = hasEvidence;
    }

    public String getEvidenceDetail() {
        return evidenceDetail;
    }

    public void setEvidenceDetail(String evidenceDetail) {
        this.evidenceDetail = evidenceDetail;
    }

	public List<InvestigateAssistVO> getInvestigateAssistVOs() {
		return investigateAssistVOs;
	}

	public void setInvestigateAssistVOs(List<InvestigateAssistVO> investigateAssistVOs) {
		this.investigateAssistVOs = investigateAssistVOs;
	}

	public List<InvestigateTaskAuditVO> getInvestigateTaskAuditVOs() {
		return investigateTaskAuditVOs;
	}

	public void setInvestigateTaskAuditVOs(List<InvestigateTaskAuditVO> investigateTaskAuditVOs) {
		this.investigateTaskAuditVOs = investigateTaskAuditVOs;
	}

	public List<InvestigateProcessVO> getInvestigateProcessVOs() {
		return investigateProcessVOs;
	}

	public void setInvestigateProcessVOs(List<InvestigateProcessVO> investigateProcessVOs) {
		this.investigateProcessVOs = investigateProcessVOs;
	}

	public String getIdAhcsInvestigateAssist() {
		return idAhcsInvestigateAssist;
	}

	public void setIdAhcsInvestigateAssist(String idAhcsInvestigateAssist) {
		this.idAhcsInvestigateAssist = idAhcsInvestigateAssist;
	}

	public InvestigateAuditVO getInvestigateAuditVO() {
		return investigateAuditVO;
	}

	public void setInvestigateAuditVO(InvestigateAuditVO investigateAuditVO) {
		this.investigateAuditVO = investigateAuditVO;
	}

    public String getDispatchName() {
        return dispatchName;
    }

    public void setDispatchName(String dispatchName) {
        this.dispatchName = dispatchName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getInvestigatorName() {
        return investigatorName;
    }

    public void setInvestigatorName(String investigatorName) {
        this.investigatorName = investigatorName;
    }

    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }
}