package com.paic.ncbs.claim.common.constant;

import com.paic.ncbs.claim.common.enums.AccidentReasonTypeEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccidentReasonConst {

    private AccidentReasonConst(){}

    public static final String OTHER_INSURANCE = "01";  //01:其他险（如退运险）
    public static final String HEALTH_INSURANCE = "02"; //02:健康险
    public static final String INJURY_INSURANCE = "03"; //03:意健险
    public static final String RESPONSIBILITY_INSURANCE = "04"; //04:责任险
    public static final String HOME_INSURANCE = "05"; //05:家财险
    public static final String ENTERPRISE_INSURANCE = "22"; //22:企财险
    public static final String GUARANTEE_INSURANCE = "23"; //23:保证保险

    public static final Map<String, List<String>> PRODUCT_CLASS_ACCIDENT_REASON_MAP = new HashMap<>();
    static {
        //其他-> 其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(OTHER_INSURANCE, Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_000.getCode()));
        //健康险-> 意外健康  其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(HEALTH_INSURANCE,Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_004.getCode(),AccidentReasonTypeEnum.ACCIDENT_000.getCode()));
        //意健险-> 意外健康  其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(INJURY_INSURANCE, Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_004.getCode(),AccidentReasonTypeEnum.ACCIDENT_000.getCode()));
        //责任险-> 意外事故 其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(RESPONSIBILITY_INSURANCE,Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_002.getCode(),AccidentReasonTypeEnum.ACCIDENT_000.getCode()));
        //家财险-> 自然灾害 意外事故 其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(HOME_INSURANCE,Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_001.getCode(),AccidentReasonTypeEnum.ACCIDENT_002.getCode(),AccidentReasonTypeEnum.ACCIDENT_000.getCode()));
        //企财险-> 自然灾害 意外事故 其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(ENTERPRISE_INSURANCE,Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_001.getCode(),AccidentReasonTypeEnum.ACCIDENT_002.getCode(),AccidentReasonTypeEnum.ACCIDENT_000.getCode()));
        //保证保险-> 其他
        PRODUCT_CLASS_ACCIDENT_REASON_MAP.put(GUARANTEE_INSURANCE, Arrays.asList(AccidentReasonTypeEnum.ACCIDENT_000.getCode()));

    }
}
