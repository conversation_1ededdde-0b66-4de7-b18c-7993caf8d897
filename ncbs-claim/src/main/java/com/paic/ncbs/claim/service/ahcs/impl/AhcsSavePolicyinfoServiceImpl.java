package com.paic.ncbs.claim.service.ahcs.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.*;
import com.paic.ncbs.claim.model.dto.ahcs.*;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoExDTO;
import com.paic.ncbs.claim.service.ahcs.*;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicyHistoryUwInfoService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AhcsSavePolicyinfoServiceImpl implements AhcsSavePolicyinfoService {

    @Autowired
    private AhcsPolicyInfoService policyInfoService;

    @Autowired
    private AhcsInsuredPersonService insuredPersonService;

    @Autowired
    private AhcsInsuredPersonExtService insuredPersonExtService;

    @Autowired
    private AhcsSpecialPromiseService ahcsSpecialPromiseService;

    @Autowired
    private AhcsPolicyPlanService policyPlanService;

    @Autowired
    private AhcsPolicyPlanDataService policyPlanDataService;

    @Autowired
    private AhcsPolicyDutyService policyDutyService;

    @Autowired
    private AhcsPolicyDutyDataService policyDutyDataService;

    @Autowired
    private AhcsPolicyDutyDetailService policyDutyDetailService;

    @Autowired
    private AhcsCoinsureService coinsureService;

    @Autowired
    private AhcsPolicyHolderService policyHolderService;

    @Autowired
    private AhcsDutyAttributeService ahcsDutyAttributeService;

    @Autowired
    private AhcsDutyAttributeDetailService ahcsDutyAttributeDetailService;

    @Autowired
    private DepartmentDefineService departmentDefineService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ClmsPolicyHistoryUwInfoService clmsPolicyHistoryUwInfoService;

    @Override
    public void savePolicyInfo(AhcsDomainDTO ahcsDomainDTO, AhcsPolicyDomainDTO policyDomainDto) {

        List<AhcsPolicyDutyDataEntity> policyDutyDataEntities = null;
        List<AhcsDutyAttributeEntity> dutyAttributeEntities = null;
        AhcsPolicyInfoEntity policyInfo = policyDomainDto.getAhcsPolicyInfo();
        policyInfo.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        policyInfo.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        policyInfo.setCreatedDate(new Date());
        policyInfo.setUpdatedDate(new Date());
        policyInfo.setPolicyValid(BaseConstant.UPPER_CASE_Y);
        policyInfo.setIdAhcsPolicyInfo(UuidUtil.getUUID());
        policyInfo.setReportNo(ahcsDomainDTO.getReportNo());
        PolicyInfoExDTO policyInfoExDTO = policyDomainDto.getPolicyInfoExDTO();

        try {
            if (policyInfoExDTO != null) {
                List<String> param = new ArrayList<>();
                List<String> agentNames = policyInfoExDTO.getAgentNames();
                if (agentNames != null && !agentNames.isEmpty()) {
                    StringBuilder agentNameParam = new StringBuilder(BaseConstant.AGENT_NAME + ":" + agentNames.get(0));
                    for (int i = 1; i < agentNames.size(); i++) {
                        agentNameParam.append(BaseConstant.SEPARATE_CHAR).append(agentNames.get(i));
                    }
                    param.add(agentNameParam.toString());
                }

                String policyExtend = buildPolicyExtends(param);
                policyInfo.setPolicyExtend(policyExtend);
            }
        } catch (Exception e1) {

            LogUtil.info("构建保单表扩展字段异常： policyInfoExDTO ：", JSONObject.toJSONString(policyInfoExDTO));
        }

        String policyId = policyDomainDto.getAhcsPolicyInfo().getIdAhcsPolicyInfo();
        LogUtil.info("开始数据落地");
        //1. 插入 clms_policy_info 保单基本信息
        int insertResult = policyInfoService.insert(policyDomainDto.getAhcsPolicyInfo());
        LogUtil.info("保存数据落地共：{}条", insertResult);
        LogUtil.info("--开始保存报案保单数据--被保人--AhcsInsuredPresonDTOs");

        for (AhcsInsuredPresonDTO insuredPresonDto : policyDomainDto.getAhcsInsuredPresonDTOs()) {
            AhcsInsuredPresonEntity insuredPreson = insuredPresonDto.getAhcsInsuredPreson();
            insuredPreson.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            insuredPreson.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            insuredPreson.setCreatedDate(new Date());
            insuredPreson.setUpdatedDate(new Date());
            insuredPreson.setIdAhcsInsuredPerson(UuidUtil.getUUID());
            insuredPreson.setIdAhcsPolicyInfo(policyId);
            insuredPreson.setPlyCertificateType(insuredPreson.getCertificateType());
            insuredPreson.setCertificateType(insuredPreson.getCertificateType());
            insuredPreson.setRelationshipWithApplicant(insuredPreson.getRelationshipWithApplicant());
            //2. 插入 clms_insured_person 被保人表记录
            insuredPersonService.insert(insuredPreson);

            AhcsInsuredPersonExtEntity insuredPersonExt = insuredPresonDto.getAhcsInsuredPersonExt();
            insuredPersonExt.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            insuredPersonExt.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            insuredPersonExt.setCreatedDate(new Date());
            insuredPersonExt.setUpdatedDate(new Date());
            insuredPersonExt.setIdAhcsInsuredPerson(insuredPreson.getIdAhcsInsuredPerson());
            insuredPersonExt.setIdAhcsInsuredPersonExt(UuidUtil.getUUID());
            //3. 插入 clms_insured_person_ext 被保人扩展表记录
            insuredPersonExtService.insert(insuredPersonExt);
        }
        LogUtil.info("--开始保存报案保单数据--特约信息--AhcsSpecialPromise");

        for (AhcsSpecialPromiseEntity specialPromise : policyDomainDto.getAhcsSpecialPromise()) {
            specialPromise.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            specialPromise.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            specialPromise.setCreatedDate(new Date());
            specialPromise.setUpdatedDate(new Date());
            specialPromise.setIdAhcsSpecialPromise(UuidUtil.getUUID());
            specialPromise.setIdAhcsPolicyInfo(policyId);
        }
        if (policyDomainDto.getAhcsSpecialPromise().size() > 1) {
            //4. 插入多条clms_Special_Promise特约表记录
            ahcsSpecialPromiseService.insertList(policyDomainDto.getAhcsSpecialPromise());
        } else if (policyDomainDto.getAhcsSpecialPromise().size() == 1) {
            //4. 插入一条clms_Special_Promise特约表记录
            ahcsSpecialPromiseService.insert(policyDomainDto.getAhcsSpecialPromise().get(0));
        }
        LogUtil.info("--开始保存报案保单数据--险种责任信息--AhcsPolicyPlanDTOs");

        for (AhcsPolicyPlanDTO policyPlanDTO : policyDomainDto.getAhcsPolicyPlanDTOs()) {
            AhcsPolicyPlanEntity policyPlan = policyPlanDTO.getAhcsPolicyPlan();
            policyPlan.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            policyPlan.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            policyPlan.setCreatedDate(new Date());
            policyPlan.setUpdatedDate(new Date());
            String policyPlanId = UuidUtil.getUUID();
            policyPlan.setIdAhcsPolicyPlan(policyPlanId);
            policyPlan.setIdAhcsPolicyInfo(policyId);
            AhcsPolicyPlanDataEntity ahcsPolicyPlanData = new AhcsPolicyPlanDataEntity();

            ahcsPolicyPlanData.setIdAhcsPolicyPlanData(UuidUtil.getUUID());
            BeanUtils.copyProperties(policyPlan, ahcsPolicyPlanData);
            //5. 插入 clms_policy_plan_data 保单险种信息表记录
            policyPlanDataService.insert(ahcsPolicyPlanData);

            policyDutyDataEntities = new ArrayList<>();
            for (AhcsPolicyDutyDTO policyDutyDTO : policyPlanDTO.getAhcsPolicyDutyDTOs()) {
                AhcsPolicyDutyEntity policyDuty = policyDutyDTO.getAhcsPolicyDuty();
                policyDuty.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
                policyDuty.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
                policyDuty.setCreatedDate(new Date());
                policyDuty.setUpdatedDate(new Date());
                policyDuty.setIdAhcsPolicyPlan(policyPlanId);
                String policyDutyId = UuidUtil.getUUID();
                policyDuty.setIdAhcsPolicyDuty(policyDutyId);

                AhcsPolicyDutyDataEntity ahcsPolicyDutyData = new AhcsPolicyDutyDataEntity();
                BeanUtils.copyProperties(policyDuty, ahcsPolicyDutyData);
                ahcsPolicyDutyData.setIdAhcsPolicyDutyData(UuidUtil.getUUID());
                policyDutyDataEntities.add(ahcsPolicyDutyData);

                for (AhcsPolicyDutyDetailEntity policyDutyDetail : policyDutyDTO.getAhcsPolicyDutyDetail()) {
                    policyDutyDetail.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
                    policyDutyDetail.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
                    policyDutyDetail.setCreatedDate(new Date());
                    policyDutyDetail.setUpdatedDate(new Date());
                    String policyDutyDetailId = UuidUtil.getUUID();
                    policyDutyDetail.setIdAhcsPolicyDutyDetail(policyDutyDetailId);
                    policyDutyDetail.setIdAhcsPolicyDuty(policyDutyId);
                }
                dutyAttributeEntities = new ArrayList<>();
                for (AhcsDutyAttributeDTO attributeDTO : policyDutyDTO.getAhcsDutyAttributeDTOs()) {
                    AhcsDutyAttributeEntity dutyAttributeEntity = attributeDTO.getAhcsDutyAttribute();
                    String dutyAttributeId = UuidUtil.getUUID();
                    dutyAttributeEntity.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
                    dutyAttributeEntity.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
                    dutyAttributeEntity.setCreatedDate(new Date());
                    dutyAttributeEntity.setUpdatedDate(new Date());
                    dutyAttributeEntity.setIdAhcsPolicyDuty(policyDutyId);
                    dutyAttributeEntity.setIdAhcsDutyAttribute(dutyAttributeId);
                    dutyAttributeEntities.add(dutyAttributeEntity);
                    for (AhcsDutyAttributeDetailEntity attributeDetailEntity : attributeDTO.getAhcsDutyAttributeDetail()) {
                        attributeDetailEntity.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
                        attributeDetailEntity.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
                        attributeDetailEntity.setCreatedDate(new Date());
                        attributeDetailEntity.setUpdatedDate(new Date());
                        attributeDetailEntity.setIdAhcsDutyAttribute(dutyAttributeId);
                        attributeDetailEntity.setIdAhcsDutyAttributeDetail(UuidUtil.getUUID());
                    }
                }
            }
            if (policyDutyDataEntities.size() > 1) {
                //6. 插入多条 clms_policy_duty_data 保单责任信息表记录
                policyDutyDataService.insertList(policyDutyDataEntities);
            } else if (policyDutyDataEntities.size() == 1) {
                //6. 插入1条clms_policy_duty_data 保单责任信息表
                policyDutyDataService.insert(policyDutyDataEntities.get(0));
            }
        }

        /**
         * 7. 插入clms_policy_plan险种记录
         * 8. 插入clms_policy_duty责任记录
         * 9. 插入clms_policy_duty_detail责任明细表
         * 10. 插入clms_duty_attribute意健险责任属性表
         * 11. 插入clms_duty_attribute_detail意健险责任属性明细表
         */
        this.saveMergePlanDutyInfo(policyDomainDto.getAhcsPolicyPlanDTOs());

        LogUtil.info("--开始保存报案保单数据--共保--AhcsCoinsure");

        for (AhcsCoinsureEntity coinsure : policyDomainDto.getAhcsCoinsure()) {
            coinsure.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            coinsure.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            coinsure.setCreatedDate(new Date());
            coinsure.setUpdatedDate(new Date());
            coinsure.setIdAhcsCoinsure(UuidUtil.getUUID());
            coinsure.setIdAhcsPolicyInfo(policyId);
        }
        if (policyDomainDto.getAhcsCoinsure().size() > 1) {
            //12. 插入clms_coinsure共保信息
            coinsureService.insertList(policyDomainDto.getAhcsCoinsure());
        } else if (policyDomainDto.getAhcsCoinsure().size() == 1) {
            coinsureService.insert(policyDomainDto.getAhcsCoinsure().get(0));
        }
        LogUtil.info("--开始保存报案保单数据--投保人信息--AhcsPolicyHolder");

        for (AhcsPolicyHolderEntity policyHolder : policyDomainDto.getAhcsPolicyHolder()) {
            policyHolder.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            policyHolder.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            policyHolder.setCreatedDate(new Date());
            policyHolder.setUpdatedDate(new Date());
            policyHolder.setIdAhcsPolicyHolder(UuidUtil.getUUID());
            policyHolder.setIdAhcsPolicyInfo(policyId);
        }
        //13. 保存 CLMS_POLICY_HOLDER
        if (policyDomainDto.getAhcsPolicyHolder().size() > 1) {
            policyHolderService.insertList(policyDomainDto.getAhcsPolicyHolder());
        } else if (policyDomainDto.getAhcsPolicyHolder().size() == 1) {
            policyHolderService.insert(policyDomainDto.getAhcsPolicyHolder().get(0));
        }
        //14. 保存小条款 clms_plan_term
        List<PlanTermContentDTO> planTermContentDTOList = policyInfoExDTO.getPlanTermContentDTOList();
        saveTremContent(planTermContentDTOList,ahcsDomainDTO);
        //15. 保存抄单抄过来的核保信息 clms_policy_history_uw_info
        List<ClmsPolicyHistoryUwInfoDTO> policyHistoryUwInfoDTOList = policyDomainDto.getPolicyHistoryUwInfoDTOList();
        if(ObjectUtil.isNotNull(policyHistoryUwInfoDTOList) && policyHistoryUwInfoDTOList.size()>=1){
           //保存
            try{
                clmsPolicyHistoryUwInfoService.saveBatch(policyHistoryUwInfoDTOList,ahcsDomainDTO.getReportNo());
            }catch (Exception e){
                log.error("抄单核保信息保存异常",e);
            }

        }
        //16. 保存保单在理赔的核保信息 clms_policy_history_uw_info
        try {
            clmsPolicyHistoryUwInfoService.saveClaimPolicyUwInfo(ahcsDomainDTO.getQueryUwInfoPolicys());
        } catch (Exception e) {
            log.error("抄单理赔二核信息保存异常",e);
        }
    }



    private List<AhcsPolicyPlanDTO> mergePlanAndDutyInfo(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyPlanDTO> mergePolicyPlanList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyPlanDTOs)) {
            for (AhcsPolicyPlanDTO planDto : ahcsPolicyPlanDTOs) {
                String planId = planDto.getAhcsPolicyPlan().getIdAhcsPolicyPlan();
                String planKey = planDto.getAhcsPolicyPlan().getPlanCode() + "_" + planDto.getAhcsPolicyPlan().getRiskGroupNo();
                if (!codeList.contains(planKey)) {
                    planDto.setAhcsPolicyDutyDTOs(mergeDutyInfo(planDto.getAhcsPolicyDutyDTOs(), planId));
                    mergePolicyPlanList.add(planDto);
                    codeList.add(planKey);
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyPlanList)) {
                        for (AhcsPolicyPlanDTO mergePlanDto : mergePolicyPlanList) {

                            if (mergePlanDto.getAhcsPolicyPlan().getPlanCode().equals(planDto.getAhcsPolicyPlan().getPlanCode())) {
                                mergePlanDto.getAhcsPolicyDutyDTOs().addAll(planDto.getAhcsPolicyDutyDTOs());
                                mergePlanDto.setApplyNum(mergePlanDto.getApplyNum().add(planDto.getApplyNum()));
                                mergePlanDto.setAhcsPolicyDutyDTOs(mergeDutyInfo(mergePlanDto.getAhcsPolicyDutyDTOs(), mergePlanDto.getAhcsPolicyPlan().getIdAhcsPolicyPlan()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyPlanList;
    }

    private List<AhcsPolicyDutyDTO> mergeDutyInfo(List<AhcsPolicyDutyDTO> ahcsPolicyDutyDTOs, String planId) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyDutyDTO> mergePolicyDutyList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDutyDTOs)) {
            for (AhcsPolicyDutyDTO dutyDto : ahcsPolicyDutyDTOs) {
                dutyDto.getAhcsPolicyDuty().setIdAhcsPolicyPlan(planId);
                if (!codeList.contains(dutyDto.getAhcsPolicyDuty().getDutyCode())) {
                    mergePolicyDutyList.add(dutyDto);
                    codeList.add(dutyDto.getAhcsPolicyDuty().getDutyCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyDutyList)) {
                        for (AhcsPolicyDutyDTO mergeDutyuDto : mergePolicyDutyList) {

                            if (mergeDutyuDto.getAhcsPolicyDuty().getDutyCode().equals(dutyDto.getAhcsPolicyDuty().getDutyCode())) {

                                mergeDutyuDto.getAhcsPolicyDuty().setDutyAmount(mergeDutyuDto.getAhcsPolicyDuty().getDutyAmount().add(dutyDto.getAhcsPolicyDuty().getDutyAmount()));
                                mergeDutyuDto.getAhcsPolicyDutyDetail().addAll(dutyDto.getAhcsPolicyDutyDetail());
                                mergeDutyuDto.setAhcsPolicyDutyDetail(mergeDutyDetailInfo(mergeDutyuDto.getAhcsPolicyDutyDetail(), mergeDutyuDto.getAhcsPolicyDuty().getIdAhcsPolicyDuty()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyDutyList;
    }

    private List<AhcsPolicyDutyDetailEntity> mergeDutyDetailInfo(List<AhcsPolicyDutyDetailEntity> ahcsPolicyDutyDetailDTOs, String dutyId) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyDutyDetailEntity> mergePolicyDutyDetailList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDutyDetailDTOs)) {
            for (AhcsPolicyDutyDetailEntity ahcsPolicyDutyDetail : ahcsPolicyDutyDetailDTOs) {

                ahcsPolicyDutyDetail.setIdAhcsPolicyDuty(dutyId);
                if (!codeList.contains(ahcsPolicyDutyDetail.getDutyDetailCode())) {
                    mergePolicyDutyDetailList.add(ahcsPolicyDutyDetail);
                    codeList.add(ahcsPolicyDutyDetail.getDutyDetailCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyDutyDetailList)) {
                        for (AhcsPolicyDutyDetailEntity mergeDutyDetail : mergePolicyDutyDetailList) {
                            if (mergeDutyDetail.getDutyDetailCode().equals(ahcsPolicyDutyDetail.getDutyDetailCode())) {
                                mergeDutyDetail.setDutyAmount(mergeDutyDetail.getDutyAmount().add(ahcsPolicyDutyDetail.getDutyAmount()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyDutyDetailList;
    }

    private String buildPolicyExtends(List<String> list) {

        String policyExtend = "";

        if (list == null || list.size() == 0) {
            return null;
        }
        policyExtend = list.get(0);
        for (int i = 1; i < list.size(); i++) {
            policyExtend = policyExtend + "," + list.get(i);
        }
        return policyExtend;
    }

    private void saveMergePlanDutyInfo(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {

        ahcsPolicyPlanDTOs = mergePlanAndDutyInfo(ahcsPolicyPlanDTOs);
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyPlanDTOs)) {
            for (AhcsPolicyPlanDTO planDto : ahcsPolicyPlanDTOs) {
                //插入clms_policy_plan险种记录
                policyPlanService.insert(planDto.getAhcsPolicyPlan());
                if (RapeCheckUtil.isNotEmpty(planDto.getAhcsPolicyDutyDTOs())) {
                    for (AhcsPolicyDutyDTO dutyDto : planDto.getAhcsPolicyDutyDTOs()) {
                        //插入clms_policy_duty责任记录
                        policyDutyService.insert(dutyDto.getAhcsPolicyDuty());
                        if (RapeCheckUtil.isNotEmpty(dutyDto.getAhcsPolicyDutyDetail())) {
                            //插入clms_policy_duty_detail责任明细表
                            if (dutyDto.getAhcsPolicyDutyDetail().size() > 1) {
                                policyDutyDetailService.insertList(dutyDto.getAhcsPolicyDutyDetail());
                            } else if (dutyDto.getAhcsPolicyDutyDetail().size() == 1) {
                                policyDutyDetailService.insert(dutyDto.getAhcsPolicyDutyDetail().get(0));
                            }
                        }
                        if (RapeCheckUtil.isNotEmpty(dutyDto.getAhcsDutyAttributeDTOs())) {
                            for (AhcsDutyAttributeDTO dutyAttributeDTO : dutyDto.getAhcsDutyAttributeDTOs()) {
                                //插入clms_duty_attribute意健险责任属性表
                                ahcsDutyAttributeService.insert(dutyAttributeDTO.getAhcsDutyAttribute());
                                if (RapeCheckUtil.isNotEmpty(dutyAttributeDTO.getAhcsDutyAttributeDetail())) {
                                    if (dutyAttributeDTO.getAhcsDutyAttributeDetail().size() > 1) {
                                        //插入clms_duty_attribute_detail意健险责任属性明细表
                                        ahcsDutyAttributeDetailService.insertList(dutyAttributeDTO.getAhcsDutyAttributeDetail());
                                    } else if (dutyAttributeDTO.getAhcsDutyAttributeDetail().size() == 1) {
                                        ahcsDutyAttributeDetailService.insert(dutyAttributeDTO.getAhcsDutyAttributeDetail().get(0));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 保存小条款信息
     * @param planTermContentDTOList
     */
    private void saveTremContent(List<PlanTermContentDTO> planTermContentDTOList,AhcsDomainDTO ahcsDomainDTO) {
        LogUtil.audit("小条款报案号={},入参={}",ahcsDomainDTO.getReportNo(), JSON.toJSONString(planTermContentDTOList));
        if(CollectionUtil.isEmpty(planTermContentDTOList)){
            return;
        }
        String reportNo = ahcsDomainDTO.getReportNo();
        Integer caseTimes = Integer.valueOf(ahcsDomainDTO.getCaseTimes());
        String um=ahcsDomainDTO.getReportAcceptUm();
        List<PlanTermContentEntity> entities = new ArrayList<>();
        for (PlanTermContentDTO dto : planTermContentDTOList) {
            PlanTermContentEntity entity = new PlanTermContentEntity();
            BeanUtils.copyProperties(dto,entity);
            entity.setReportNo(reportNo);
            entity.setCaseTimes(caseTimes);
            entity.setId(UuidUtil.getUUID());
            entity.setCreatedBy(um);
            entity.setUpdatedBy(um);
            entities.add(entity);
        }
        policyPlanDataService.insertTermList(entities);
    }


}
