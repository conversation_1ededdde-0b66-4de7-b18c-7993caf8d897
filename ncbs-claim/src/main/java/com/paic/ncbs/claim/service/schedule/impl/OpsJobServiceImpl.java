package com.paic.ncbs.claim.service.schedule.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.ReportConstant;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.BatchAutoCloseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.pay.SendPaymentRecordMapper;
import com.paic.ncbs.claim.dao.mapper.reinsurance.SendReinsuranceRecordMapper;
import com.paic.ncbs.claim.model.dto.ahcs.BatchAutoCloseDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.common.ClmsCommonPolicyService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.schedule.OpsJobService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpsJobServiceImpl implements OpsJobService {

    @Autowired
    private CommonParameterService commonService;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private SendReinsuranceRecordMapper sendReinsuranceRecordMapper;
    @Autowired
    private SendPaymentRecordMapper sendPaymentRecordMapper;
    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private BatchAutoCloseMapper batchAutoCloseMapper;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private ClmsCommonPolicyService clmsPolicyService;
    /**
     * 处理退货运费险结案之后的操作（送收付，送再保）。
     *
     * @param reportNos 补传报案号集合
     * @param isManual  是否人工补传 1是，0否
     * @return
     */
    @Override
    public Map<String, List<String>> saveCloseCaseAfter(List<String> reportNos, String isManual) {
        LogUtil.info("saveCloseCaseAfter开始:reportNos={}，isManual={}", reportNos, isManual);
        if (StringUtils.equals("1", isManual) && CollectionUtils.isEmpty(reportNos)) {
            return null;
        }


        Map<String, List<String>> resultMap = new HashMap<>();
        List<WholeCaseBaseDTO> wholeCaseBaseDTOS;
        // 获取需要处理的案件
        if (StringUtils.equals("1", isManual)) {
            // 人工取人功入参 默认处理caseTimes = 1的情况
            wholeCaseBaseDTOS = new ArrayList<>();
            reportNos.forEach(reportNo -> wholeCaseBaseDTOS.add(wholeCaseBaseMapper.getWholeCaseBase2(reportNo, 1)));
        } else {
            // 自动查询退货运费险需要补传的案件。
            wholeCaseBaseDTOS = wholeCaseBaseMapper.getWholeCaseBaseByPendPay();
        }
        if(CollectionUtils.isEmpty(wholeCaseBaseDTOS)){
            LogUtil.info("saveCloseCaseAfter需要补充案件不存在直接返回！");
            return null;
        }
        LogUtil.info("saveCloseCaseAfter送收付开始:{}", wholeCaseBaseDTOS.size());
        for (WholeCaseBaseDTO wholeCaseBaseDTO : wholeCaseBaseDTOS) {
            String reportNo = wholeCaseBaseDTO.getReportNo();
            Integer caseTimes = wholeCaseBaseDTO.getCaseTimes();
            LogUtil.info("saveCloseCaseAfter送收付:{},{}", reportNo, caseTimes);
            // 添加校验 paymentitem里状态不全是10就跳过
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setCaseTimes(caseTimes);
            List<PaymentItemDTO> paymentItemDTOList = paymentItemService.getPaymentItem(paymentItemDTO);
            if (CollectionUtils.isEmpty(paymentItemDTOList)
                    || paymentItemDTOList.stream().anyMatch(tPaymentItemDTO ->
                    !StringUtils.equals(Constants.PAYMENT_ITEM_STATUS_10, tPaymentItemDTO.getPaymentItemStatus()))) {
                LogUtil.info("saveCloseCaseAfter送收付跳过:{},{}", reportNo, caseTimes);
                resultMap.putIfAbsent("noticePayment", new ArrayList<>());
                resultMap.get("noticePayment").add(reportNo);
                continue;
            }
            payInfoNoticeThirdPartyCoreSAO.noticePayment(reportNo, caseTimes, null, true, false);

        }
        LogUtil.info("saveCloseCaseAfter送收付结束:{}！", wholeCaseBaseDTOS.size());

        LogUtil.info("saveCloseCaseAfter-送再保开始:");
        // 立案送再保
        for (WholeCaseBaseDTO wholeCaseBaseDTO : wholeCaseBaseDTOS) {
            String reportNo = wholeCaseBaseDTO.getReportNo();
            Integer caseTimes = wholeCaseBaseDTO.getCaseTimes();
            // 添加校验只要有送再保记录就不送了不管成功失败。
            if (!StringUtils.equals("0", wholeCaseBaseDTO.getWholeCaseStatus())
                    || sendReinsuranceRecordMapper.queryOneByCondition(reportNo, caseTimes,
                    ReinsuranceClaimTypeEnum.REGISTER.getType()) != null) {
                LogUtil.info("saveCloseCaseAfter-送再保立案跳过:reportNo={}，caseTimes={}", reportNo, caseTimes);
                resultMap.putIfAbsent("registerReins", new ArrayList<>());
                resultMap.get("registerReins").add(reportNo);
                continue;
            }
            LogUtil.info("saveCloseCaseAfter-送再保立案开始:reportNo={}，caseTimes={}", reportNo, caseTimes);
            RepayCalDTO repayCalDTO = new RepayCalDTO();
            repayCalDTO.setReportNo(reportNo);
            repayCalDTO.setCaseTimes(caseTimes);
            repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.REGISTER);
            reinsuranceService.sendReinsurance(repayCalDTO);
        }

//        try {
//            // 确保createDate不一样 延时一秒调用
//            Thread.sleep(1000L);
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//        }
        // 结案送再保
        for (WholeCaseBaseDTO wholeCaseBaseDTO : wholeCaseBaseDTOS) {
            String reportNo = wholeCaseBaseDTO.getReportNo();
            Integer caseTimes = wholeCaseBaseDTO.getCaseTimes();
            // 添加校验只要有送再保记录就不送了不管成功失败。
            if (!StringUtils.equals("0", wholeCaseBaseDTO.getWholeCaseStatus())
                    || sendReinsuranceRecordMapper.queryOneByCondition(reportNo, caseTimes,
                    ReinsuranceClaimTypeEnum.ENDCASE.getType()) != null) {
                LogUtil.info("saveCloseCaseAfter-送再保结案跳过:reportNo={}，caseTimes={}", reportNo, caseTimes);
                resultMap.putIfAbsent("endcaseReins", new ArrayList<>());
                resultMap.get("endcaseReins").add(reportNo);
                continue;
            }
            LogUtil.info("saveCloseCaseAfter-送再保结案开始:reportNo={}，caseTimes={}", reportNo, caseTimes);
            RepayCalDTO repayCalDTO = new RepayCalDTO();
            repayCalDTO.setReportNo(reportNo);
            repayCalDTO.setCaseTimes(caseTimes);
            repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
            repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
            reinsuranceService.sendReinsurance(repayCalDTO);
        }
        LogUtil.info("saveCloseCaseAfter送再保结束。");

        LogUtil.info("saveCloseCaseAfter结束:{}！", reportNos);
        return resultMap;
    }

    /**
     * 批量修改支付退回任务
     * @param reportNos 报案号集合
     * @return
     */
    @Override
    public List<String> batchModifyPayBack(List<String> reportNos) {
        LogUtil.info("batchModifyPayBack开始:reportNos={}", reportNos);
        if (CollectionUtils.isEmpty(reportNos)) {
            LogUtil.info("batchModifyPayBack: request is empty.");
            return null;
        }
        List<PaymentItemDTO> paymentItemDTOList = new ArrayList<>();
        for (String reportNo : reportNos) {
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_70);
            paymentItemDTOList.addAll(paymentItemService.getPaymentItem(paymentItemDTO));
        }

        if (CollectionUtils.isEmpty(paymentItemDTOList)) {
            LogUtil.info("batchModifyPayBack: 请求案件无退票数据！");
            return null;
        }

        List<String> errorList = new ArrayList<>();
        for (PaymentItemDTO itemDTO : paymentItemDTOList) {
            try {
                PaymentInfoVO paymentInfoVO = new PaymentInfoVO();
                BeanUtils.copyProperties(itemDTO, paymentInfoVO);
                paymentInfoVO.setCityCode(itemDTO.getCityName());
                AdressSearchDto adressSearchDto = new AdressSearchDto();
                adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(paymentInfoVO.getProvinceName())
                        .setAccidentCountyCode(paymentInfoVO.getRegionCode()).setAccidentCityCode(paymentInfoVO.getCityCode());
                AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
                paymentInfoVO.setProName(detailAdressFormCode.getAccidentProvinceName());
                paymentInfoVO.setCiName(detailAdressFormCode.getAccidentCityName());
                paymentInfoVO.setCountryName(detailAdressFormCode.getAccidentCountyName());

                paymentItemService.modifyPayBackItem(paymentInfoVO, itemDTO);
            } catch (Exception e) {
                String errorNo = itemDTO.getReportNo() + " " + itemDTO.getIdClmPaymentItem();
                LogUtil.error("batchModifyPayBack支付修改异常，案件：{}", errorNo, e);
                errorList.add(errorNo);
            }
        }
        LogUtil.info("batchModifyPayBack结束:{}！", reportNos);
        return errorList;
    }

    /**
     * 批量补推自动核赔后继逻辑
     *
     * @param reportNos
     * @return
     */
    @Override
    public List<String> batchAutoVerifyAfter(List<String> reportNos) {
        List<WholeCaseBaseDTO> wholeCaseBaseDTOS = new ArrayList<>();
        // 获取需要处理的案件
        reportNos.forEach(reportNo -> wholeCaseBaseDTOS.add(wholeCaseBaseMapper.getWholeCaseBase2(reportNo, 1)));
        if(CollectionUtils.isEmpty(wholeCaseBaseDTOS)){
            LogUtil.info("batchAutoVerifyAfter需要补充案件不存在直接返回！");
            return null;
        }

        List<String> errorList = new ArrayList<>();
        for (WholeCaseBaseDTO wholeCaseBaseDTO : wholeCaseBaseDTOS) {
            String reportNo = wholeCaseBaseDTO.getReportNo();
            Integer caseTimes = wholeCaseBaseDTO.getCaseTimes();

            LogUtil.info("saveCloseCaseAfter-syncCaseStatus同步MQ:reportNo={}，caseTimes={}", reportNo, caseTimes);
            // 调用渠道mq
            SyncCaseStatusDto dto = new SyncCaseStatusDto();
            dto.setReportNo(reportNo);
            dto.setCaseTimes(caseTimes);
            dto.setCaseStatus(SyncCaseStatusEnum.ENDCASE);
            dto.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
            mqProducerSyncCaseStatusService.syncCaseStatus(dto);

            // 结案送再保
            // 添加校验只要有送再保记录就不送了不管成功失败。
            if (!StringUtils.equals("0", wholeCaseBaseDTO.getWholeCaseStatus())
                    || sendReinsuranceRecordMapper.queryOneByCondition(reportNo, caseTimes,
                    ReinsuranceClaimTypeEnum.ENDCASE.getType()) != null) {
                LogUtil.info("saveCloseCaseAfter-送再保结案跳过:reportNo={}，caseTimes={}", reportNo, caseTimes);
                errorList.add(reportNo);
                continue;
            }
            LogUtil.info("saveCloseCaseAfter-送再保结案开始:reportNo={}，caseTimes={}", reportNo, caseTimes);
            RepayCalDTO repayCalDTO = new RepayCalDTO();
            repayCalDTO.setReportNo(reportNo);
            repayCalDTO.setCaseTimes(caseTimes);
            repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
            repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
            reinsuranceService.sendReinsurance(repayCalDTO);
        }
        return errorList;
    }

    /**
     * 批量补推理赔费用信息到收付Q13
     *
     * @param reportNos
     * @return
     */
    @Override
    public void batchSendClaimVatInfo(List<String> reportNos) {
        LogUtil.info("batchSendClaimVatInfo:开始补传：{}", reportNos);
        List<String> cullPaySerials = new ArrayList<>();
        cullPaySerials.add("e8dfb82408144b99b9a78c025b8509b3");
        cullPaySerials.add("729c3c1acd0246e097f2d6d08af5270f");
        cullPaySerials.add("c43a8576caac46cbb2a21ba3ef161ecd");
        cullPaySerials.add("1d5494cfae91494abe144c296ce47571");
        cullPaySerials.add("35387f9c642845a6b97a814a6c60f64d");
        cullPaySerials.add("509da6e3779949e7895f4f374cd07499");
        cullPaySerials.add("1d8bc2bd99164ef9835a975574e1a2b7");
        cullPaySerials.add("547389ce825e47f0abe57acd2e19ae20");
        cullPaySerials.add("404428385d5f49cfbe1effbfc52ab6dc");
        cullPaySerials.add("8e692a8238a84c5ab061f1a0577cc026");
        cullPaySerials.add("bfed928e82fc42c6ac88b69c7f0cd19c");
        cullPaySerials.add("a3b4bb065fb245109af078cdd1fb4771");
        cullPaySerials.add("6cec3e3194434761886ad8e2560331cd");
        for (String reportNo : reportNos) {
            List<WholeCaseBaseEntity> wholeCaseBaseEntities = wholeCaseBaseMapper.getWholeCaseBaseByReport(reportNo);
            if (CollectionUtils.isEmpty(wholeCaseBaseEntities)) {
                LogUtil.info("batchSendClaimVatInfo:案件：{}，不存在！", reportNo);
                continue;
            }

            for (WholeCaseBaseEntity wholeCaseBaseEntity : wholeCaseBaseEntities) {
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setReportNo(wholeCaseBaseEntity.getReportNo());
                paymentItemDTO.setCaseTimes(Integer.valueOf(wholeCaseBaseEntity.getCaseTimes()));
                paymentItemDTO.setPaymentType(PaymentTypeEnum.FEE.getType());
                List<PaymentItemDTO> paymentItemDTOList = paymentItemService.getPaymentItem(paymentItemDTO);
                if (CollectionUtils.isEmpty(paymentItemDTOList)) {
                    LogUtil.info("batchSendClaimVatInfo:案件：{}，直接费用支付信息为空！", reportNo);
                    continue;
                }

                // 排查已经补传的进行防重推送。 暂时先不加了
                for (PaymentItemDTO itemDTO : paymentItemDTOList) {
                    if (!PaymentTypeEnum.FEE.getType().equals(itemDTO.getPaymentType())
                            || cullPaySerials.contains(itemDTO.getIdClmPaymentItem())) {
                        continue;
                    }
                    List<SendPaymentRecord> sendPaymentRecords = sendPaymentRecordMapper.selectByPaySerialNo(itemDTO.getIdClmPaymentItem());
                    if (CollectionUtils.isNotEmpty(sendPaymentRecords)) {
                        LogUtil.info("batchSendClaimVatInfo:案件：{}-{}，已经送收付！", reportNo,itemDTO.getIdClmPaymentItem());
                        continue;
                    }
                    payInfoNoticeThirdPartyCoreSAO.sendClaimVatInfo(itemDTO);
                    cullPaySerials.add(itemDTO.getIdClmPaymentItem());
                }
            }
        }
        LogUtil.info("batchSendClaimVatInfo:结束补传！");
    }


    /**
     * 批量补推理赔费用信息到收付Q13
     * @param paySerialNoS
     * @return
     */
    @Override
    public void batchSendClaimVatInfoByPaySerialNo(List<String> paySerialNoS) {
        LogUtil.info("batchSendClaimVatInfoByPaySerialNo:开始补传：{}", paySerialNoS);
        for (String paySerialNo : paySerialNoS) {
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setIdClmPaymentItem(paySerialNo);
            List<PaymentItemDTO> paymentItemDTOList = paymentItemService.getPaymentItem(paymentItemDTO);
            if (CollectionUtils.isEmpty(paymentItemDTOList)) {
                LogUtil.info("batchSendClaimVatInfoByPaySerialNo:支付序号：{}，直接费用支付信息为空！", paySerialNo);
                continue;
            }
            // 排查已经补传的进行防重推送。 暂时先不加了
            for (PaymentItemDTO itemDTO : paymentItemDTOList) {
                if (!PaymentTypeEnum.FEE.getType().equals(itemDTO.getPaymentType()) && !PaymentTypeEnum.PRE_FEE.getType().equals(itemDTO.getPaymentType())){
                    continue;
                }
                List<SendPaymentRecord> sendPaymentRecords = sendPaymentRecordMapper.selectByPaySerialNo(itemDTO.getIdClmPaymentItem());
                if (CollectionUtils.isNotEmpty(sendPaymentRecords)) {
                    LogUtil.info("batchSendClaimVatInfoByPaySerialNo:案件：{}-{}，已经送收付！", itemDTO.getReportNo(),itemDTO.getIdClmPaymentItem());
                    continue;
                }
                payInfoNoticeThirdPartyCoreSAO.sendClaimVatInfo(itemDTO);
            }
        }
        LogUtil.info("batchSendClaimVatInfoByPaySerialNo:结束补传！");
    }


    /**
     * 退运险数据修改
     *
     * @param reportNos
     * @param isManual
     * @return
     */
    @Override
    public void batchUpdatePolicyPay(List<String> reportNos, String isManual) {
        LogUtil.info("batchUpdatePolicyPay:reportNos={}，isManual={}", reportNos, isManual);
        if (StringUtils.equals("1", isManual) && CollectionUtils.isEmpty(reportNos)) {
            return;
        }

        List<BatchAutoCloseDTO> batchAutoCloseDTOList;
        // 获取需要处理的案件
        if (StringUtils.equals("1", isManual)) {
            // 人工指定
            batchAutoCloseDTOList =
                    batchAutoCloseMapper.getByReportNos(reportNos).stream()
                            .filter(item -> StringUtils.equals(ReportConstant.THREE_SOURCE_TUIYUNXIAN_NAME, item.getThreeSource())
                                    && BigDecimalUtils.compareBigDecimalPlus(item.getPayAmount(), BigDecimal.ZERO))
                            .collect(Collectors.toList());
        } else {
            // 自动查询退货运费险需要补传的案件。
            batchAutoCloseDTOList = batchAutoCloseMapper.getProblemBatchCloseList();
        }
        if(CollectionUtils.isEmpty(batchAutoCloseDTOList)){
            LogUtil.info("batchUpdatePolicyPay,不存在需要更新的需要退运险案件！");
            return ;
        }

        for (BatchAutoCloseDTO batchAutoCloseDTO : batchAutoCloseDTOList) {
            String reportNo = batchAutoCloseDTO.getReportNo();
            BigDecimal payAmount = batchAutoCloseDTO.getPayAmount();
            String batchKey = batchAutoCloseDTO.getPolicyNo() + batchAutoCloseDTO.getPlanCode() + batchAutoCloseDTO.getDutyCode() + batchAutoCloseDTO.getDutyDetailCode();
            LogUtil.info("batchUpdatePolicyPay,开始更新案件：{}，{},key={}",reportNo,payAmount,batchKey);
            List<PolicyPayDTO> policyPayDTOList = policyPayService.getByReportNo(reportNo, 1);
            for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
                if(BigDecimalUtils.isEqual(payAmount,policyPayDTO.getPolicyPay())){
                    LogUtil.info("batchUpdatePolicyPay,案件：{}，金额一致{} ",reportNo,payAmount);
                    continue;
                }
                for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                    for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                        for (DutyDetailPayDTO dutyDetailPayDTO : dutyPayDTO.getDutyDetailPayArr()) {
                            String key = policyPayDTO.getPolicyNo()+planPayDTO.getPlanCode()+dutyPayDTO.getDutyCode()+dutyDetailPayDTO.getDutyDetailCode();
                            LogUtil.audit("matchKey={}",key);
                            if(StringUtils.isBlank(batchKey) || key.equals(batchKey)) {
                                dutyDetailPayDTO.setAutoSettleAmount(payAmount);
                                dutyPayDTO.setSettleAmount(payAmount);
                                dutyPayDTO.setSettleReason("按照保险责任赔付" + payAmount + "元");
                                planPayDTO.setSettleAmount(payAmount);
                                policyPayDTO.setPolicyPay(payAmount);
                            }
                        }
                    }
                }
                policyPayDTO.setPolicySumPay(policyPayDTO.getPolicyPay());
                policyPayDTO.setSettleAmount(policyPayDTO.getPolicyPay());
            }
            clmsPolicyService.updatePolicyPays(policyPayDTOList);
            LogUtil.info("batchUpdatePolicyPay,结束更新案件：{}，{},key={}",reportNo,payAmount,batchKey);
        }
    }
}
