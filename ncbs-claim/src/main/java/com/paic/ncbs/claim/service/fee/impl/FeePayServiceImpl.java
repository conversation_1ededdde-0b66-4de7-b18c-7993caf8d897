package com.paic.ncbs.claim.service.fee.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseProcessMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.ClmsPolicyPrepayDutyDetailMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeCostDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.FeeInvoiceSendPaymentDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.ahcs.*;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleValidateService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.ErrorCode.PAYMENT_ITEM_IS_NULL;

@Service("feePayService")
public class FeePayServiceImpl implements FeePayService {
    @Autowired
    private FeePayMapper feePayMapper;
    @Autowired
    private AhcsCommonService ahcsCommonService;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private SettleValidateService settleValidateService;
    @Autowired
    private CaseProcessMapper caseProcessDao;
    @Autowired
    private PersonAccidentMapper personAccidentMapper;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private PaymentInfoService paymentInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private FileCommonService fileCommonService;
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private ClmsPolicyPrepayDutyDetailMapper clmsPolicyPrepayDutyDetailMapper;

    @Override
    public List<FeePayDTO> getFeePaysForPrepay(FeePayDTO feePay) throws GlobalBusinessException {
        //查询预赔的费用信息
        return Optional.of(getFeePayList(feePay.getReportNo(), feePay.getCaseTimes(), new ArrayList<>()))
                .orElse(new ArrayList<>());

    }


    @Override
    @Transactional
    public void saveFeePay(String reportNo,Integer caseTimes,List<FeePayDTO> feePayDTOS) throws GlobalBusinessException {
        String loginUm = WebServletContext.getUserId();
        //先删除所有费用相关的支付项,费用、发票信息
        clearFee(reportNo,caseTimes);
        if (feePayDTOS == null || CollectionUtils.isEmpty(feePayDTOS)) {
            LogUtil.audit("#费用保存#新费用列表为空reportNo={}",reportNo);
            return;
        }
        LogUtil.audit("删除所有费用相关的支付项,费用、发票信息完成"+reportNo);
        //做fees的拷贝
        FeeVO feeCopy = this.feeBeanCopy(feePayDTOS);
        List<PaymentItemComData> feeItemListAdd = new ArrayList<>();
        List<FeePayDTO> feePayDTOList = feeCopy.getFeePays();
        if (feePayDTOList == null || feePayDTOList.isEmpty()){
            return;
        }
        LogUtil.audit("做fees的拷贝完成"+reportNo);
        Map<String, List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(reportNo);
        feePayDTOList.forEach(feePayDTO -> {
            List<FeeInfoDTO> feeList = feePayDTO.getFeeInfos();
            // 根据费用类型分组 同一个费用类型的下面才要合并
            Map<String, List<FeeInfoDTO>> itemGroupByPolicyNo = feeList.stream().collect(Collectors.groupingBy(FeeInfoDTO::getFeeType));
            itemGroupByPolicyNo.forEach((k,v)->{
                List<FeeInfoDTO> mergeFeeList = new ArrayList<>(v.stream().collect(Collectors.toMap(FeeInfoDTO::getIdClmPaymentInfo, f -> f, (o1, o2) -> {
                    o1.setFeeAmount(o1.getFeeAmount().add(o2.getFeeAmount()));
                    return o1;
                })).values());
                //生成100%的支付项,存入list
                List<PaymentItemComData> items = this.generatePaymentItem(mergeFeeList, feePayDTO, loginUm,k, coinsMap);
                feeItemListAdd.addAll(items);
            });
        });
        List<PaymentItemDTO> paymentItems = paymentItemService.addPaymentItemDataList(feeItemListAdd);
        LogUtil.audit("开始保存费用赔付信息"+reportNo);
        //保存费用赔付信息
        feePayDTOS.forEach(feePayDTO -> this.saveFeePayAll(feePayDTO, loginUm, paymentItems));
        LogUtil.audit("保存费用赔付信息完成"+reportNo);
        //更新保单费用信息
        policyPayService.updatePolicyFee(reportNo, caseTimes);
    }

    @Override
    public void clearFee(String reportNo,Integer caseTimes ){
        //先根据报案号和赔付次数删除所有费用相关的支付项,支付项做的是先删后增
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_FEE);
        paymentItemService.delPaymentItem(paymentItemDTO);

        List<FeeInfoDTO> feeInfoDTOList = feePayMapper.getFeeByClaimType(reportNo,caseTimes,SettleConst.CLAIM_TYPE_PAY,null);
        feeInfoDTOList.forEach(feeInfoDTO -> {
            //删除发票信息
            feePayMapper.removeInvoiceInfo(feeInfoDTO.getIdAhcsFeePay());
            //删除理赔信息
//            feePayMapper.delFeePay(feeInfoDTO.getIdAhcsFeePay());
            feePayMapper.removeFeePay(feeInfoDTO.getIdAhcsFeePay());
        });

    }

    /**
     * 拼装支付项信息,但不保存
     * @return List<PaymentItemComData>
     */
    private List<PaymentItemComData> generatePaymentItem(List<FeeInfoDTO> mergeFeeList, FeePayDTO feePayDTO, String loginUm,
                                                         String feeType, Map<String, List<CoinsureDTO>> coinsMap) throws GlobalBusinessException {
        String policyNo = feePayDTO.getPolicyNo();
        String reportNo = feePayDTO.getReportNo();
        Integer caseTimes = feePayDTO.getCaseTimes();
        Integer subTimes = feePayDTO.getSubTimes();
        String claimType = feePayDTO.getClaimType();
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
        String departmentCode = MapUtils.getString(productMap,"departmentCode");
        String generateNo = commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO, departmentCode);

        //拼装支付项VO
        List<PaymentItemComData> paymentItems = new ArrayList<>();

        //生成批次信息,插入批次表,预赔不需要批次信息,预赔不需要考虑共保情形
        String idAhcsBatch = null;
        if(!SettleConst.CLAIM_TYPE_PRE_PAY.equals(claimType)){
            idAhcsBatch = this.insertBatch(reportNo, caseTimes, loginUm);
        }

        for (FeeInfoDTO feeInfo : mergeFeeList) {
            PaymentInfoDTO paymentInfoDTO = paymentInfoService.getPaymentInfoById(feeInfo.getIdClmPaymentInfo());
            //先查出支付对象信息
            PaymentItemComData paymentItem = new PaymentItemComData();
            if (paymentInfoDTO != null ) {
                BeanUtils.copyProperties(paymentInfoDTO,paymentItem);
                paymentItem.setIdClmPaymentInfo(feeInfo.getIdClmPaymentInfo());
                paymentItem.setFeeType(feeType);
                paymentItem.setPolicyNo(policyNo);
                paymentItem.setReportNo(reportNo);
                paymentItem.setCaseTimes(caseTimes);
                paymentItem.setCaseNo(feePayDTO.getCaseNo());
                paymentItem.setIdClmBatch(idAhcsBatch);
                paymentItem.setCompensateNo(generateNo);
                paymentItem.setClaimType(StringUtils.isEmptyStr(claimType)?SettleConst.CLAIM_TYPE_PAY:claimType);
                if(SettleConst.CLAIM_TYPE_PRE_PAY.equals(claimType)){
                    paymentItem.setPaymentType(SettleConst.PAYMENT_TYPE_FEE_PREPAY);
                } else {
                    paymentItem.setPaymentType(SettleConst.PAYMENT_TYPE_FEE);
                }
                paymentItem.setPaymentTypeName(SettleConst.PAYMENT_TYPE_FEE_NAME);
                paymentItem.setPaymentCurrencyCode(SettleConst.RMB);
                paymentItem.setSubTimes(subTimes);
                if ("Y".equals(paymentItem.getIsMergePay())) {
                    paymentItem.setMergeSign(SettleConst.PREPARE_MERGE);
                } else {
                    paymentItem.setMergeSign(SettleConst.NOT_MERGE);
                }
                paymentItem.setPaymentAmount(feeInfo.getFeeAmount());
                if (BigDecimalUtils.isGreaterOrEqualToZero(paymentItem.getPaymentAmount())) {
                    paymentItem.setCollectPaySign(SettleConst.COLLECTION);
                } else {
                    //当录入负数时，在生成支付项时，支付项目为费用冲减，金额变更为正数
                    paymentItem.setPaymentAmount(feeInfo.getFeeAmount().multiply(new BigDecimal("-1")));
                    paymentItem.setCollectPaySign(SettleConst.COLLECT_SIGN);
                }

                List<CoinsureDTO> coinsureList = coinsMap.get(policyNo);
                if (CollectionUtils.isNotEmpty(coinsureList)) {
                    CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
                    if (Objects.nonNull(coinsureDTO)) {
                        paymentItem.setCoinsuranceMark(BaseConstant.STRING_1);
                        paymentItem.setAcceptInsuranceFlag(coinsureDTO.getAcceptInsuranceFlag());
                        paymentItem.setCoinsuranceCompanyCode(coinsureDTO.getReinsureCompanyCode());
                        paymentItem.setCoinsuranceCompanyName(coinsureDTO.getReinsureCompanyName());
                        paymentItem.setCoinsuranceRatio(coinsureDTO.getReinsureScale());
                        paymentItem.setIsFullPay(BaseConstant.STRING_1);
                        paymentItem.setCoinsuranceActualAmount(paymentItem.getPaymentAmount());
                    }
                }

                paymentItems.add(paymentItem);
            } else {
                //支付信息不存在 要报错
                throw new GlobalBusinessException(ErrorCode.PAYMENT_ITEM_IS_NULL, PAYMENT_ITEM_IS_NULL);
            }
        }
        return paymentItems;

    }

    /**
     * 保存费用赔付信息
     */
    private void saveFeePayAll(FeePayDTO feePay, String loginUm, List<PaymentItemDTO> items) {
        //找到对应的支付项id并set到feeInfo中
        for (FeeInfoDTO info : feePay.getFeeInfos()) {
            for (PaymentItemDTO item : items) {
                if (info.getIdClmPaymentInfo().equals(item.getIdClmPaymentInfo())
                        && feePay.getCaseNo().equals(item.getCaseNo())
                        && info.getFeeType().equals(item.getFeeType())) {
                    info.setIdClmPaymentItem(item.getIdClmPaymentItem());
                    break;
                }
            }
        }
        LogUtil.audit("找到对应的支付项id并set到feeInfo中完成"+feePay.getReportNo());
        //保存
        for (FeeInfoDTO feeInfoDTO : feePay.getFeeInfos()) {
            //生成feepay信息
            FeeCostDTO feeCost = new FeeCostDTO();
            feeCost.setIdClmPaymentItem(feeInfoDTO.getIdClmPaymentItem());
            feeCost.setCreatedBy(loginUm);
            feeCost.setUpdatedBy(loginUm);
            feeCost.setPolicyNo(feePay.getPolicyNo());
            feeCost.setReportNo(feePay.getReportNo());
            feeCost.setCaseNo(feePay.getCaseNo());
            feeCost.setCaseTimes(feePay.getCaseTimes());
            feeCost.setFeeType(feeInfoDTO.getFeeType());
            feeCost.setFeeAmount(feeInfoDTO.getFeeAmount());
            feeCost.setClientName(feeInfoDTO.getClientName());
            feeCost.setIdClmPaymentInfo(feeInfoDTO.getIdClmPaymentInfo());
            feeCost.setSubTimes(feePay.getSubTimes());
            String idAhcsFeePay = UuidUtil.getUUID();
            feeInfoDTO.setIdAhcsFeePay(idAhcsFeePay);
            feeCost.setIdAhcsFeePay(idAhcsFeePay);
            feeCost.setClaimType(StringUtils.isEmptyStr(feePay.getClaimType()) ? ChecklossConst.CONCLUSION_VERIFY_PAY : feePay.getClaimType());
            feePayMapper.insertFeePay(feeCost);
            LogUtil.audit("插入clms_fee_pay表信息完成"+feePay.getReportNo());
            LogUtil.audit("准备保存发票信息"+feePay.getReportNo());
            //发票信息
            InvoiceInfoDTO invoiceInfoDTO =  feeInfoDTO.getInvoiceInfo();
            LogUtil.audit("发票信息参数"+ JSON.toJSONString(invoiceInfoDTO));
            if (invoiceInfoDTO != null) {
                invoiceInfoDTO.setCreatedBy(loginUm);
                invoiceInfoDTO.setUpdatedBy(loginUm);
                invoiceInfoDTO.setIdAhcsFeePay(feeCost.getIdAhcsFeePay());
                invoiceInfoDTO.setIsModifiedFlag("3".equals(feePay.getIsModifiedFlag())?feePay.getIsModifiedFlag():"0");
                feePayMapper.addInvoiceInfo(invoiceInfoDTO);
                LogUtil.audit("保存发票信息完成"+feePay.getReportNo());
            }
        }

    }


    @Override
    public void delPrepayFee(List<String> clmPayItemIdList) throws GlobalBusinessException {
        if (ListUtils.isNotEmpty(clmPayItemIdList)) {
            try {
                ahcsCommonService.batchHandlerTransactional(FeePayMapper.class, clmPayItemIdList, 40, "delPrepayFee", null);
            } catch (Exception e) {
                throw new GlobalBusinessException("删除预赔费用失败");
            }
        }
    }

    private List<FeePayDTO> getFeePayList(String reportNo, Integer caseTimes, List<FeePayDTO> feePaysTemp) throws GlobalBusinessException{
        //合并同一赔案号下的费用信息
        List<FeePayDTO> feePays = new ArrayList<>(feePaysTemp.stream().collect(Collectors.toMap(FeePayDTO::getCaseNo, f -> f, (o1, o2) -> {
            o1.getFeeInfos().addAll(o2.getFeeInfos());
            return o1;
        })).values());

        //查该报案下的保单和承保机构信息
        List<FeePayDTO> feePayList = feePayMapper.getPolicyDepartment(reportNo, caseTimes);

        // 案子有ABC三个保单，fee只录入了B保单，还需要把AC展示出来
        feePayList.forEach(a->{
            if(feePays.stream().noneMatch(b-> (a.getPolicyCerNo() != null && a.getPolicyCerNo().equals(b.getPolicyCerNo())) || a.getPolicyNo().equals(b.getPolicyNo()))){
                feePays.add(a);
            }
        });
        return feePays;
    }


    /**
     *
     * @param reportNo
     * @param caseTimes
     * @param isModifiedFlag 费用发票修改标记 0:未修改 1:待修改 2:修改完成 3:暂存
     * @return
     * @throws GlobalBusinessException
     */
    @Override
    public FeeBigVO getFeePays(String reportNo, Integer caseTimes, String isModifiedFlag, String claimType) throws GlobalBusinessException {

        FeePayDTO feePay = new FeePayDTO();
        feePay.setClaimType(SettleConst.CLAIM_TYPE_PRE_PAY);
        feePay.setReportNo(reportNo);
        feePay.setCaseTimes(caseTimes);

        //查询整案维度正常费用信息
        //根据前端传参区分查询赔款费用/预赔费用信息 如果入参为空默认为赔款费用
        feePay.setClaimType(ObjectUtil.isNotEmpty(claimType) && !"undefined".equals(claimType) ? claimType : SettleConst.CLAIM_TYPE_PAY);
        List<FeePayDTO> normal = feePayMapper.getFeePays(feePay);
        if (CollectionUtils.isNotEmpty(normal)) {
            InvoiceInfoDTO invoiceInfo;
            for (FeePayDTO payDTO : normal) {
                List<FeeInfoDTO> newFeeInfos = new ArrayList<>();
                for (FeeInfoDTO infoDTO : payDTO.getFeeInfos()) {
                    invoiceInfo = new InvoiceInfoDTO();
                    invoiceInfo.setIsModifiedFlag(StrUtil.isEmptyOrUndefined(isModifiedFlag) ? null : isModifiedFlag);
                    invoiceInfo.setIdAhcsFeePay(infoDTO.getIdAhcsFeePay());
                    invoiceInfo = feePayMapper.getInvoiceInfo(invoiceInfo);
                    if (Objects.nonNull(invoiceInfo)){
                        infoDTO.setInvoiceInfo(invoiceInfo);
                        newFeeInfos.add(infoDTO);
                    }
                }
                if ("1".equals(isModifiedFlag) || "3".equals(isModifiedFlag)) {
                    //费用修改菜单，只把要修改的发票费用数据返回
                    payDTO.setFeeInfos(newFeeInfos);
                }
            }
        }
        //查询获取到发票的文件地址 给前端展示文件影像
        getFiledUrl(normal,reportNo);
        //给整案的费用信息加上保单机构信息
        if (caseTimes >= 2 && ListUtils.isEmptyList(normal)) {
            // 重开时查直接理赔费用的保单、承保信息为空，则查询首次赔付的保单及承保信息
            normal = new ArrayList<FeePayDTO>();
            normal = this.getMultipleClaimFeePayList(reportNo, caseTimes, 1, normal);
        } else {
            normal = this.getFeePayList(reportNo, caseTimes, normal);
        }

        if (ListUtils.isNotEmpty(normal)) {
            //设置共保描述
            Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
            for (FeePayDTO fee : normal) {
                fee.setClaimType(SettleConst.CLAIM_TYPE_PAY);
                fee.setSubTimes(null);
                fee.setCoinsuranceDesc(coinsMap.get(fee.getPolicyNo()));
            }
        }

        FeeBigVO vo = new FeeBigVO();
        vo.setNormalFee(normal);

        return vo;
    }



    /**
     * 获取该报案下的保单和承保机构信息，功能等同于feePayMapper.getPolicyDepartment(reportNo, caseTimes);
     * @param reportNo
     * @param caseTimes
     * @return
     * @throws GlobalBusinessException
     */
    @Override
    public List<FeePayDTO> getPolicyDepartment(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return feePayMapper.getPolicyDepartment(reportNo, caseTimes);
    }

    /**
     * 重开申请时如果查直接理赔费用的保单、承保信息为空，则查询首次赔付的保单及承保信息，查到后将返回结果中的赔付次数改为现在的赔付次数，因为保存时会用这个赔付次数保存
     * @param reportNo 报案号
     * @param caseTimes 现在的赔付次数
     * @param oldCaseTimes 之前的赔付次数
     * @return List<FeePayDTO>
     * @throws GlobalBusinessException
     */
    private List<FeePayDTO> getMultipleClaimFeePayList(String reportNo, Integer caseTimes, Integer oldCaseTimes, List<FeePayDTO> feePays) throws GlobalBusinessException{
        LogUtil.audit("#重开申请获取直接理赔费用的保单和承保机构信息为空#入参#reportNo="+ reportNo +",caseTimes="+ caseTimes +",oldCaseTimes="+oldCaseTimes);

        List<FeePayDTO> feePayList = this.getPolicyDepartment(reportNo, oldCaseTimes);

        // 找出该报案下的没有理赔信息的保单，并与有理赔信息的保单返回页面显示 ,flag标识有没有理赔信息的保单
        for (FeePayDTO feePayDTO : feePayList) {
            feePayDTO.setCaseTimes(caseTimes);
            feePays.add(feePayDTO);
        }

        return feePays;
    }


    /**
     * 做fee的拷贝
     * @param fees
     * @return
     */
    private FeeVO feeBeanCopy(List<FeePayDTO> fees) {
        FeeVO fee2 = new FeeVO();
        List<FeePayDTO> list1 = new ArrayList<>();//多个保单
        for (FeePayDTO feePayDTO : fees) {
            List<FeeInfoDTO> list2 = new ArrayList<>();//多个费用
            //一个保单
            for (FeeInfoDTO feeInfo : feePayDTO.getFeeInfos()) {
                //一条费用
                if (null != feeInfo.getFeeAmount()) {
                    FeeInfoDTO temp1 = new FeeInfoDTO();
                    BeanUtils.copyProperties(feeInfo, temp1);
                    list2.add(temp1);
                }
            }
            if (!list2.isEmpty()){
                FeePayDTO feePayDTOTemp = new FeePayDTO();
                BeanUtils.copyProperties(feePayDTO, feePayDTOTemp);
                feePayDTOTemp.setFeeInfos(list2);
                list1.add(feePayDTOTemp);
            }
        }
        if (!list1.isEmpty()){
            fee2.setFeePays(list1);
        }

        return fee2;
    }

    /**
     * 校验费用是否可以保存/更新
     * 保存费用之前需要校验这个案件在待结案、已结案状态下只能进行只读操作，不能保存则直接抛出异常
     * @param reportNo
     * @param caseTimes
     * @throws GlobalBusinessException
     */
    private CaseProcessDTO checkIsCanSave(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        CaseProcessDTO param = new CaseProcessDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
        CaseProcessDTO dto = caseProcessDao.getCaseProcessDTO(param);

        //兼容重开申请的时候录入的费用信息，这时候还没有起案件流程
        if (null != dto) {
            String processStatus = dto.getProcessStatus();
            if (ConfigConstValues.PROCESS_STATUS_PENDING_CASE.equals(processStatus)
                    || ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(processStatus)
                    || ConfigConstValues.PROCESS_STATUS_ZORE_CANCEL.equals(processStatus)
                    || ConfigConstValues.PROCESS_STATUS_CANCELLATION.equals(processStatus)) {
                LogUtil.audit("该案件为待结案或已结案状态，不能添加删除费用信息! processStatus="+ processStatus);
                throw new GlobalBusinessException(ErrorCode.MultipleClaim.ERROR_ENDCASE_STATUS2);
            }
        }

        return dto;
    }

    private String insertBatch(String reportNo,Integer caseTimes, String loginUm){
        return UuidUtil.getUUID();

    }

    @Override
    public FeeInfoDTO getFeePayById(String idAhcsFeePay){
        FeeInfoDTO dto = feePayMapper.getFeePayById(idAhcsFeePay);
        return dto;
    }

    @Transactional
    @Override
    public void removeFeePay(String idAhcsFeePay, String reportNo, Integer caseTimes, FeeInfoDTO dto) throws GlobalBusinessException {
        //保存费用之前需要校验这个案件在待结案、已结案状态下只能进行只读操作
        this.checkIsCanSave(reportNo, caseTimes);
        //删除发票信息
        feePayMapper.removeInvoiceInfo(idAhcsFeePay);
        //删除理赔信息
        feePayMapper.removeFeePay(idAhcsFeePay);
        //更新保单费用信息
        policyPayService.updatePolicyFee(reportNo, caseTimes);
        // 删除赔付表的信息
        //先根据报案号和赔付次数删除所有费用相关的支付项,支付项做的是先删后增
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_FEE);
        paymentItemService.delPaymentItem(paymentItemDTO);
    }

    @Override
    @Transactional
    public void removeFeePayByIdPayinfo(FeePayDTO feePayDTO) throws Exception {
        List<String> idAhcsFeePayList = feePayMapper.getIdFeePayByIdPayinfo(feePayDTO);
        this.removeFeePay(idAhcsFeePayList);
    }

    @Override
    public void removeFeePayByIdPaymentInfo(String reportNo, Integer caseTimes, String idClmPaymentInfo) throws Exception {
        List<String> idAhcsFeePayList = feePayMapper.getIdFeePayByIdPaymentInfo(idClmPaymentInfo);
        this.removeFeePay(idAhcsFeePayList);
        //更新保单费用信息
        policyPayService.updatePolicyFee(reportNo, caseTimes);
    }

    @Override
    @Transactional
    public void removeFeePayByRnCt(String reportNo, Integer caseTimes) throws Exception {
        List<String> idAhcsFeePayList = feePayMapper.getIdFeePayByRnCt(reportNo, caseTimes);
        this.removeFeePay(idAhcsFeePayList);
    }

    private void  removeFeePay(List<String> idAhcsFeePayList) throws Exception {
        if(ListUtils.isNotEmpty(idAhcsFeePayList)){
            //删除发票信息
            ahcsCommonService.batchHandlerTransactional(FeePayMapper.class, idAhcsFeePayList, 40, "removeInvoiceInfoList", null);
            //删除理赔费用信息
            ahcsCommonService.batchHandlerTransactional(FeePayMapper.class, idAhcsFeePayList, 40, "removeFeePayList", null);
        }
    }

    @Transactional
    @Override
    public BigDecimal getSumFeePay(String reportNo, Integer caseTimes) {
        return feePayMapper.getSumFeePay(reportNo, caseTimes);
    }

    @Override
    public BigDecimal getFeePayAmount(FeePayDTO feePay) {
        return feePayMapper.getFeePayAmount(feePay);
    }

    @Override
    public void addInvoiceInfo(InvoiceInfoDTO invoiceInfo) {
        UserInfoDTO user = WebServletContext.getUser();
        invoiceInfo.setCreatedBy(user.getUserCode());
        invoiceInfo.setUpdatedBy(user.getUserCode());
        if (StringUtils.isEmptyStr(invoiceInfo.getIdAhcsInvoiceInfo())){
            feePayMapper.addInvoiceInfo(invoiceInfo);
        }else{
            feePayMapper.modifyInvoiceInfo(invoiceInfo);
        }
    }

    @Override
    public InvoiceInfoDTO getInvoiceInfo(InvoiceInfoDTO invoiceInfo) {
        return feePayMapper.getInvoiceInfo(invoiceInfo);
    }

    @Override
    public FeePaySumVO getFeePayForSum(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        String sum = feePayMapper.getFeePayForSum(reportNo, caseTimes);
        String pre = feePayMapper.getPrePayAmountForSum(reportNo, caseTimes);

        FeePaySumVO vo = new FeePaySumVO();
        if (StringUtils.isEmptyStr(sum)) {
            sum = "0";
        }
        vo.setPolicyPayAmount(new BigDecimal(sum));

        if (StringUtils.isEmptyStr(pre)) {
            pre = "0";
        }
        vo.setPrePayAmount(new BigDecimal(pre));
        vo.setFinalPayAmount(vo.getPolicyPayAmount().subtract(vo.getPrePayAmount()));

        //获取批单模板信息
        //填充模板内容
        String str = feePayMapper.getEndorseTemplate(SettleConst.INDEMNITY_MODE_DENIED);
        String policyStr = "";
        String nameStr = "";

        //查询所有保单信息
        List<String> policyNos = feePayMapper.getAllPolicyByReportNo(reportNo, caseTimes);
        policyStr = this.listTrans2String(policyNos);

        //查询被保人信息
        nameStr = feePayMapper.getInsuredNameByReportNo(reportNo);

        PersonAccidentDTO dto = personAccidentMapper.getPersonAccidentInfo(reportNo, caseTimes.toString(), "report1", null);
        String time = "";
        if (dto != null) {
            time = 	DateUtils.dateFormat(dto.getAccidentTime(), DateUtils.FULL_DATE_STR);
        }

//        nameStr = nameStr + "{}" + time +"{}";
//        policyStr = "{}" + policyStr + "{}";

//        LogUtil.audit("拒付批单模板查询结果：" + str);
//        Reader reader = new StringReader(StringUtils.cancelNull(str));
//        try {
//            HashMap<String, Object> params = new HashMap<>();
//            params.put("clientNames", nameStr);
//            params.put("policyNo", policyStr);
//            params.put("deniedReason", "{}");
//
//            Template temp = new Template(null, reader, null, "UTF-8");
//            StringWriter stringWriter = new StringWriter();
//            temp.process(params, stringWriter);
//            vo.setEndorseTemplate(stringWriter.toString());
//        } catch (Exception e) {
//
//        }

        return vo;
    }

    /**
     * 将list转成string,以英文逗号分隔
     * @param list
     * @return
     * @return String
     */
    private String listTrans2String(List<String> list) {
        StringBuffer str = new StringBuffer();
        for (Object o : list) {
            str.append(o).append(",");
        }
        if (str.length() > 0) {
            str = new StringBuffer(str.substring(0, str.length() - 1));
        }
        return str.toString();
    }


    @Override
    public BatchDTO getBatchByReportNo(String reportNo, Integer caseTimes) {
        return feePayMapper.getBatchByReportNo(reportNo, caseTimes);
    }

    @Override
    public void delFeeByReport(String reportNo, Integer caseTimes, String claimType){
        LogUtil.audit("自动删除费用，delFeeByReport.reportNo={},caseTimes={}", reportNo, caseTimes);
        feePayMapper.delFeeByReport(reportNo, caseTimes, claimType);
    }

    /**
     * 供微服务调用：根据报案号和赔付次数查询赔付明细
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public List<FeeInfoDTO> getFeeByClaimType(String reportNo, Integer caseTimes,
                                              String claimType, Integer subTimes) {
        return feePayMapper.getFeeByClaimType(reportNo, caseTimes, claimType, subTimes);
    }


    @Override
    public List<FeePayDTO> getFeeTypeAmountByReportNo(String reportNo, Integer caseTimes) {
        return feePayMapper.getFeeTypeAmountByReportNo(reportNo, caseTimes);
    }

    @Override
    public List<FeeInfoDTO> getFeePayByParam(FeePayDTO feePay) {
        return feePayMapper.getFeePayByParam(feePay);
    }

    @Override
    public BigDecimal getPolicyFeePayAmount(String reportNo, Integer caseTimes,
                                            String policyNo, String claimType){
        return feePayMapper.getPolicyFeePayAmount(reportNo, caseTimes, policyNo, claimType);
    }

    @Override
    public List<PrePaymentVO> getPolicyPrePayFeeList(String reportNo, Integer caseTimes, Integer subTimes) {
        return feePayMapper.getPolicyPrePayFeeList(reportNo,caseTimes,subTimes);
    }

    @Override
    public List<InvoiceInfoDTO> getInvoiceByFeeIds(List<String> feeIdList) {
        return feePayMapper.getInvoiceByFeeIds(feeIdList);
    }

    @Override
    public List<FeeInfoDTO> getPrePayFeeAmountByParam(FeePayDTO feePay) {
        return feePayMapper.getPrePayFeeAmountByParam(feePay);
    }
    /***
     * 根据文件id获取文件url,文件名称，文件类型
     * @param feepayList
     */
    private void getFiledUrl(List<FeePayDTO> feepayList,String reportNo) {
        if(CollectionUtil.isEmpty(feepayList)){
            return;
        }
        for (FeePayDTO dto : feepayList) {
            dealFileInfo(dto,reportNo);
        }

    }
    private void dealFileInfo(FeePayDTO dto,String reportNo){
        if(ObjectUtil.isEmpty(dto)){
            return;
        }
        if(CollectionUtil.isEmpty(dto.getFeeInfos())){
           return;
        }
        for (FeeInfoDTO feeInfoDTO : dto.getFeeInfos()) {
            dealInvoiceInfo(feeInfoDTO,reportNo);
        }

    }

    /***
     * 处理发票信息 给发票对象赋值
     * 文件名，文件的url 文件类型
     * @param feeInfoDTO
     * @param reportNo
     */
    private void dealInvoiceInfo(FeeInfoDTO feeInfoDTO,String reportNo){
        if(ObjectUtil.isEmpty(feeInfoDTO)){
            return;
        }
        if(ObjectUtil.isEmpty(feeInfoDTO.getInvoiceInfo())){
           return;
        }
        String fileId = feeInfoDTO.getInvoiceInfo().getFileId();
        try {
            FileInfoDTO fileInfoDTO = new FileInfoDTO();
            fileInfoDTO.setReportNo(reportNo);
            fileInfoDTO.setFileId(fileId);
            List<FileDocumentDTO> fileDtoList = fileInfoMapper.getDocumentName(fileInfoDTO);
            if (ObjectUtil.isNotEmpty(fileDtoList)) {
                FileDocumentDTO fileDto = fileDtoList.get(0);
                LogUtil.audit("读取文件名" + fileDto.getDocumentName());
                feeInfoDTO.getInvoiceInfo().setFileName(fileDto.getDocumentName());
                feeInfoDTO.getInvoiceInfo().setDocumentFormat(fileDto.getDocumentFormat());
            }
            UserInfoDTO userDTO = WebServletContext.getUser();
            String url = fileCommonService.getPreviewUrl(fileId,userDTO.getUserName());
            feeInfoDTO.getInvoiceInfo().setFileUrl(url);
            LogUtil.audit("读取文件地址" + url);

        } catch (Exception e) {
            LogUtil.error("读取文件地址异常"+e.getMessage());
        }
    }

    @Transactional
    @Override
    public void feeInvoiceBackResultModify(FeeBigVO feeBigVO) {
        String loginUm = WebServletContext.getUserId();
        List<FeePayDTO> feePayDTOList = feeBigVO.getNormalFee();
        for (FeePayDTO feePayDTO: feePayDTOList){
            for (FeeInfoDTO feeInfoDTO : feePayDTO.getFeeInfos()) {
                //发票信息
                InvoiceInfoDTO invoiceInfoDTO =  feeInfoDTO.getInvoiceInfo();
                LogUtil.audit("发票信息参数"+ JSON.toJSONString(invoiceInfoDTO));
                if (invoiceInfoDTO != null) {
                    invoiceInfoDTO.setUpdatedBy(loginUm);
                    invoiceInfoDTO.setInvoiceNo(invoiceInfoDTO.getInvoiceNo());
                    invoiceInfoDTO.setInvoiceCode(invoiceInfoDTO.getInvoiceCode());
                    invoiceInfoDTO.setInvoiceDate(invoiceInfoDTO.getInvoiceDate());
                    invoiceInfoDTO.setIdAhcsInvoiceInfo(invoiceInfoDTO.getIdAhcsInvoiceInfo());
                    invoiceInfoDTO.setIsModifiedFlag("3");
                    feePayMapper.modifyInvoiceInfo(invoiceInfoDTO);
                }
            }
        }
    }
    @Transactional
    @Override
    public void feeInvoiceBackResultSubmit(String reportNo, Integer caseTimes) {
        String loginUm = WebServletContext.getUserId();
        //只查询暂存的发票，待修改的发票不做处理
        List<FeeInvoiceSendPaymentDTO> feeInvoiceSendPaymentDTOs = feePayMapper.getInvoiceListByReportNo(reportNo,caseTimes);
        InvoiceInfoDTO invoiceInfoDTO ;
        for (FeeInvoiceSendPaymentDTO feeInvoiceSendPaymentDTO : feeInvoiceSendPaymentDTOs) {
            LogUtil.audit("发票信息参数"+ JSON.toJSONString(feeInvoiceSendPaymentDTO));
            invoiceInfoDTO = new InvoiceInfoDTO();
            invoiceInfoDTO.setUpdatedBy(loginUm);
            invoiceInfoDTO.setIdAhcsInvoiceInfo(feeInvoiceSendPaymentDTO.getIdAhcsInvoiceInfo());
            invoiceInfoDTO.setIsModifiedFlag("2");
            feePayMapper.modifyInvoiceInfo(invoiceInfoDTO);
            //送收付费
            FeeInvoiceSendPaymentDTO dto = FeeInvoiceSendPaymentDTO.builder()
                    .batchNo(feeInvoiceSendPaymentDTO.getBatchNo())
                    .businessType("3")
                    .invoiceNo(feeInvoiceSendPaymentDTO.getInvoiceNo())
                    .invoiceCode(feeInvoiceSendPaymentDTO.getInvoiceCode())
                    .invoiceDate(feeInvoiceSendPaymentDTO.getInvoiceDate())
                    .invoiceView(feeInvoiceSendPaymentDTO.getInvoiceView())
                    .build();
            payInfoNoticeThirdPartyCoreSAO.feeInvoiceModifySendPayment(reportNo,dto);
            bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_FEE_INVOICE_MODIFY,null);
            }
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_FEE_INVOICE_MODIFY, "提交", null, WebServletContext.getUserIdForLog());
    }

    /**
     * 根据报案号，赔付次数，预赔次数查询数据
     * @param reportNo
     * @param caseTimes
     * @param subTims
     * @return
     */
    @Override
    public PayPrepayDetailVo getPayPrepayDetail(String reportNo, Integer caseTimes, Integer subTims) {
        PayPrepayDetailVo payPrepayDetailVo = new PayPrepayDetailVo();
        FeePayDTO feePayDTO = new FeePayDTO();
        feePayDTO.setReportNo(reportNo);//报案号
        feePayDTO.setSubTimes(subTims);//预赔次数
        feePayDTO.setCaseTimes(caseTimes);//赔付次数
        //查询保单
        List<PolicyPayDTO> policyPayDTOS  = policyPayService.getByReportNo(reportNo,caseTimes);
        if(CollectionUtils.isNotEmpty(policyPayDTOS)){
            PolicyPayDTO payDTO = policyPayDTOS.get(0);
            payPrepayDetailVo.setPolicyNo(payDTO.getPolicyNo());//保单号
        }
        //查询预赔费用明细
        List<FeeInfoDTO>  feeInfoDTOList = feePayMapper.getFeePayByParam(feePayDTO);
        if(CollectionUtils.isNotEmpty(feeInfoDTOList)){
            FeeInfoDTO feeInfoDTO = feeInfoDTOList.get(0);
            InvoiceInfoDTO invoiceInfoDTO = new InvoiceInfoDTO();
            invoiceInfoDTO.setIdAhcsFeePay(feeInfoDTO.getIdAhcsFeePay());//理赔费用主键
            invoiceInfoDTO.setInvoiceNo(feeInfoDTO.getInvoiceNo());//发票号
            invoiceInfoDTO = feePayMapper.getInvoiceInfo(invoiceInfoDTO);
            feeInfoDTO.setInvoiceInfo(invoiceInfoDTO);//发票信息
        }
        //根据报案号，赔付次数，预赔次数查询预赔责任明细
        List<ClmsPolicyPrepayDutyDetailEntity>  prepayDutyDetailEntityList = clmsPolicyPrepayDutyDetailMapper.getDutyDetailInfo(reportNo,caseTimes,subTims);
        payPrepayDetailVo.setFeeInfoDTOList(feeInfoDTOList);//理赔费用明细
        payPrepayDetailVo.setPrepayDutyDetailDTOList(prepayDutyDetailEntityList);//预赔责任明细
        return payPrepayDetailVo;
    }

}
