package com.paic.ncbs.claim.service.settle.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.BankAccountTypeEnum;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.SmsTypeEnum;
import com.paic.ncbs.claim.common.enums.WholeCaseStatusEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.config.ProductConfig;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.other.SmsInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.ClmsPolicyPrepayDutyDetailMapper;
import com.paic.ncbs.claim.dao.mapper.report.LinkManMapper;
import com.paic.ncbs.claim.dao.mapper.settle.*;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.trace.ClmsPersTraceExpMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.PlanTermContentDTO;
import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicySumDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyEndorsementVO;
import com.paic.ncbs.claim.model.vo.report.LinkManVO;
import com.paic.ncbs.claim.model.vo.settle.*;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.ahcs.AhcsPolicyPlanDataService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.*;
import com.paic.ncbs.claim.service.duty.DutyAttrService;
import com.paic.ncbs.claim.service.duty.DutyDetailPayService;
import com.paic.ncbs.claim.service.duty.DutyPayService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.risk.RiskCheckService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import com.paic.ncbs.claim.service.settle.*;
import com.paic.ncbs.claim.service.settle.factor.interfaces.ClaimSettleService;
import com.paic.ncbs.claim.service.trace.impl.PersonTraceServiceImpl;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;

@Slf4j
@Transactional
@RefreshScope
@Service("policyPayService")
public class PolicyPayServiceImpl implements PolicyPayService {

    @Autowired
    private MaxPayService maxPayService;

    @Autowired
    private DutyAttrService dutyAttrService;

    @Autowired
    private AutoSettleService autoSettleService;

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Resource(name = "endorsementService")
    private EndorsementService endorsementService;

    @Autowired
    @Lazy
    private SettleValidateService settleValidateService;

    @Resource(name = "planPayService")
    private PlanPayService planPayService;

    @Resource(name = "dutyPayService")
    private DutyPayService dutyPayService;

    @Resource(name = "dutyDetailPayService")
    private DutyDetailPayService dutyDetailPayService;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private PolicyPayMapper policyPayDao;

    @Autowired
    private MedicalBillService medicalBillService;

    @Autowired
    private PlanPayMapper planPayDao;

    @Autowired
    private DutyPayMapper dutyPayDao;

    @Autowired
    private DutyDetailPayMapper dutyDetailPayDao;

    @Autowired
    private SettleBatchMapper settleBatchDao;

    @Autowired
    private CaseBaseService caseBaseService;

    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private SettlePaymentService settlePaymentService;
    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private PolicyBatchMapper policyBatchMapper;

    @Autowired
    private PaymentInfoService paymentInfoService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private LinkManMapper linkManMapper;

    @Autowired
    private FeePayService feePayService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Autowired
    private PaymentInfoMapper paymentInfoMapper;

    @Autowired
    private AhcsPolicyPlanDataService ahcsPolicyPlanDataService;
    @Autowired
    private ClmsSecondUnderwritingService secondUnderwritingService;
    @Autowired
    private RiskCheckService riskCheckService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private RiskPropertyPayService riskPropertyPayService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private  DutyAttributeService dutyAttributeService;
    @Autowired
    private ClmBatchService clmBatchService;
    @Autowired
    private ClmsCommonVerifyService clmsCommonVerifyService;
    @Autowired
    private ClaimCommonPaymentService claimCommonPaymentService;
    @Autowired
    private ClmsReduceAmountService clmsReduceAmountService;
    @Autowired
    private ClmsCommonPolicyService clmsPolicyService;
    @Autowired
    private ClmsEveryMonthPayTimesCheckService clmsEveryMonthPayTimesCheckService;
    @Autowired
    private ClmsQueryPolicyAllInfoService clmsQueryPolicyAllInfoService;
    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;
    @Autowired
    private DiagnoseHospitalBillAssociationMapper diagnoseHospitalBillAssociationMapper;
    @Autowired
    private ProductConfig productConfig;

    @Autowired
    private DutySettleRuleFilterService dutySettleRuleFilterService;
    @Autowired
    private ClaimSettleService claimSettleService;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Value("${settleproduct.productList:N}")
    private List<String> productCodeLists;
    /**
     *理算新老逻辑开关
     */
    @Value("${switch.setttleOnOff:N}")
    private String  setttleNewOrOld;

    @Autowired
    private CaseBaseMapper caseBaseMapper;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;

    @Autowired
    private ClmsPolicyPrepayDutyDetailMapper policyPrepayDutyDetailMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private PersonTraceServiceImpl psersonTraceService;

    @Autowired
    private SmsInfoMapper smsInfoMapper;

    @Autowired
    private CacheService cacheService;

    @Override
    @Transactional
    public List<PolicyPayDTO>  initPolicyPayInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        String productCode = clmsQueryPolicyAllInfoService.getPolicyProductCode(reportNo,caseTimes);

//        if(productCodeLists.contains(productCode)){
//            //重构逻辑
//            if(Objects.equals("Y",setttleNewOrOld)){
//                ClaimCaseDTO bo = claimSettleService.settle(reportNo,caseTimes);
//                if(Objects.isNull(bo)){
//                   return  null;
//                }
//                List<PolicyPayDTO> policyPayDTOList=bo.getPolicyPayDTOList();
//                return policyPayDTOList;
//            }else{
//                List<PolicyPayDTO> policyPayDTOList =delaData(reportNo,caseTimes);
//                return policyPayDTOList;
//            }
//
//        }else{
//            List<PolicyPayDTO> policyPayDTOList =delaData(reportNo,caseTimes);
//            return policyPayDTOList;
//        }
//
        if(productCodeLists.contains(productCode)){
            return delaData(reportNo,caseTimes);
        }else {
            ClaimCaseDTO bo = claimSettleService.settle(reportNo,caseTimes);
            if(Objects.isNull(bo)){
                return  null;
            }
            return bo.getPolicyPayDTOList();
        }


    }

    private  List<PolicyPayDTO> delaData(String reportNo,Integer caseTimes) {

        LogUtil.audit("#-----理算初始化开始-----");
        LogUtil.audit("--初始化赔付信息-校验是否已初始化,报案号:{},赔付次数:{}", reportNo, caseTimes);
        if (this.checkExists(reportNo, caseTimes)) {
            LogUtil.audit("--已经初始化过理算数据,不需要继续初始化,报案号:{},赔付次数:{}", reportNo, caseTimes);
            return null;
        }

        String lockValue = UuidUtil.getUUID();
        String lockName = String.format(BaseConstant.POLICY_PAY_INIT_LOCK,reportNo);
        if(!redisService.tryLock(lockName,lockValue)){
            LogUtil.audit("初始化中，请稍侯");
            return null;
        }

        LogUtil.audit("--初始化赔付信息-从抄单表查询数据,报案号:{},赔付次数:{}", reportNo, caseTimes);
        List<PolicyPayDTO> copyPolicyPays = selectFromPolicyCopy(reportNo, caseTimes);
        if (ListUtils.isEmptyList(copyPolicyPays)) {
            LogUtil.audit("--初始化赔付信息结束,报案号:{},赔付次数:{},保单列表为空，无需初始化自动理算", reportNo, caseTimes);
            return null;
        }

        LogUtil.audit("--初始化赔付信息-默认不发送赔付短信,报案号：" + reportNo);
        notSendMessage(reportNo,caseTimes);

        LogUtil.audit("--初始化赔付信息-插入理算批次表,报案号：" + reportNo);
        String idClmBatch = clmBatchService.insertBatch(reportNo, caseTimes, SettleConst.SETTLE_STATUS_ON);

        LogUtil.audit("--初始化赔付信息-生成赔付信息uuid,报案号：" + reportNo);
        SettleHelper.setPolicyPays(copyPolicyPays, idClmBatch);

        LogUtil.audit("--初始化赔付信息-计算剩余赔付额,报案号：" + reportNo);
        setRollback(copyPolicyPays);
        maxPayService.initPoliciesPayMaxPay(copyPolicyPays, null);

        LogUtil.audit("--初始化赔付信息-设置预估、预赔、费用,报案号：" + reportNo);
        this.setPolicyInfo(copyPolicyPays);

        LogUtil.audit("--初始化赔付信息-初始化责任明细属性,报案号：{},保单信息={}",reportNo, JsonUtils.toJsonString(copyPolicyPays));
        dutyAttrService.initDetailAttributes(copyPolicyPays);
        //核责
        dutySettleRuleFilterService.dutyRuleFilter(reportNo,caseTimes,copyPolicyPays);
        try {
            LogUtil.audit("--初始化赔付信息-自动理算责任明细理赔金额,报案号：" + reportNo);
            autoSettleService.getSettleAmount(reportNo, caseTimes, copyPolicyPays);
        } catch (Exception e) {
            LogUtil.info(String.format("#自动理算失败,报案号:reportNo:%s,赔付次数:%s", reportNo, caseTimes), e);
        }
        LogUtil.audit("--初始化赔付信息-统计保单理算金额总和,报案号：" + reportNo);
        SettleHelper.initSettleAmount(copyPolicyPays);

        LogUtil.audit("--初始化赔付信息-插入保单赔付批次明细表,报案号：" + reportNo);
        this.insertPolicyBatch(copyPolicyPays, idClmBatch);
        LogUtil.audit("--初始化赔付信息-删除保单赔付信息,报案号：" + reportNo);
        policyPayDao.deletePolicyPays(reportNo, caseTimes);

        LogUtil.audit("--初始化赔付信息-插入理算赔付数据,报案号={} ,保单赔付信息={}" , reportNo,JsonUtils.toJsonString(copyPolicyPays));
        this.insertPayInfo(copyPolicyPays);

        LogUtil.audit("--初始化赔付信息-自动批单,报案号：" + reportNo);
        endorsementService.autoGenerateEndorsement(reportNo, caseTimes, copyPolicyPays);

        LogUtil.audit("--初始化赔付信息-自动生成支付项（赔付信息）,报案号：" + reportNo);
        settlePaymentService.autoGeneratePaymentItems(reportNo,caseTimes,copyPolicyPays,idClmBatch);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisService.removeLock(lockName,lockValue);
            }
        });

        LogUtil.audit("#-----理算初始化结束-----, 报案号: {}, policyPayList: {}", reportNo, JSON.toJSONString(copyPolicyPays));
        return copyPolicyPays;
    }

    @Override
    @Transactional
    public void reCalculateSettleAmount(String reportNo, Integer caseTime){
        String productCode = clmsQueryPolicyAllInfoService.getPolicyProductCode(reportNo,caseTime);
//        LogUtil.info("重新理算判断走新老逻辑报案号={},产品编码={},配置的产品列表={}",reportNo,productCode,JsonUtils.toJsonString(productCodeLists));
//        if(productCodeLists.contains(productCode)){
//            if(Objects.equals("Y",setttleNewOrOld)){
//                LogUtil.info("开关打开重新理算判断走新逻辑报案号={},产品编码={},开关={}",reportNo,productCode,setttleNewOrOld);
//               claimSettleService.reSettle(reportNo,caseTime);
//            }else{
//                LogUtil.info("开关没有开重新理算判断走老逻辑报案号={},产品编码={},开关={}",reportNo,productCode,setttleNewOrOld);
//                reCalculate(reportNo,caseTime);
//            }
//        }else{
//            LogUtil.info("没有配置产品重新理算判断走老逻辑报案号={},产品编码={},开关={}",reportNo,productCode,setttleNewOrOld);
//            reCalculate(reportNo,caseTime);
//        }

        if(productCodeLists.contains(productCode)){
            LogUtil.info("老逻辑报案号={},产品编码={}",reportNo,productCode);
            reCalculate(reportNo,caseTime);
        }else {
            claimSettleService.reSettle(reportNo,caseTime);
        }

    }

    /**
     *
     * @param reportNo
     * @param caseTime
     */
    private void reCalculate(String reportNo, Integer caseTime) {
        LogUtil.audit("#-----重新理算开始-----");
        List<PolicyPayDTO> policyList = policyPayDao.selectByReportNo(reportNo, caseTime);
        LogUtil.audit("--重新理算-查询险种责任赔付信息，并重新设置责任属性,报案号：" + reportNo);
        for (PolicyPayDTO policy : policyList) {
            planPayService.reQueryDutyDetail(policy);
        }
        //核责
        dutySettleRuleFilterService.dutyRuleFilter(reportNo,caseTime,policyList);
        /*LogUtil.audit("--重新理算-分摊费用至险种责任,报案号：" + reportNo);
        settleService.distributeFee(policyList, SettleConst.CLAIM_TYPE_PAY, null);*/
        LogUtil.audit("--重新理算-设置预估、预赔、费用,报案号：" + reportNo);
        this.setPolicyInfo(policyList);
        LogUtil.audit("--重新理算-计算剩余赔付额,报案号：" + reportNo);
        setRollback(policyList);
        maxPayService.initPoliciesPayMaxPay(policyList, null);
        LogUtil.audit("--重新理算-重新计算理算金额,报案号：" + reportNo);
        autoSettleService.getSettleAmount(reportNo,caseTime,policyList);
        printLog(policyList);
        LogUtil.audit("--重新理算-统计保单理算金额总和,报案号：" + reportNo);
        dutyAttributeService.getDutyAttributeDTO(policyList,reportNo);
        SettleHelper.initSettleAmount(policyList);
        printLog(policyList);
        LogUtil.audit("--重新理算-更新理算赔付数据,报案号：" + reportNo);
        clmsPolicyService.updatePolicyPays(policyList);
        LogUtil.audit("--重新理算-自动批单,报案号：" + reportNo);
        endorsementService.autoGenerateEndorsement(reportNo, caseTime, policyList);
        LogUtil.audit("--重新理算-自动生成支付项（赔付信息）,报案号：" + reportNo);
        String idClmBatch = clmBatchService.insertBatch(reportNo, caseTime, SettleConst.SETTLE_STATUS_ON);
        settlePaymentService.autoGeneratePaymentItems(reportNo,caseTime,policyList,idClmBatch);
        LogUtil.audit("#-----重新理算结束-----, 报案号: {}, policyPayList: {}", reportNo, JSON.toJSONString(policyList));

    }

    public void printLog(List<PolicyPayDTO> policyList){
        LogUtil.audit("-------------------------------------");
        policyList.forEach(policy->{
            LogUtil.audit("保单：{} 赔付金额：{}",policy.getPolicyNo(),policy.getSettleAmount());
            List<PlanPayDTO> plans = policy.getPlanPayArr();
            if (plans != null && !plans.isEmpty()){
                plans.forEach(plan->{
                    LogUtil.audit("险种：{} 赔付金额：{}",plan.getPlanName(),plan.getSettleAmount());
                    List<DutyPayDTO> dutys = plan.getDutyPayArr();
                    if (dutys != null && !dutys.isEmpty()){
                        dutys.forEach(duty->{
                            LogUtil.audit("责任:{} 赔付金额：{}",duty.getDutyName(),duty.getSettleAmount());
                            List<DutyDetailPayDTO> details = duty.getDutyDetailPayArr();
                            if (details != null && !details.isEmpty()){
                                details.forEach(detail->{
                                    LogUtil.audit("责任明细:{} 赔付金额：{},手动赔付金额：{},协议赔付金额：{}",
                                            detail.getDutyDetailName(),detail.getAutoSettleAmount(),detail.getSettleAmount(),detail.getProtocolAmount());
                                });
                            }
                        });
                    }
                });
            }
        });
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void initPolicyForRefuse(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        try {
            LogUtil.audit("#初始化拒赔付信息-入参reportNo={},caseTimes={}", reportNo, caseTimes);
            if (checkExists(reportNo, caseTimes)) {
                LogUtil.audit("#拒赔付信息已经存在,不需要初始化");
                return;
            }
            List<PolicyPayDTO> copyPolicyPays = this.selectFromPolicyCopy(reportNo, caseTimes);
            String idAhcsBatch = clmBatchService.insertBatch(reportNo, caseTimes, SettleConst.SETTLE_STATUS_ON);
            SettleHelper.setPolicyPays(copyPolicyPays, idAhcsBatch);
//            settleService.distributeFee(copyPolicyPays, SettleConst.CLAIM_TYPE_PAY, null);
            policyPayDao.deletePolicyPays(reportNo, caseTimes);

            setPolicyInfo(copyPolicyPays);
            insertPolicyBatch(copyPolicyPays, idAhcsBatch);
            insertPayInfo(copyPolicyPays);
            LogUtil.audit("#初始化拒赔付信息-完成");
        } catch (Exception e) {
            LogUtil.info("#初始化拒赔付信息失败,报案号:{},赔付次数{}", reportNo, caseTimes, e);
        }

    }


    @Override
    public void handlePolicyPaysByConclusion(String reportNo, Integer caseTimes, String conclusion, Boolean clearPolicyFee) throws GlobalBusinessException {
        LogUtil.audit("#处理赔付数据-报案号:{},赔付次数:{},处理结论:{}", reportNo, caseTimes, conclusion);
        List<PolicyPayDTO> policyPays = handlePolicyPays(reportNo, caseTimes, null, conclusion, clearPolicyFee);
        handlePolicyBatchForRefuse(reportNo, caseTimes,  policyPays);
        LogUtil.audit("#处理赔付数据成功-报案号:{},赔付次数:{}", reportNo, caseTimes);
    }

    /**
     * 理算暂存/保存
     */
    @Override
    @Transactional
    public void addSettles(SettlesFormVO settlesFormVO) {
        TaskInfoDTO taskInfoDTO = taskInfoMapper.checkWorkflow(settlesFormVO.getReportNo(), settlesFormVO.getCaseTimes(), BpmConstants.OC_MANUAL_SETTLE,
                BpmConstants.TASK_STATUS_PENDING);
        if (taskInfoDTO == null) {
            throw new GlobalBusinessException("理算任务不存在或者已处理！");
        }

        UserInfoDTO u = WebServletContext.getUser();
        settlesFormVO.setUserId(u.getUserCode());
        settlesFormVO.setUpdatedBy(u.getUserCode());
        List<PolicyPayDTO> policyPays = settlesFormVO.getPolicyPayArr();

        riskPropertyService.getRiskPropertyPlan(policyPays);

        //保存减损金额
        clmsReduceAmountService.isSaveReduceAmount(settlesFormVO.getReportNo(),settlesFormVO.getCaseTimes(),settlesFormVO.getReduceAmount());
        //设置创建人、修改人等必要信息
        initPolicyPay(policyPays);
        dutyAttributeService.getDutyAttributeDTO(policyPays,settlesFormVO.getReportNo());
        //计算保单理算金额（责任理算金额总和）------理算金额到责任明细
        SettleHelper.initSettleAmount(policyPays);
        //分摊费用至险种责任
        //settleService.distributeFee(policyPays, SettleConst.CLAIM_TYPE_PAY, null);
        //设置保单总预估金额、总预赔、初始化费用金额
        setPolicyInfo(policyPays);
        //更新保单、险种、责任、责任明细的案件理算信息
        clmsPolicyService.updatePolicyPaysForAdmin(policyPays, settlesFormVO.getReportNo(), settlesFormVO.getCaseTimes(), settlesFormVO.getSettleAutoSubmit());
        //更新人伤估损金额
        psersonTraceService.updateTraceExp(settlesFormVO.getClmsPersTraceExpVOList());
        //更新批次表、保单赔付批次明细表
        updateSettleBatch(settlesFormVO, policyPays, false);
        //合并批单
        mergeEndorsementInfo(settlesFormVO);
        //更新支付项
        claimCommonPaymentService.updatePaymentItems(settlesFormVO.getPaymentItemArr(),settlesFormVO.getPolicyPayArr(),settlesFormVO.getReportNo(),settlesFormVO.getCaseTimes());
        //更新联系人
        updateLinkMan(settlesFormVO.getLinkManList());
    }

//    public static void main(String[] args) {
//        String s = "[{\"detailArr\":[{\"dutyName\":\"院外药店药品费用保险金\",\"dutyAmount\":20,\"adjustmentTextArea\":\"药物责任明细: 合计赔付20.00元<br> 2025-01-23号理算金额： 200.00×30% =60.00 (理算金额超日限额本日按照限额给付20.00元) <br>药物责任明细：合计赔付11.00元<br>药物责任明细：合计赔付2000.00元<br>药物责任明细: 合计赔付20.00元<br> 2025-01-23号理算金额： 200.00×30% =60.00 (理算金额超日限额本日按照限额给付20.00元) <br>药物责任明细：合计赔付11.00元<br>药物责任明细: 合计赔付20.00元<br> 2025-01-23号理算金额： 200.00×30% =60.00 (理算金额超日限额本日按照限额给付20.00元) <br>\",\"dutyCode\":\"C00369\"}],\"policyNo\":\"2250008080000887288\"}]";
//        List<PolicyEnendorsementVO> policyEnendorsementVOS = JSON.parseArray(s, PolicyEnendorsementVO.class);
//        if(CollectionUtils.isNotEmpty(policyEnendorsementVOS)){
//            List<DutyEndorsementVO> detailArr = policyEnendorsementVOS.get(0).getDetailArr();
//            if(CollectionUtils.isNotEmpty(detailArr)){
//                BigDecimal reduce = detailArr.stream()
//                        .map(DutyEndorsementVO::getDutyAmount)
//                        .filter(Objects::nonNull)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                System.out.println(reduce);
//            }
//        }
//    }

    /**
     * 理算发送
     */
    @Override
    @Transactional
    public void sendSettles(SettlesFormVO settlesFormVO) throws GlobalBusinessException {
        String userId = WebServletContext.getUserId();
        settlesFormVO.setUserId(userId);
        settlesFormVO.setUpdatedBy(userId);
        String reportNo = settlesFormVO.getReportNo();
        Integer caseTimes = settlesFormVO.getCaseTimes();
        List<PaymentItemComData> paymentItems = settlesFormVO.getPaymentItemArr();
        EndorsementDTO endorsement = settlesFormVO.getEndorInfo();
        long start = System.currentTimeMillis();
        List<PolicyPayDTO> policyPays = settlesFormVO.getPolicyPayArr();
        LogUtil.audit("#人工理算查询保单信息耗时:{},报案号:{},赔付次数:{}", System.currentTimeMillis() - start, reportNo, caseTimes);
        // 校验当前案件状态是否可以提交理算
        TaskInfoDTO taskInfoDTO = taskInfoMapper.checkWorkflow(reportNo, caseTimes, BpmConstants.OC_MANUAL_SETTLE,
                BpmConstants.TASK_STATUS_PENDING);
        if (taskInfoDTO == null) {
            throw new GlobalBusinessException("理算任务不存在或者已处理！");
        }
        //校验当前流程是否存在冲突
        bpmService.processCheck(reportNo,BpmConstants.OC_MANUAL_SETTLE,BpmConstants.OPERATION_SUBMIT);
        //校验理算环节赔款的赔付责任必须包含已预赔的赔付责任，且各责任的理算赔付金额必须大于等于该责任的已预赔金额
        this.checkSettlePrePayAmount(settlesFormVO);
//        Integer count = policyPrepayDutyDetailMapper.checkPrePayAmount(reportNo,caseTimes);
//        if(count != null && count > 0){
//            throw new GlobalBusinessException("赔付责任手动理算金额小于该责任的已预赔金额！");
//        }
        riskPropertyService.getRiskPropertyPlan(policyPays);
        /*  delete by zjtang 取消旧流程冲突校验逻辑
        //校验是否有在途的未决审批
        if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
            throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能理算提交！");
        }
         */
        //整案信息
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
        String indemnityModel = "";
        if (null != wholeCaseBaseDTO) {
            indemnityModel = wholeCaseBaseDTO.getIndemnityModel();
        }
        //校验每月赔付次数是否超限制,通融不校验此项
        if (!"6".equals(indemnityModel)) {
            clmsEveryMonthPayTimesCheckService.checkPayTimes(policyPays,reportNo);
        }
        // 校验个人的被保险人关系是否已填，如未填不可提交给提示
        checkClientRelation(reportNo, caseTimes);
        // 校验支付信息中领款人必做在案件中存在
        checkPayeeInfo(reportNo,paymentItems);
        // 判断案件是有有二核未完成的任务
        checkSecondUWIsFinish(reportNo, caseTimes);

        //校验账单信息
        List<MedicalBillInfoVO> medicalBillInfoVOS = checkBillInfo(reportNo, caseTimes);

        //校验被保人有无二核在途案件
        riskCheckService.checkOtherCaseUnfinishedTask(reportNo, caseTimes);

        //设置创建人、修改人等必要信息
        initPolicyPay(policyPays);
        dutyAttributeService.getDutyAttributeDTO(policyPays,settlesFormVO.getReportNo());
        //计算保单理算金额（责任理算金额总和）
        //todo 重新理算逻辑 hyf
        SettleHelper.initSettleAmount(policyPays);
        LogUtil.audit("计算保单理算金额完成");
        //校验仅健康险允许提交理算金额为0
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("reportNo",reportNo);
        List<Map<String,String>> mapList = ahcsPolicyInfoMapper.getProductInfo(paramMap);
        String productClass = null;
        if (!ListUtils.isEmptyList(mapList)) {
            productClass = mapList.get(0).get("productClass");
        }
        BigDecimal sumSettleAmount = BigDecimal.ZERO;
        for (PolicyPayDTO policyPayInfo : policyPays) {
            sumSettleAmount = sumSettleAmount.add(policyPayInfo.getSettleAmount());
        }
        if(!"02".equals(productClass) && sumSettleAmount.compareTo(BigDecimal.ZERO) == 0){
            throw new GlobalBusinessException("非健康险理算金额不能为0！");
        }

        boolean endorseAmountCheck = false;
        try {
            List<PolicyEnendorsementVO> policyEnendorsementVOS = JSON.parseArray(settlesFormVO.getEndorInfo().getEndorsementRemark(), PolicyEnendorsementVO.class);
            if (CollectionUtils.isNotEmpty(policyEnendorsementVOS)) {
                List<DutyEndorsementVO> detailArr = policyEnendorsementVOS.get(0).getDetailArr();
                if (CollectionUtils.isNotEmpty(detailArr)) {
                    BigDecimal reduce = detailArr.stream()
                            .map(DutyEndorsementVO::getDutyAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (reduce.compareTo(settlesFormVO.getPolicyPayArr().get(0).getSettleAmount()) != 0) {
                        endorseAmountCheck = true;
                    }
                }
            }
        }catch (Exception e){
            log.warn("理算提交批文与理算金额校验异常，reportNo：{}，message：{}", reportNo, e.getMessage(), e);
        }
        if(endorseAmountCheck){
            throw new GlobalBusinessException("批单内赔付金额与理算金额不一致！");
        }

        //人工理算校验
        checkSettle(reportNo, caseTimes, policyPays, paymentItems, endorsement);
        //保存减损金额
        clmsReduceAmountService.isSaveReduceAmount(settlesFormVO.getReportNo(),settlesFormVO.getCaseTimes(),settlesFormVO.getReduceAmount());
        //设置保单总预估金额、总预赔、初始化费用金额
        setPolicyInfo(policyPays);
        LogUtil.audit("更新更新保单险种责任责任明细信息开始");
        //更新保单、险种、责任、责任明细信息
        //todo 重新理算逻辑 hyf
        clmsPolicyService.updatePolicyPaysForAdmin(policyPays, reportNo, caseTimes, settlesFormVO.getSettleAutoSubmit());
        //更新人伤估损金额
        psersonTraceService.updateTraceExp(settlesFormVO.getClmsPersTraceExpVOList());
        //更新批次表、保单赔付批次明细表
        updateSettleBatch(settlesFormVO, policyPays, true);
        //合并批单
        mergeEndorsementInfo(settlesFormVO);
        //更新支付项
        claimCommonPaymentService.updatePaymentItems(settlesFormVO.getPaymentItemArr(),settlesFormVO.getPolicyPayArr(),settlesFormVO.getReportNo(),settlesFormVO.getCaseTimes());
        //更新联系人
        updateLinkMan(settlesFormVO.getLinkManList());
        //工作流
        bpmService.completeTask_oc(reportNo,caseTimes,BpmConstants.OC_MANUAL_SETTLE);
        startSettleReview(reportNo,caseTimes,settlesFormVO.getSettleAutoSubmit(),settlesFormVO.getVerifyApproved());
        //流程状态
        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.WAIT_VERIFICATION.getCode());
        //自动写核赔记录
        clmsCommonVerifyService.insertVerify(reportNo,caseTimes,userId);
        if("Y".equals(settlesFormVO.getSettleAutoSubmit())){
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_MANUAL_SETTLE, "自动提交", null, ConstValues.SYSTEM_UM);
        }else {
            //操作记录
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_MANUAL_SETTLE, "提交", null);
        }
        //更新ES同步时间
        caseBaseService.updateEsUpdatedDate(reportNo,caseTimes);
        //案件状态更新为已理算
        this.updateCaseStatus(reportNo,caseTimes, WholeCaseStatusEnum.WHOLE_CASE_STATUS_TWO.getCode());

        //保存发送短信内容
        this.saveTextMessageContent(settlesFormVO);
    }

    //保存发送短信内容
    private void saveTextMessageContent(SettlesFormVO settlesFormVO) {
        if (settlesFormVO.getMessageText()!=null){
            SmsInfoDTO smsInfoDTO = new SmsInfoDTO();
            smsInfoDTO.setCreatedBy(settlesFormVO.getUserId());
            smsInfoDTO.setCreatedDate(new Date());
            smsInfoDTO.setUpdatedBy(settlesFormVO.getUserId());
            smsInfoDTO.setUpdatedDate(new Date());
            smsInfoDTO.setSmsContent(settlesFormVO.getMessageText());
            smsInfoDTO.setMobileNo(settlesFormVO.getLinkManList().get(0).getLinkManTelephone());
            smsInfoDTO.setSendUser(settlesFormVO.getUserId());
            smsInfoDTO.setUserId(settlesFormVO.getUserId());
            smsInfoDTO.setReportNo(settlesFormVO.getReportNo());
            smsInfoDTO.setSmsStatus(Constants.SMS_STATUS_INIT);
            smsInfoDTO.setIdAhcsSmsInfo(UuidUtil.getUUID());
            smsInfoMapper.addSmsInfo(smsInfoDTO);
        }
    }

    //责任层级理算金额校验
    private void checkSettlePrePayAmount(SettlesFormVO settlesFormVO) {
        String reportNo = settlesFormVO.getReportNo();
        Integer case_times = settlesFormVO.getCaseTimes();
        List<PolicyPayDTO> policyPays = settlesFormVO.getPolicyPayArr();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
        Map<String,BigDecimal> prepayMap = new HashMap<>();
        List<DutyPayDTO> msgPayArr = new ArrayList<>();
        List<DutyPayDTO> nullPayArr = new ArrayList<>();
        //1.拆分数据，获取所有前端传入的责任数据
        dutyPayArr = this.getdutyDetailList(policyPays);
        //2.从数据库获取预赔金额信息
        prepayMap = this.getprepayAmountMap(reportNo,case_times);
        String msg = "";
        //3.开始校验理算金额小于等于预赔金额
        if(dutyPayArr!=null && dutyPayArr.size()>0){
            if(ObjectUtil.isNotEmpty(prepayMap)) {
                Map<String, BigDecimal> finalPrepayMap = prepayMap;
                msgPayArr = dutyPayArr.stream()
                        .filter(dto -> ObjectUtil.isNotEmpty(dto.getSettleAmount()) && ObjectUtil.isNotEmpty(finalPrepayMap.get(dto.getDutyName())) && dto.getSettleAmount().compareTo(finalPrepayMap.get(dto.getDutyName())) < 0)
                        .collect(Collectors.toList());
                nullPayArr = dutyPayArr.stream().filter(dto -> ObjectUtil.isEmpty(dto.getSettleAmount()) || dto.getSettleAmount().compareTo(BigDecimal.ZERO)==0).collect(Collectors.toList());
                List<String> prepays = prepayMap.keySet().stream().collect(Collectors.toList());
                List<String> settles = dutyPayArr.stream().filter(dto -> ObjectUtil.isNotEmpty(dto.getSettleAmount())).map(DutyPayDTO::getDutyName).collect(Collectors.toList());
                if(ObjectUtil.isNotEmpty(msgPayArr)){
                    for (int i = 0; i < msgPayArr.size(); i++) {
                        msg += msgPayArr.get(i).getDutyName()+" 赔付责任手动理算金额小于该责任的已预赔金额！";
                        if(i!=msgPayArr.size()-1 && msgPayArr.size()>1){
                            msg += "\n";
                        }
                    }
                }
                if(ObjectUtil.isNotEmpty(nullPayArr) && nullPayArr.size()==dutyPayArr.size()){
                    msg += "理算金额未录入，不允许提交理算";
                }
                if(ObjectUtil.isNotEmpty(prepays) && ObjectUtil.isNotEmpty(settles) && !settles.containsAll(prepays)){
                    if(ObjectUtil.isNotEmpty(msg)){
                        msg += "\n"+"赔付责任必须包含已预赔的赔付责任!";
                    }else{
                        msg += "赔付责任必须包含已预赔的赔付责任!";
                    }
                }
            }
        }
        if(ObjectUtil.isNotEmpty(msg)){
            throw new GlobalBusinessException(msg);
        }
    }

    private Map<String, BigDecimal> getprepayAmountMap(String reportNo, Integer caseTimes) {
        List<ClmsPolicyPrepayDutyDetailEntity> clmsPolicyPrepayDutyDetailEntities = new ArrayList<>();
        Map<String,BigDecimal> prepayMap = new HashMap<>();

        clmsPolicyPrepayDutyDetailEntities = policyPrepayDutyDetailMapper.selectPrePayAmountAndCode(reportNo,caseTimes);
        if(clmsPolicyPrepayDutyDetailEntities != null && clmsPolicyPrepayDutyDetailEntities.size()>0){
            for(ClmsPolicyPrepayDutyDetailEntity clmsPolicyPrepayDutyDetailEntity:clmsPolicyPrepayDutyDetailEntities){
                prepayMap.put(clmsPolicyPrepayDutyDetailEntity.getDutyCode(),clmsPolicyPrepayDutyDetailEntity.getPrepayAmount());
            }
        }
        return prepayMap;
    }

    private List<DutyPayDTO> getdutyDetailList(List<PolicyPayDTO> policyPays) {
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
//        List<DutyDetailPayDTO> detailPayArr = new ArrayList<>();

        if (!CollectionUtils.isEmpty(policyPays)) {
            for (PolicyPayDTO policyPay : policyPays) {
                planPayArr.addAll(policyPay.getPlanPayArr());
            }
        }
        if (!CollectionUtils.isEmpty(planPayArr)) {
            for (PlanPayDTO planPayDTO : planPayArr) {
                dutyPayArr.addAll(planPayDTO.getDutyPayArr());
            }
        }
//        if (!CollectionUtils.isEmpty(dutyPayArr)) {
//            for (DutyPayDTO dutyPayDTO : dutyPayArr) {
//                detailPayArr.addAll(dutyPayDTO.getDutyDetailPayArr());
//            }
//        }
        return dutyPayArr;
    }

    /**
      *
      * @Description 校验是否有理赔二核，有的话判断状态是否完成
      * <AUTHOR>
      * @Date 2023/10/16 15:01
      **/
    private void checkSecondUWIsFinish(String reportNo, Integer caseTimes) {

        List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities = secondUnderwritingService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsSecondUnderwritingEntities)){
            for (ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity : clmsSecondUnderwritingEntities) {
                if ("02".equals(clmsSecondUnderwritingEntity.getLettersCancelStatus()) && !"26".equals(clmsSecondUnderwritingEntity.getConclusion())){
                    // 前一次函件未回销不可重复发起
                    throw new GlobalBusinessException("该案件有二核任务函件未回销，不可提交理算");
                }
                if ("01".equals(clmsSecondUnderwritingEntity.getUnderwritingStatus())){
                    // 前一次任务未完成不可重复发起
                    throw new GlobalBusinessException("该案件有二核任务未核保通过，不可提交理算");
                }
            }
        }
    }

    private List<MedicalBillInfoVO> checkBillInfo(String reportNo, Integer caseTimes) {
        List<MedicalBillInfoVO> medicalBillInfoList = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        // 校验是否有重复的票据
        Set<String> duplicates = new HashSet<>();
        List<String> repeatBillList =
                medicalBillInfoList.stream().map(MedicalBillInfoVO::getBillNo).filter(n -> !duplicates.add(n)).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(repeatBillList)) {
            throw new GlobalBusinessException(ErrorCode.MedicalBill.FAIL_MEDICALBILL_EXISTS, "本案有发票重复，请修改！");
        }

        /*
         * 校验健康告知内容:
         *      长鹅百万医疗住院版，发票类型为住院对应的ICD代码，是否在健康告知阻断ICD代码范围内并且没有进行过二核。
         *      根据产品号与方案查询是否配置了健康告知，如果配置了健康告知，则需要校验健康告知内容。
         */
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        for (PolicyInfoDTO policyInfoDTO : policyInfoDTOList) {
            String productCode = policyInfoDTO.getProductCode();
            String productPackageType = policyInfoDTO.getProductPackageType();
            List<String> healthNoticeList = productConfig.getHealthNoticeList(productCode, productPackageType);
            if (CollectionUtils.isNotEmpty(healthNoticeList)) {
                // 1、获取案件中需要校验的ICD集合
                List<String> icdCodeList = new ArrayList<>();
                for (MedicalBillInfoVO medicalBillInfoVO : medicalBillInfoList) {
                    if (!StringUtils.equals(medicalBillInfoVO.getTherapyType(), ChecklossConst.AHCS_THERAPY_TWO)) {
                        // 过滤类型不是住院的账单
                        continue;
                    }
                    // 从clms_diagnose_hospital_bill_association表获取 可能多条
                    List<DiagnoseHospitalBillAssociationDTO> diagnoseTempList =
                            diagnoseHospitalBillAssociationMapper.getDiagnoseHospitalBillAssociation(medicalBillInfoVO.getIdAhcsBillInfo());
                    icdCodeList.addAll(diagnoseTempList.stream().map(DiagnoseHospitalBillAssociationDTO::getDiagnoseCode).distinct().collect(Collectors.toList()));
                }
                // 2、过滤出案件命中的健康告知ICD集合： icdCodeList里可能有重复ICD代码应该不影响
                List<String> hitHealthNoticeList =
                        healthNoticeList.stream().filter(item -> icdCodeList.stream().anyMatch(icd -> icd.startsWith(item))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hitHealthNoticeList)) {
                    // 3、对命中的数据进行二核结果校验
                    if (!secondUnderwritingService.isAllSecondUW(reportNo, caseTimes, hitHealthNoticeList)) {
                        throw new GlobalBusinessException("本次索赔项目涉及健康告知，请发起二核!");
                    }
                }
            }
        }
        return medicalBillInfoList;
    }

    private void startSettleReview(String reportNo,Integer caseTimes,String settleAutoSubmit,String verifyApproved){
        /* delete zjtang 刪除旧流程校验逻辑
        String bpmKey = taskInfoMapper.getPendingTaskCount(reportNo, caseTimes);
        if (com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(bpmKey)){
            throw new GlobalBusinessException("该案件还有其它待处理的任务！");
        }
         */
        String managerUserId = null;
        String managerUserName = null;
        String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
        if(StringUtils.isNotEmpty(verifyApproved)){
            String [] parts = verifyApproved.split("-",2);
            managerUserId = parts[0];
            managerUserName = parts[1];
            try {
                UserInfoDTO userInfoDTO = cacheService.queryUserInfo(managerUserId);
                if(userInfoDTO != null){
                    departmentCode = userInfoDTO.getComCode();
                }
            }catch (Exception e){
                log.error("查询用户异常，reportNo:{}，userId：{}", reportNo, managerUserId);
            }
        }
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        String userId = userInfoDTO.getUserCode();
        TaskInfoDTO startTask = new TaskInfoDTO();
        startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
        startTask.setTaskId(UuidUtil.getUUID());
        startTask.setReportNo(reportNo);
        startTask.setCaseTimes(caseTimes);
        startTask.setTaskDefinitionBpmKey(BpmConstants.OC_SETTLE_REVIEW);
        startTask.setAssigneeTime(new Date());
        startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
        Date createdDate=new Date();
        //理算自动提交的理算处理人默认为system
        if(Objects.equals("Y",settleAutoSubmit)){
            startTask.setApplyer(Constants.SYSTEM_USER);
            startTask.setApplyerName(Constants.SYSTEM_USER);
            startTask.setUpdatedBy(Constants.SYSTEM_USER);
            startTask.setCreatedBy(Constants.SYSTEM_USER);
            //自动提交太快 理算任务和核赔任务的创建时间会一样
            startTask.setCreatedDate(DateUtil.offsetSecond(createdDate,25));
        }else{
            startTask.setApplyer(userId);
            startTask.setApplyerName(userInfoDTO.getUserName());
            startTask.setUpdatedBy(userId);
            startTask.setCreatedBy(userId);
            startTask.setCreatedDate(createdDate);
        }
        startTask.setAssigner(managerUserId);
        startTask.setAssigneeName(managerUserName);
        //理算金额+理算费用
        BigDecimal totalAmount = getSumPayFee(reportNo,caseTimes);
        LogUtil.audit("案件总金额={}",totalAmount);
        Integer taskGrade = permissionService.getPermissionGrade(Constants.PERMISSION_VERIFY, ConfigConstValues.HQ_DEPARTMENT,totalAmount);
        if(taskGrade == null){
            //查不到等级，默认给4级
            LogUtil.audit("使用默认案件等级");
            taskGrade = 4;
        }

//        if(taskInfoService.checkPolicyActualPremium(reportNo)){
//            //若保费实收不等于应收，此时核赔等级为五级
//            Integer defaultGrade = 5;
//            //两者比较取高等级
//            taskGrade = defaultGrade > taskGrade ? defaultGrade : taskGrade;
//            LogUtil.audit("保费为交齐，案件等级为{}",taskGrade);
//        }

        PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_VERIFY,departmentCode,1);
        Integer auditGrade = permissionUser.getGrade();
        if(auditGrade == null){
            LogUtil.audit("使用默认审批等级");
            auditGrade = 1;
        }
        startTask.setTaskGrade(taskGrade);
        startTask.setAuditGrade(auditGrade);
        startTask.setDepartmentCode(permissionUser.getComCode());
        taskInfoMapper.addTaskInfo(startTask);
    }

    /**
     * 设置创建人、修改人等必要信息
     */
    private void initPolicyPay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        for (PolicyPayDTO policyPayInfo : policyPays) {
            policyPayInfo.setUserId(WebServletContext.getUserId());
            if (CollectionUtils.isEmpty(policyPayInfo.getPlanPayArr())) {
                if(ListUtils.isEmptyList(policyPayInfo.getRiskGroupList())){
                    continue;
                }
                for (RiskGroupDTO riskGroupDTO : policyPayInfo.getRiskGroupList()) {
                    if (ListUtils.isEmptyList(riskGroupDTO.getPlanPayArr())) {
                        continue;
                    }
                    initPlanPay(policyPayInfo, riskGroupDTO.getPlanPayArr());
                    /*if(ListUtils.isEmptyList(riskGroupDTO.getRiskPropertyInfoList())){
                        continue;
                    }
                    for (CaseRiskPropertyDTO riskDTO : riskGroupDTO.getRiskPropertyInfoList()) {
                        if(ListUtils.isEmptyList(riskDTO.getPlanPayArr())){
                            continue;
                        }
                        initPlanPay(policyPayInfo,riskDTO.getPlanPayArr());
                    }*/
                }
            }else{
                initPlanPay(policyPayInfo,policyPayInfo.getPlanPayArr());
            }

        }
    }

    private void initPlanPay(PolicyPayDTO policyPayInfo,List<PlanPayDTO> planList){
        for (PlanPayDTO planPayItem : planList) {
            planPayItem.setCreatedBy(policyPayInfo.getCreatedBy());
            planPayItem.setUpdatedBy(policyPayInfo.getUpdatedBy());
            planPayItem.setPolicyNo(policyPayInfo.getPolicyNo());
            planPayItem.setCaseNo(policyPayInfo.getCaseNo());
            planPayItem.setCaseTimes(policyPayInfo.getCaseTimes());
            planPayItem.setUserId(policyPayInfo.getUserId());
            planPayItem.setClaimType(policyPayInfo.getClaimType());
            for (DutyPayDTO dutyPayItem : planPayItem.getDutyPayArr()) {
                dutyPayItem.setCreatedBy(policyPayInfo.getCreatedBy());
                dutyPayItem.setUpdatedBy(policyPayInfo.getUpdatedBy());
                dutyPayItem.setIdAhcsPlanPay(planPayItem.getIdAhcsPlanPay());
                dutyPayItem.setPlanCode(planPayItem.getPlanCode());
                dutyPayItem.setPolicyNo(policyPayInfo.getPolicyNo());
                dutyPayItem.setCaseNo(policyPayInfo.getCaseNo());
                dutyPayItem.setCaseTimes(policyPayInfo.getCaseTimes());
                dutyPayItem.setUserId(policyPayInfo.getUserId());
                for (DutyDetailPayDTO dutyDetailPayItem : dutyPayItem.getDutyDetailPayArr()) {
                    dutyDetailPayItem.setIdAhcsDutyPay(dutyPayItem.getIdAhcsDutyPay());
                    dutyDetailPayItem.setCreatedBy(policyPayInfo.getCreatedBy());
                    dutyDetailPayItem.setUpdatedBy(policyPayInfo.getUpdatedBy());
                    dutyDetailPayItem.setPlanCode(planPayItem.getPlanCode());
                    dutyDetailPayItem.setPolicyNo(policyPayInfo.getPolicyNo());
                    dutyDetailPayItem.setDutyCode(dutyPayItem.getDutyCode());
                    dutyDetailPayItem.setCaseNo(policyPayInfo.getCaseNo());
                    dutyDetailPayItem.setCaseTimes(policyPayInfo.getCaseTimes());
                    dutyDetailPayItem.setUserId(policyPayInfo.getUserId());
                }
            }
        }
    }

    @Override
    public BigDecimal getPrePayAmount(String policyNo, Integer caseTimes) throws GlobalBusinessException {
        LogUtil.audit("#根据保单号赔付次数查询预赔付金额-入参policyNo={},caseTimes={}", policyNo, caseTimes);
        BigDecimal result = policyPayDao.getPrePayAmount(policyNo, caseTimes);
        LogUtil.audit("#根据保单号赔付次数查询预赔付金额-出参" + result);
        return result;
    }


    /**
     * 查询案件理算信息
     */
    @Override
    public PolicyPayInfoVO getPolicyPayInfo(String reportNo, Integer caseTimes, String scene) {
        PolicyPayInfoVO policyPayInfo = new PolicyPayInfoVO();
        long startTime = System.currentTimeMillis();

        // ADD BY SHENWEN 20241212  start
        //查询保单赔付信息、险种信息
//        List<PolicyPayDTO> policyPayInfoArr = getByReportNo(reportNo, caseTimes);
        ClmsDutyDetailBillSettleDTO params = new ClmsDutyDetailBillSettleDTO();
        params.setReportNo(reportNo);
        params.setCaseTimes(caseTimes);
        params.setScene(scene);
        log.info("scenescenescenescenescene22222：{}", scene);
        List<PolicyPayDTO> policyPayInfoArr = this.getPolicyPayByParam(params);
        // ADD BY SHENWEN 20241212  end

        if (CollectionUtils.isEmpty(policyPayInfoArr)) {
            LogUtil.info("查询场景:{}", scene);
            LogUtil.info("报案号:{},赔付次数:{}的赔付数据不存在", reportNo, caseTimes);
            return policyPayInfo;
        }

        LogUtil.audit("报案号:{},赔付次数:{},查询保单赔付信息耗时:{}", reportNo, caseTimes, System.currentTimeMillis() - startTime);

        //给子属性赋值案件号、保单号等信息
        SettleHelper.setDetailExInfo(policyPayInfoArr);
        //设置责任颜色
        setDisPlayInfo(policyPayInfoArr);
        //设置最大赔付额
        maxPayService.initPoliciesPayMaxPay(policyPayInfoArr, scene);
        //共保描述
        Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
        Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(reportNo);
        Map<String, List<CoinsureRecordDTO>> coinsuranceRecordMap = coinsureService.getCoinsureRecordByReportNo(reportNo);
        if(coinsMap.size() > 0){
            for (PolicyPayDTO dto : policyPayInfoArr) {
                dto.setCoinsuranceDesc(coinsMap.getOrDefault(dto.getPolicyNo(),""));
                dto.setCoinsuranceList(coinsuranceMap.get(dto.getPolicyNo()));

                if (coinsuranceRecordMap.containsKey(dto.getPolicyNo())) {
                    dto.setIsFullPay(BaseConstant.STRING_1);
                }
            }
        }

        policyPayInfo.setPolicys(policyPayInfoArr);
        policyPayInfo.setSettleReviewRemark(settleBatchDao.getSettleReviewRemark(reportNo, caseTimes));

        //查询小条款信息
        List<PlanTermContentDTO>  termDtoList = ahcsPolicyPlanDataService.getPlanTermContentInfo(reportNo);
        policyPayInfo.setPlanTermContentDTOList(termDtoList);



        return policyPayInfo;
    }

    private List<PolicyPayDTO> getPolicyPayByParam(ClmsDutyDetailBillSettleDTO params) {
        //根据报案号查询保单列表
        List<PolicyPayDTO> policyList = policyPayDao.selectByReportNo(params.getReportNo() ,params.getCaseTimes());
        for (PolicyPayDTO policy : policyList) {
            // 加入查询参数
            policy.setScene(params.getScene());
            log.info("scenescenescenescenescene33333：{}", params.getScene());
            //险种赔付信息
            policy.setPlanPayArr(planPayService.getByPolicy(policy));

            policy.setRiskGroupSpecialPromises(policy.getSpecialPromises().stream().filter(i-> (policy.getRiskGroupName()).equals(i.getRiskGroupName())).collect(Collectors.toList()));
            policy.setSpecialPromises(policy.getSpecialPromises().stream().filter(i-> StringUtils.isEmpty(i.getRiskGroupName())).collect(Collectors.toList()));
        }

        return policyList;
    }

    private void handlePolicyBatchForRefuse(String reportNo, Integer caseTimes,  List<PolicyPayDTO> policyPays) {
        List<PolicyBatchPayDTO> policyBatchPays = null;
        policyBatchPays = policyBatchMapper.getPolicyBatchsByCliamType(reportNo, caseTimes, null);
        LogUtil.info("handlePolicyBatchForRefuseCome:{}",JSONObject.toJSONString(policyBatchPays));
        if (CollectionUtils.isEmpty(policyBatchPays)) {
            return;
        }
        for (PolicyBatchPayDTO policyBatch : policyBatchPays) {
            if (policyBatch.getFinalPayAmount() != null) {
                policyBatch.setFinalPayAmount(BigDecimal.ZERO);
            }
            if (policyBatch.getPolicyPayAmount() != null) {
                policyBatch.setPolicyPayAmount(BigDecimal.ZERO);
            }
        }
        updatePolicyBatch(policyPays, policyBatchPays);
        LogUtil.info("handlePolicyBatchForRefuseParams:{}",JSONObject.toJSONString(policyBatchPays));

        if (!CollectionUtils.isEmpty(policyBatchPays)) {
            policyBatchMapper.bathUpdatePolicy(policyBatchPays);
        }
    }


    private List<PolicyPayDTO> handlePolicyPays(String reportNo, Integer caseTimes, String lossObjectNo, String conclusion, Boolean clearPolicyFee) throws GlobalBusinessException {
        List<PolicyPayDTO> policyPays = null;
        policyPays = getByReportNo(reportNo, caseTimes);

        if (CollectionUtils.isEmpty(policyPays)) {
            LogUtil.audit("#处理赔付数据-报案号:{},赔付次数:{},赔付数据不存在,不需要处理", reportNo, caseTimes);
            return policyPays;
        }
        if (clearPolicyFee) {
            setPolicyFeePayZero(policyPays);
        } else {
            setPolicyFeePay(policyPays);
        }
//        settleService.distributeFee(policyPays, SettleConst.CLAIM_TYPE_PAY, null);

        for (PolicyPayDTO policyPay : policyPays) {
            List<PlanPayDTO> prePayPlanPays = null;
            BigDecimal policyPayAmount = policyPay.getSettleAmount();
            BigDecimal policyPrePayAmount = policyPay.getPolicyPrePay();
            policyPay.setPolicySumPay(policyPay.getPolicySumFee());
            if (policyPay.getPolicyPrePay() != null && policyPay.getPolicyPrePay().compareTo(BigDecimal.ZERO) > 0) {
                if (SettleConst.INDEMNITY_MODE_DENIED.equals(conclusion)) {
                    policyPay.setRefuseAmount(policyPayAmount.subtract(policyPrePayAmount));
                }
                policyPay.setSettleAmount(policyPrePayAmount);
                PolicyPayDTO policy = new PolicyPayDTO();
                BeanUtils.copyProperties(policyPay, policy);
                policy.setClaimType(ModelConsts.PRE_PAY);
                prePayPlanPays = planPayService.getByPolicy(policy);
            } else {
                if (SettleConst.INDEMNITY_MODE_DENIED.equals(conclusion)) {
                    policyPay.setRefuseAmount(policyPayAmount);
                }
                policyPay.setSettleAmount(BigDecimal.ZERO);
            }
            for (PlanPayDTO planPay : policyPay.getPlanPayArr()) {
                if (ListUtils.isNotEmpty(prePayPlanPays)) {
                    PlanPayDTO prePlanPayDTO = this.getPrePayPlan(planPay, prePayPlanPays);
                    if (prePlanPayDTO != null) {
                        planPay.setSettleAmount(prePlanPayDTO.getSettleAmount());
                    } else {
                        planPay.setSettleAmount(BigDecimal.ZERO);
                    }
                    for (DutyPayDTO dutyPay : planPay.getDutyPayArr()) {
                        DutyPayDTO preDutyPayDTO = null;
                        if (prePlanPayDTO != null) {
                            preDutyPayDTO = this.getPrePayDuty(dutyPay, prePlanPayDTO.getDutyPayArr());
                            if (preDutyPayDTO == null) {
                                dutyPay.setSettleAmount(BigDecimal.ZERO);
                            } else {
                                dutyPay.setSettleAmount(preDutyPayDTO.getSettleAmount());
                            }
                        } else {
                            dutyPay.setSettleAmount(BigDecimal.ZERO);
                        }
                        for (DutyDetailPayDTO detail : dutyPay.getDutyDetailPayArr()) {
                            detail.setAutoSettleAmount(null);
                            detail.setSettleAmount(BigDecimal.ZERO);
                        }
                    }
                } else {
                    planPay.setSettleAmount(BigDecimal.ZERO);
                    for (DutyPayDTO dutyPay : planPay.getDutyPayArr()) {
                        dutyPay.setSettleAmount(BigDecimal.ZERO);
                        for (DutyDetailPayDTO detail : dutyPay.getDutyDetailPayArr()) {
                            detail.setAutoSettleAmount(null);
                            detail.setSettleAmount(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }

        deletePayInfo(reportNo, caseTimes);
        insertPayInfo(policyPays);

        return policyPays;
    }

    public void deletePayInfo(String reportNo, Integer caseTimes) {
        BatchDTO batch = clmBatchService.getBatchInfo(reportNo, caseTimes);
        if (batch == null) {
            return;
        }
        String idAhcsBatch = batch.getIdAhcsBatch();
        policyPayDao.deletePolicyPays(reportNo, caseTimes);

        DutyDetailPayDTO dutyDetailPayDTO = new DutyDetailPayDTO();
        dutyDetailPayDTO.setIdAhcsBatch(idAhcsBatch);
        dutyDetailPayDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        dutyDetailPayDTO.setUpdatedBy(ConstValues.SYSTEM);
        dutyDetailPayService.updateEffective(dutyDetailPayDTO);

        dutyPayService.deleteByBatchId(idAhcsBatch, SettleConst.CLAIM_TYPE_PAY);
        planPayService.deleteByBatchId(idAhcsBatch, SettleConst.CLAIM_TYPE_PAY);
    }

    private DutyPayDTO getPrePayDuty(DutyPayDTO dutyPay, List<DutyPayDTO> preDutyPays) {
        DutyPayDTO retDutyPayDTO = null;
        if (preDutyPays == null) {
            return retDutyPayDTO;
        }
        for (DutyPayDTO dutyPayDTO : preDutyPays) {
            if (dutyPayDTO.getPlanCode().concat(dutyPayDTO.getDutyCode()).equals(dutyPay.getPlanCode().concat(dutyPay.getDutyCode()))) {
                if (retDutyPayDTO == null) {
                    retDutyPayDTO = new DutyPayDTO();
                    BeanUtils.copyProperties(dutyPayDTO, retDutyPayDTO);
                } else {
                    retDutyPayDTO.setSettleAmount(retDutyPayDTO.getSettleAmount().add(dutyPayDTO.getSettleAmount()));
                }
            }
        }
        return retDutyPayDTO;
    }

    private PlanPayDTO getPrePayPlan(PlanPayDTO planPay, List<PlanPayDTO> prePayPlanPays) {
        PlanPayDTO retPlanPayDto = null;
        if (prePayPlanPays == null) {
            return retPlanPayDto;
        }
        for (PlanPayDTO planPayDTO : prePayPlanPays) {
            if (planPay.getPolicyNo().concat(planPay.getPlanCode()).equals(planPayDTO.getPolicyNo().concat(planPayDTO.getPlanCode()))) {
                if (retPlanPayDto == null) {
                    retPlanPayDto = new PlanPayDTO();
                    BeanUtils.copyProperties(planPayDTO, retPlanPayDto);
                } else {
                    retPlanPayDto.setSettleAmount(retPlanPayDto.getSettleAmount().add(planPayDTO.getSettleAmount()));
                }
            }
        }
        return retPlanPayDto;
    }

    public void setPolicyFeePayZero(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        List<FeeInfoDTO> preFeeInfoList = new ArrayList<>();
        for (PolicyPayDTO policy : policyPays) {
            FeeAmountVO feeAmount = SettleHelper.getPolicyFee(preFeeInfoList, policy.getPolicyNo(), policy.getCaseNo());
            BigDecimal policyDecreaseFee = feeAmount.getFeeAmountAwa();
            BigDecimal policySumFee = feeAmount.getFeeAmountSum().subtract(policyDecreaseFee);
            policy.setPolicyDecreaseFee(policyDecreaseFee);
            policy.setPolicySumFee(policySumFee);
            BigDecimal policyPay = policy.getSettleAmount();
            policy.setPolicySumPay(sum(policySumFee, policyPay));
        }
    }

    private void setDisPlayInfo(List<PolicyPayDTO> policyPays) {
        Map<String, MedicalRuleConfigDTO> medicalRuleMap = new HashMap<>();
        for (PolicyPayDTO policy : policyPays) {
            String departmentCode = policy.getDepartmentCode();
            MedicalRuleConfigDTO medicalRuleConfig = medicalRuleMap.get(departmentCode);
            if (null == medicalRuleConfig) {
                medicalRuleConfig = new MedicalRuleConfigDTO();
                medicalRuleMap.put(departmentCode, medicalRuleConfig);
            }
            for (PlanPayDTO plan : policy.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    if (duty.getSettleAmount() != null && duty.getSettleAmount().compareTo(BigDecimal.ZERO) > 0) {
                            duty.setDisPlayColor("G");
                    }
                }
            }
        }
    }


    @Override
    public BigDecimal getFeePayAmount(PolicyPayDTO policyPay, String claimType) {
        FeePayDTO feePay = new FeePayDTO();
        feePay.setReportNo(policyPay.getReportNo());
        feePay.setCaseTimes(policyPay.getCaseTimes());
        feePay.setPolicyNo(policyPay.getPolicyNo());
        feePay.setCaseNo(policyPay.getCaseNo());
        feePay.setClaimType(claimType);
        return feePayService.getFeePayAmount(feePay);
    }

    @Override
    public boolean checkExists(String reportNo, Integer caseTime) {
        return policyPayDao.checkExists(reportNo, caseTime, SettleConst.CLAIM_TYPE_PAY) > 0;
    }



    @Override
    public List<PolicyPayDTO> selectFromPolicyCopy(String reportNo, Integer caseTimes) {
        List<PolicyPayDTO> policys=  clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo,caseTimes);
        return policys;
    }




    @Override
    public List<PolicyPayDTO> getByReportNo(String reportNo, Integer caseTime) {
        //根据报案号查询保单列表
        List<PolicyPayDTO> policyList = policyPayDao.selectByReportNo(reportNo, caseTime);
        for (PolicyPayDTO policy : policyList) {
            //险种赔付信息
            policy.setPlanPayArr(planPayService.getByPolicy(policy));
            //保单共享信息
           /* List<PolicyShareDTO> policyShares = policyShareDao.getByPolicy(policy.getIdAhcsPolicyInfo());
            this.fitShareGroupCode(policy, policyShares);*/
            policy.setRiskGroupSpecialPromises(policy.getSpecialPromises().stream().filter(i-> (policy.getRiskGroupName()).equals(i.getRiskGroupName())).collect(Collectors.toList()));
            policy.setSpecialPromises(policy.getSpecialPromises().stream().filter(i-> StringUtils.isEmpty(i.getRiskGroupName())).collect(Collectors.toList()));
        }
        /*try{
            riskPropertyService.setRiskProperty(policyList);
        }catch (Exception e){
            LogUtil.info("标的设置失败,不影响原有流程",e);
        }*/
        return policyList;
    }

    @Override
    public List<PolicyPayDTO> getByReportNo(String reportNo, Integer caseTime, String claimType) {
        List<PolicyPayDTO> policyList = policyPayDao.selectByReportNo(reportNo, caseTime);
        for (PolicyPayDTO policy : policyList) {
            policy.setClaimType(claimType);
            List<PlanPayDTO> planPays = planPayService.getByPolicy(policy);
            policy.setPlanPayArr(planPays);
        }
        return policyList;
    }

    @Override
    public void insertPolicyBatch(List<PolicyPayDTO> copyPolicyPays, String idAhcsBatch) {
        List<FeeInfoDTO> feeInfoList = new ArrayList<>();
        for (PolicyPayDTO policyPay : copyPolicyPays) {
            FeeAmountVO policyFee = SettleHelper.getPolicyFee(feeInfoList, policyPay.getPolicyNo(), policyPay.getCaseNo());
            insertPolicyBatch(policyPay, idAhcsBatch, policyFee);
        }
    }

    @Override
    public void insertPayInfo(List<PolicyPayDTO> policyPays) {
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
        List<DutyDetailPayDTO> detailPayArr = new ArrayList<>();
        for (PolicyPayDTO policyPayInfo : policyPays) {
            if(ListUtils.isNotEmpty(policyPayInfo.getRiskGroupList())){
                for (RiskGroupDTO group : policyPayInfo.getRiskGroupList()) {
                    for (CaseRiskPropertyDTO risk : group.getRiskPropertyInfoList()) {
                        riskPropertyService.setPlanDutyDetailRiskId(risk.getIdPlyRiskProperty(),risk.getPlanPayArr());
                        planPayArr.addAll(risk.getPlanPayArr());
                        for (PlanPayDTO plan : policyPayInfo.getPlanPayArr()) {
                            dutyPayArr.addAll(plan.getDutyPayArr());
                            for (DutyPayDTO duty : plan.getDutyPayArr()) {
                                detailPayArr.addAll(duty.getDutyDetailPayArr());
                            }
                        }
                    }
                }
            }else{
                planPayArr.addAll(policyPayInfo.getPlanPayArr());
                for (PlanPayDTO plan : policyPayInfo.getPlanPayArr()) {
                    dutyPayArr.addAll(plan.getDutyPayArr());
                    for (DutyPayDTO duty : plan.getDutyPayArr()) {
                        detailPayArr.addAll(duty.getDutyDetailPayArr());
                    }
                }
            }
        }
        if (ListUtils.isEmptyList(policyPays)
                || ListUtils.isEmptyList(planPayArr)
                || ListUtils.isEmptyList(dutyPayArr)
                || ListUtils.isEmptyList(detailPayArr)) {
            return;
        }
        insertPolicyInfo(policyPays, planPayArr, dutyPayArr, detailPayArr);
        setDutyLimitData(policyPays);
    }

    @Override
    public void setPolicyFeePay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        //获取理赔费用信息
        FeePayDTO feePayDTO = new FeePayDTO();
        feePayDTO.setReportNo(policyPays.get(0).getReportNo());
        feePayDTO.setCaseTimes(policyPays.get(0).getCaseTimes());
        feePayDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        List<FeeInfoDTO> feeInfoList = Optional.ofNullable(feePayService.getFeePayByParam(feePayDTO)).orElse(new ArrayList<>());
        for (PolicyPayDTO policy : policyPays) {
            FeeAmountVO feeAmount = SettleHelper.getPolicyFee(feeInfoList, policy.getPolicyNo(), policy.getCaseNo());
            BigDecimal policyDecreaseFee = feeAmount.getFeeAmountAwa();
            BigDecimal policySumFee = feeAmount.getFeeAmountSum().subtract(policyDecreaseFee);
            policy.setPolicyDecreaseFee(policyDecreaseFee);
            policy.setPolicySumFee(policySumFee);
            BigDecimal policyPay = policy.getSettleAmount();
            policy.setPolicySumPay(sum(policySumFee, policyPay));
        }
    }

    @Override
    public EpcisRequestVO getEpicsRequest(String clientNo, String policyNo, String reportNo) {
        return policyPayDao.getEpicsRequest(clientNo, policyNo, reportNo);
    }

    @Override
    public BigDecimal getSumPayFee(String reportNo, Integer caseTimes) {
        BigDecimal b = policyPayDao.getSumPayFee(reportNo, caseTimes);
        b = b.setScale(2, BigDecimal.ROUND_DOWN);
        return b;
    }

    private void insertPolicyBatch(PolicyPayDTO policyPay, String idAhcsBatch, FeeAmountVO policyFee) {
        BigDecimal settleAmount = nvl(policyPay.getSettleAmount(), 0);
        PolicyBatchPayDTO policyBatch = new PolicyBatchPayDTO();
        policyBatch.setIdAhcsPolicyBatchPay(UuidUtil.getUUID());
        policyBatch.setIdAhcsBatch(idAhcsBatch);
        policyBatch.setCaseNo(policyPay.getCaseNo());
        policyBatch.setPolicyNo(policyPay.getPolicyNo());
        policyBatch.setClaimType(policyPay.getClaimType());
        policyBatch.setCaseTimes(policyPay.getCaseTimes());
        policyBatch.setCreatedBy(policyPay.getCreatedBy());
        policyBatch.setUpdatedBy(policyPay.getUpdatedBy());
        policyBatch.setPolicyFee(policyFee.getFeeAmountSum().subtract(policyFee.getFeeAmountAwa()));
        policyBatch.setPolicyPayAmount(settleAmount);
        BigDecimal prePayFee = new BigDecimal("0");
        policyBatch.setFinalFee(policyFee.getFeeAmountSum().subtract(policyFee.getFeeAmountAwa()).subtract(nvl(prePayFee, 0)));
        policyBatch.setPolicyDecreaseFee(policyFee.getFeeAmountAwa());
        policyBatch.setFinalPayAmount(settleAmount.subtract(nvl(policyPay.getPolicyPrePay(), 0)));
        policyBatch.setArchiveDate(new Date());
        policyBatchMapper.insertPolicyBatch(policyBatch);
    }

    /**
     * 设置保单总预估金额、总预赔、初始化费用金额
     */
    private void setPolicyInfo(List<PolicyPayDTO> policyPays) {
        //设置保单总预估金额
        setEstimatePay(policyPays);
        //设置保单总预赔
        setPrePayInfo(policyPays);
        //初始化费用（都为0）
        setPolicyFeePay(policyPays);
    }

    /**
     * 更新批次表、保单赔付批次明细表
     */
    private void updateSettleBatch(SettlesFormVO settlesForm, List<PolicyPayDTO> policyPays, boolean isSend) {
        String reportNo = settlesForm.getReportNo();
        Integer caseTimes = settlesForm.getCaseTimes();
        BatchDTO batchDTO = clmBatchService.getBatchInfo(reportNo, caseTimes);
        if (isSend) {
            //发送
            batchDTO.setSettleStatus(SettleConst.SETTLE_STATUS_DONE);
        } else {
            //保存
            batchDTO.setSettleStatus(SettleConst.SETTLE_STATUS_ON);
        }
        batchDTO.setBatchSettleType(SettleConst.BATCH_SETTLE_TYPE_WHOLE_CASE);
        batchDTO.setSettleUserUm(settlesForm.getUserId());
        batchDTO.setUpdatedBy(settlesForm.getUserId());
        clmBatchService.updateBatch(batchDTO);

        //更新保单赔付批次明细表
        List<PolicyBatchPayDTO> policyBatchs = policyBatchMapper.listPolicyBatchs(reportNo, caseTimes);
        updatePolicyBatch(policyPays, policyBatchs);
//        List<CoinsureInfoDTO> coinsureInfos = settlesForm.getCoinsureInfos();
//        setCoinsuranceAmount(coinsureInfos, policyBatchs);
        if (!CollectionUtils.isEmpty(policyBatchs)) {
            for (PolicyBatchPayDTO policyBatch : policyBatchs) {
                policyBatchMapper.bathUpdatePolicyBath(policyBatch);
            }
        }

    }

    /**
     * 合并批单
     */
    private void mergeEndorsementInfo(SettlesFormVO settlesFormVO) {
        String reportNo = settlesFormVO.getReportNo();
        Integer caseTimes = settlesFormVO.getCaseTimes();
        String userId = WebServletContext.getUserId();
        EndorsementDTO endorInfo = settlesFormVO.getEndorInfo();
        LogUtil.audit("mergeEndorsementInfo合并批单报案号{}，入参{}",reportNo,JSONObject.toJSONString(endorInfo));
        if (endorInfo == null) {
            return;
        }
        endorInfo.setIdAhcsEndorsement(UuidUtil.getUUID());
        endorInfo.setCaseTimes(caseTimes);
        endorInfo.setReportNo(reportNo);
        endorInfo.setCreatedBy(userId);
        endorInfo.setUpdatedBy(userId);
        endorsementService.mergeEndorsementInfo(endorInfo);
    }

    /**
     * 人工理算校验
     */
    private void checkSettle(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays, List<PaymentItemComData> paymentItems,
                             EndorsementDTO endorsement) throws GlobalBusinessException {
        long start = System.currentTimeMillis();

        // 校验赔款金额是否大于立案金额，如果大于则无法提交完成
        settleValidateService.checkPolicyPay(policyPays,reportNo,caseTimes);

        if (caseTimes > 1) {
            // 重开单个人的赔付金额需大于上一次赔付金额 否则应该走追偿
            settleValidateService.checkLastPaymentItem(paymentItems,reportNo,caseTimes);
        }

        // 赔付金额就算是 0 也得录入领款人信息
        if(CollectionUtils.isEmpty(paymentItems)){
            throw new GlobalBusinessException("赔付信息不能为空，请填写后提交！");
        }
        paymentItems.forEach(paymentItemComData -> {
            if (StringUtils.isEmpty(paymentItemComData.getIdClmPaymentInfo())){
                throw new GlobalBusinessException("赔付信息里的领款人信息不能为空，请选择后提交！");
            }
        });

        // 如果赔付金额为0，没有必须进行下面的校验
        if (!settleValidateService.hasSettlePay(policyPays)) {
            return;
        }

        Map<String,PaymentInfoDTO> paymentInfoMap = getPaymentInfoMap(paymentItems);

        // 单个银行账户金额大于100万的时候开户行明细必填校验
        settleValidateService.checkBankDetail(paymentItems,paymentInfoMap);

        //校验保单是否有没有理算金额或费用项目
//        settleValidateService.checkSettled(policyPays, paymentItems);

        //校验批单信息是否为空
        settleValidateService.checkEndorsement(policyPays, endorsement);
        //校验限额
//        riskPropertyPayService.checkRiskPropertyPay(policyPays);
        riskPropertyPayService.checkRiskPay(reportNo, policyPays);
        //设置、校验最大赔付额
        setRollback(policyPays);
        settleValidateService.checkSettle(policyPays, paymentItems, reportNo, caseTimes);
        LogUtil.audit("checkSettle人工理算校验耗时:{},报案号:{},赔付次数:{}", System.currentTimeMillis() - start, reportNo, caseTimes);
    }

    /**
      *
      * @Description 构造 k : 主键 v：PaymentInfoDTO 的map
      * <AUTHOR>
      * @Date 2023/7/3 14:36
      **/
    private Map<String, PaymentInfoDTO> getPaymentInfoMap(List<PaymentItemComData> paymentItems) {
        Map<String, PaymentInfoDTO> paymentInfoMap = new HashMap<>();
        paymentItems.forEach(p -> paymentInfoMap.put(p.getIdClmPaymentInfo(), paymentInfoService.getPaymentInfoById(p.getIdClmPaymentInfo())));
        return paymentInfoMap;
    }







    private void insertPolicyInfo(List<PolicyPayDTO> policyPays, List<PlanPayDTO> planPays, List<DutyPayDTO> dutyPays, List<DutyDetailPayDTO> detailPays) {
        List<List<PolicyPayDTO>> policyPaysList = ListUtils.getListByGroup(policyPays, 20);
        for (List<PolicyPayDTO> policyList : policyPaysList) {
            ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyPayMapper.class, policyList,
                    ListUtils.GROUP_NUM, "insertPolicyPayInfoList");
        }
        List<List<PlanPayDTO>> planPaysList = ListUtils.getListByGroup(planPays, 20);
        for (List<PlanPayDTO> planList : planPaysList) {
            planPayService.insertPlanPayList(planList);
        }
        List<List<DutyPayDTO>> dutyPaysList = ListUtils.getListByGroup(dutyPays, 20);
        for (List<DutyPayDTO> dutyList : dutyPaysList) {
            dutyPayService.insertDutyPayList(dutyList);
        }
        List<List<DutyDetailPayDTO>> detailPaysList = ListUtils.getListByGroup(detailPays, 20);
        for (List<DutyDetailPayDTO> dutyDetailList : detailPaysList) {
            dutyDetailPayService.insertDutyDetailPayList(dutyDetailList);
        }

    }

    private void setEstimatePay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        PolicyPayDTO policy = policyPays.get(0);
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        List<EstimatePolicySumDTO> estimatePolicys = estimateService.getEstimatePolicySum(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(estimatePolicys)) {
            return;
        }
        for (PolicyPayDTO policyPay : policyPays) {
            for (EstimatePolicySumDTO estimatePolicy : estimatePolicys) {
                if (policyPay.getPolicyNo().equals(estimatePolicy.getPolicyNo()) && policyPay.getCaseNo().equals(estimatePolicy.getCaseNo())) {
                    policyPay.setPolicySumEstimate(estimatePolicy.getPolicySumEstimate());
                }
            }
        }
    }

    private void setPrePayInfo(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        PolicyPayDTO policy = policyPays.get(0);
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        List<PolicyPayDTO> oldPolicyPays = policyPayDao.getPrePolicyPays(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(oldPolicyPays)) {
            return;
        }
        for (PolicyPayDTO policyPay : policyPays) {
            for (PolicyPayDTO oldPolicyPay : oldPolicyPays) {
                if (policyPay.getPolicyNo().equals(oldPolicyPay.getPolicyNo()) && policyPay.getCaseNo().equals(oldPolicyPay.getCaseNo())) {
                    policyPay.setPolicyPrePay(oldPolicyPay.getPolicyPrePay());
                    policyPay.setPolicyPreFee(oldPolicyPay.getPolicyPreFee());
                }
            }
        }
    }

    private void updatePolicyBatch(List<PolicyPayDTO> policyPays, List<PolicyBatchPayDTO> policyBatchs) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        for (PolicyPayDTO policyPayInfo : policyPays) {
            BigDecimal policyPay = nvl(policyPayInfo.getSettleAmount(), 0);
            BigDecimal policyFee = nvl(policyPayInfo.getPolicySumFee(), 0);
            BigDecimal policyDecreaseFee = nvl(policyPayInfo.getPolicyDecreaseFee(), 0);
            PolicyBatchPayDTO policyBatch = getPolicyBatch(policyBatchs, policyPayInfo.getCaseNo());
            policyBatch.setPolicyFee(policyFee);
            policyBatch.setPolicyPayAmount(policyPay);
            policyBatch.setUpdatedBy(WebServletContext.getUserId());
            policyBatch.setPolicyDecreaseFee(policyDecreaseFee);
            BigDecimal prePolicyFee = getPrePolicyFee(policyBatchs, policyPayInfo.getCaseNo());
            policyBatch.setFinalFee(policyFee.subtract(prePolicyFee));
            policyBatch.setFinalPayAmount(policyPay.subtract(nvl(policyPayInfo.getPolicyPrePay(), 0)));
        }
    }

    private PolicyBatchPayDTO getPolicyBatch(List<PolicyBatchPayDTO> policyBatchs, String caseNo) {
        for (PolicyBatchPayDTO policyBatchPay : policyBatchs) {
            if (!SettleConst.CLAIM_TYPE_PAY.equals(policyBatchPay.getClaimType())) {
                continue;
            }
            if (caseNo.equals(policyBatchPay.getCaseNo())) {
                return policyBatchPay;
            }
        }
        return new PolicyBatchPayDTO();
    }

    private BigDecimal getPrePolicyFee(List<PolicyBatchPayDTO> policyBatchs, String caseNo) {
        BigDecimal prePolicyFee = BigDecimal.ZERO;
        for (PolicyBatchPayDTO policyBatchPay : policyBatchs) {
            if (!SettleConst.CLAIM_TYPE_PRE_PAY.equals(policyBatchPay.getClaimType())) {
                continue;
            }
            if (caseNo.equals(policyBatchPay.getCaseNo())) {
                prePolicyFee = sum(prePolicyFee, policyBatchPay.getPolicyFee());
            }
        }
        return prePolicyFee;
    }

    /*private void setCoinsuranceAmount(List<CoinsureInfoDTO> coinsureInfos, List<PolicyBatchPayDTO> policyBatchs) {
        if (CollectionUtils.isEmpty(coinsureInfos)) {
            return;
        }
        for (PolicyBatchPayDTO policyBatch : policyBatchs) {
            for (CoinsureInfoDTO coinsureInfo : coinsureInfos) {
                if (!policyBatch.getPolicyNo().equals(coinsureInfo.getPolicyNo())) {
                    continue;
                }
                String isCoinsurePay = coinsureInfo.getIsCoinsurePay();
                String isCoinsureFee = coinsureInfo.getIsCoinsureFee();
                if (SettleConst.IS_COINSURE_N.equals(isCoinsureFee) && SettleConst.IS_COINSURE_N.equals(isCoinsurePay)) {
                    continue;
                }
                BigDecimal mainReinsureScale = BigDecimal.ZERO;
                if (SettleConst.IS_COINSURE_Y.equals(isCoinsurePay)) {
                    BigDecimal policyPayAmount = coinsureInfo.getPolicyPayAmount();
                    BigDecimal claimCoinsuranceAmount = policyPayAmount.subtract(policyPayAmount.multiply(mainReinsureScale).setScale(2, BigDecimal.ROUND_HALF_UP));
                    policyBatch.setClaimCoinsuranceAmount(claimCoinsuranceAmount);
                }
                if (SettleConst.IS_COINSURE_Y.equals(isCoinsureFee)) {
                    BigDecimal policyFeeAmount = coinsureInfo.getPolicyFeeAmount();
                    BigDecimal claimCoinsuranceFee = policyFeeAmount.subtract(policyFeeAmount.multiply(mainReinsureScale).setScale(2, BigDecimal.ROUND_HALF_UP));
                    policyBatch.setClaimCoinsuranceFee(claimCoinsuranceFee);
                }
            }
        }
    }*/

    @Override
    public List<PolicyPayDTO> getPolicyPayListByReportNoAndCaseTimes(String reportNo, Integer caseTimes) throws GlobalBusinessException {

        return policyPayDao.getPolicyPayListByReportNo(reportNo, caseTimes);
    }

    @Override
    public void bathAddOrUpdatePayInfo(List<PolicyBatchPayDTO> policyBathPayList, List<PolicyPayDTO> updatePolicyPays, List<PolicyPayDTO> addPolicyPays, List<PlanPayDTO> planPays,
                                       List<DutyPayDTO> dutyPays) throws GlobalBusinessException {
        if (ListUtils.isNotEmpty(policyBathPayList)) {

            ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyBatchMapper.class, policyBathPayList,
                    ListUtils.GROUP_NUM, "bathInsertPolicyBath");
        }
        if (ListUtils.isNotEmpty(updatePolicyPays)) {

            ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyPayMapper.class, updatePolicyPays,
                    ListUtils.GROUP_NUM, "updatePolicyPayInfoList");

        }
        if (ListUtils.isNotEmpty(addPolicyPays)) {
            ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyPayMapper.class, addPolicyPays,
                    ListUtils.GROUP_NUM, "insertPolicyPayInfoList");
        }
        if (ListUtils.isNotEmpty(planPays)) {
            //保存险种赔付信息
            planPayService.insertPlanPayList(planPays);
        }
        if (ListUtils.isNotEmpty(dutyPays)) {
            //保存责任赔付信息
            dutyPayService.insertDutyPayList(dutyPays);
        }

    }

    @Override
    public List<PolicyPayDTO> getSimplePolicyDutyList(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return policyPayDao.getSimplePolicyDutyList(reportNo, caseTimes);
    }

    @Override
    public void updatePolicyFee(String reportNo, Integer caseTimes){
        List<PolicyPayDTO> policyPays = getByReportNo(reportNo, caseTimes);
        //过滤出未结案赔案
        List<CaseBaseDTO> caseList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        Map<String, String> caseMap = new HashMap<>();
        for(CaseBaseDTO caseBaseDTO : caseList){
            caseMap.put(caseBaseDTO.getCaseNo(), caseBaseDTO.getCaseStatus());
        }
        policyPays = policyPays.stream().filter(p -> !"0".equals(caseMap.get(p.getCaseNo()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        //设置保单费用
        setPolicyFeePay(policyPays);
        //分摊费用到险种责任上
//        settleService.distributeFee(policyPays, SettleConst.CLAIM_TYPE_PAY, null);
        //批量更新保单赔付信息
       /* ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyPayMapper.class, policyPays,
                ListUtils.GROUP_NUM, "updatePolicyPayInfoList");*/
        policyPays.forEach(policyPayDTO -> policyPayDao.updatePolicyPayInfoList(policyPayDTO));
        //批量更新赔付责任信息和险种赔付信息
        for (PolicyPayDTO policyPay : policyPays) {
            planPayService.batchModify(policyPay.getPlanPayArr());
        }

        CoinsureRecordDTO record = new CoinsureRecordDTO();
        record.setReportNo(reportNo);
        record.setCaseTimes(caseTimes);

//        List<CoinsureInfoDTO> coinsureInfos = coinsureService.getCoinsureInfo(record);
        List<PolicyBatchPayDTO> policyBatchs = policyBatchMapper.listPolicyBatchs(reportNo, caseTimes);
        updatePolicyBatch(policyPays, policyBatchs);
        //设置共保摊回金额
//        setCoinsuranceAmount(coinsureInfos, policyBatchs);

    }




    /*private List<PaymentItemDTO> buildCoinsurePaymemtItem(SettlesFormVO settlesFormVO){
        Map<String,List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(settlesFormVO.getReportNo());
        if(coinsMap.size() < 1){
            return new ArrayList<>();
        }
        LogUtil.audit("共保信息reportNo="+settlesFormVO.getReportNo()+",coinsMap="+ JSON.toJSONString(coinsMap));
        Map<String,PolicyPayDTO> policyMap = settlesFormVO.getPolicyPayArr().stream().collect(Collectors.toMap(PolicyPayDTO::getPolicyNo,dto->dto));
        String userId = WebServletContext.getUserId();
        List<PaymentItemDTO> resultList = new ArrayList<>();
        for (Map.Entry<String,PolicyPayDTO> entry:policyMap.entrySet()){
            String policyNo = entry.getKey();
            PolicyPayDTO policyPayDTO = Optional.ofNullable(entry.getValue()).orElse(new PolicyPayDTO());
            BigDecimal payAmt = policyPayDTO.getSettleAmount();
            if(payAmt == null || !BigDecimalUtils.compareBigDecimalPlus(payAmt,BigDecimal.ZERO)){
                continue;
            }
            List<CoinsureDTO> coinsList = coinsMap.getOrDefault(policyNo,new ArrayList<>());
            if(coinsList.size() < 1){
                continue;
            }
            coinsList = coinsList.stream().filter(coin -> !"1".equals(coin.getAcceptInsuranceFlag())).collect(Collectors.toList());
            //共保总比例
            BigDecimal coinTotalPer = BigDecimal.ZERO;
            for (CoinsureDTO coinsureDTO : coinsList) {
                if(coinsureDTO.getReinsureScale() == null){
                    coinsureDTO.setReinsureScale(BigDecimal.ZERO);
                }
                coinTotalPer = coinTotalPer.add(coinsureDTO.getReinsureScale());
            }
            //共保总金额
            BigDecimal coinTotalAmt = BigDecimalUtils.percentage(payAmt,coinTotalPer);
            int size = coinsList.size();
            LogUtil.audit("policyPayDTO={}",policyPayDTO);
            BigDecimal addAmt = BigDecimal.ZERO;
            for(int i=0;i<size;i++){
                CoinsureDTO coins = coinsList.get(i);
                BigDecimal coinAmt = BigDecimal.ZERO;
                if(size == 1){
                    coinAmt = BigDecimalUtils.percentage(payAmt,coins.getReinsureScale());
                }else{
                    if(i < size - 1 ){
                        coinAmt = BigDecimalUtils.percentage(payAmt,coins.getReinsureScale());
                        LogUtil.audit("共保代付coinAmt="+coinAmt);
                        addAmt = addAmt.add(coinAmt);
                    }else{
                        coinAmt = coinTotalAmt.subtract(addAmt);
                    }
                }

                PaymentItemDTO itemDTO = new PaymentItemDTO();
                itemDTO.setClientName(coins.getReinsureCompanyName());
                if(itemDTO.getClientName() == null){
                    itemDTO.setClientName(coins.getReinsureCompanyCode());
                }
                itemDTO.setCreatedBy(userId);
                itemDTO.setUpdatedBy(userId);
                itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
                itemDTO.setPolicyNo(policyNo);
                itemDTO.setCaseNo(policyPayDTO.getCaseNo());
                itemDTO.setReportNo(settlesFormVO.getReportNo());
                itemDTO.setCaseTimes(settlesFormVO.getCaseTimes());
                itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
                itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
                itemDTO.setIdClmPaymentInfo(coins.getReinsureCompanyCode());
                itemDTO.setCollectPaySign(SettleConst.COLLECT_SIGN);
                itemDTO.setPaymentAmount(coinAmt);
                itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
                itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
                itemDTO.setMergeSign(SettleConst.NOT_MERGE);
                itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
                itemDTO.setIsCoinsure(ConstValues.YES);
                itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
                itemDTO.setCompensateNo(commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO, policyPayDTO.getDepartmentCode()));
                resultList.add(itemDTO);
            }
        }

        if(resultList.size() > 0){
            //删除已经生成的，重新写入
            PaymentItemDTO param = new PaymentItemDTO();
            param.setReportNo(settlesFormVO.getReportNo());
            param.setCaseTimes(settlesFormVO.getCaseTimes());
            param.setClaimType(SettleConst.CLAIM_TYPE_PAY);
            param.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
            paymentItemService.delPaymentItem(param);
        }
        return resultList;
    }*/

    @Override
    public List<PolicyPayHisInfo> queryCaseInfo(String reportNo, Integer caseTime) {
        List<PolicyPayDTO> policyPayDTOS = policyPayDao.getPolicyPayListByReportNo(reportNo, caseTime);
        /*List<PolicyPayHisInfo> policyPayHisInfos = new ArrayList<>();
        if(riskPropertyService.displayRiskProperty(reportNo,null)){
            List<PolicyPayDTO> policyPayInfoArr = getByReportNo(reportNo, caseTime);
            if(ListUtils.isEmptyList(policyPayInfoArr)){
                return policyPayHisInfos;
            }
            policyPayHisInfos = convertPolicyPayList(policyPayInfoArr);
        }else{
            policyPayHisInfos =  policyPayDao.queryCaseInfo(reportNo, caseTime);
            removeUnSettled(policyPayHisInfos);
        }*/
        List<PolicyPayHisInfo> policyPayHisInfos =  policyPayDao.queryCaseInfo(reportNo, caseTime);
        removeUnSettled(policyPayHisInfos);
        if (riskPropertyService.displayRiskProperty(reportNo,null)) {
            setRiskGroup(reportNo, caseTime, policyPayHisInfos);
        }

        for (PolicyPayDTO policyPayDTO:policyPayDTOS){
            for (PolicyPayHisInfo policyPayHisInfo:policyPayHisInfos){
                if (policyPayHisInfo.getPolicyNo().equals(policyPayDTO.getPolicyNo())){
                    policyPayHisInfo.setFeePayAmount(nvl(policyPayDTO.getPolicySumFee(),0));
                }
            }
        }
        return policyPayHisInfos;
    }

    private void setRiskGroup(String reportNo, Integer caseTimes, List<PolicyPayHisInfo> policyPayHisInfos) {
        List<CaseBaseDTO> caseBaseDTOList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        Map<String, List<CaseBaseDTO>> caseBaseMap = caseBaseDTOList.stream().collect(Collectors.groupingBy(CaseBaseDTO::getPolicyNo));
        for (PolicyPayHisInfo policyPayHisInfo : policyPayHisInfos) {
            List<CaseBaseDTO> caseBaseList = caseBaseMap.get(policyPayHisInfo.getPolicyNo());
            if (CollectionUtils.isEmpty(caseBaseList)) {
                continue;
            }
            CaseBaseDTO caseBase = caseBaseList.get(0);

            List<RiskGroupDTO> riskGroupList = new ArrayList<>();
            RiskGroupDTO riskGroup = new RiskGroupDTO();
            riskGroup.setRiskGroupNo(caseBase.getRiskGroupNo());
            riskGroup.setRiskGroupName(caseBase.getRiskGroupName());
            riskGroup.setPlanList(policyPayHisInfo.getPlanList());
            riskGroupList.add(riskGroup);
            policyPayHisInfo.setRiskGroupList(riskGroupList);
            policyPayHisInfo.setPlanList(null);
        }
    }

    private List<PolicyPayHisInfo> convertPolicyPayList(List<PolicyPayDTO> policyPayInfoArr){
        List<PolicyPayHisInfo> policyPayHisInfos = new ArrayList<>();
        for (PolicyPayDTO policy : policyPayInfoArr) {
            List<RiskGroupDTO> groupList = policy.getRiskGroupList();
            if(ListUtils.isEmptyList(groupList)){
                continue;
            }
            List<RiskGroupDTO> groupHisList = new ArrayList<>();
            for (RiskGroupDTO riskGroupDTO : groupList) {
                List<CaseRiskPropertyDTO> riskList = riskGroupDTO.getRiskPropertyInfoList();
                if(ListUtils.isEmptyList(riskList)){
                    continue;
                }
                List<CaseRiskPropertyDTO> riskHisList = new ArrayList<>();
                for (CaseRiskPropertyDTO riskDTO : riskList) {
                    List<PlanPayDTO> planList = riskDTO.getPlanPayArr();
                    if(ListUtils.isEmptyList(planList)){
                        continue;
                    }
                    List<PlanPayHisInfo> planHisList = new ArrayList<>();
                    for (PlanPayDTO planPayDTO : planList) {
                        List<DutyPayDTO> dutyList = planPayDTO.getDutyPayArr();
                        if(ListUtils.isEmptyList(dutyList)){
                            continue;
                        }
                        List<DutyPayHisInfo> dutyHisList = new ArrayList<>();
                        for (DutyPayDTO dutyPayDTO : dutyList) {
                            List<DutyDetailPayDTO> detailList = dutyPayDTO.getDutyDetailPayArr();
                            if(ListUtils.isEmptyList(detailList)){
                                continue;
                            }
                            List<DutyDetailPayHisInfo> detailHisList = new ArrayList<>();
                            for (DutyDetailPayDTO detailPayDTO : detailList) {
                                BigDecimal settleAmt = BigDecimalUtils.nvl(detailPayDTO.getSettleAmount(),BaseConstant.STRING_0);
                                BigDecimal autoSettleAmt = BigDecimalUtils.nvl(detailPayDTO.getAutoSettleAmount(),BaseConstant.STRING_0);
                                if(settleAmt.compareTo(BigDecimal.ZERO)==0){
                                    settleAmt = autoSettleAmt;
                                }
                                if(settleAmt.compareTo(BigDecimal.ZERO)==0){
                                    continue;
                                }
                                DutyDetailPayHisInfo detailPayHis = new DutyDetailPayHisInfo();
                                detailPayHis.setDutyDetailCode(detailPayDTO.getDutyDetailCode());
                                detailPayHis.setDutyDetailName(detailPayDTO.getDutyDetailName());
                                detailPayHis.setDutyDetailBaseAmount(detailPayDTO.getBaseAmountPay());
                                detailPayHis.setSettleAmount(settleAmt);
                                detailHisList.add(detailPayHis);
                            }
                            if(detailHisList.size() < 1){
                                continue;
                            }
                            DutyPayHisInfo dutyPayHis = new DutyPayHisInfo();
                            dutyPayHis.setDutyCode(dutyPayDTO.getDutyCode());
                            dutyPayHis.setDutyName(dutyPayDTO.getDutyName());
                            dutyPayHis.setDutyDetailList(detailHisList);
                            dutyPayHis.setDutyFee(convertDutyFee(dutyPayDTO));
                            dutyHisList.add(dutyPayHis);
                        }
                        if(dutyHisList.size()<1){
                            continue;
                        }
                        PlanPayHisInfo planPayHis = new PlanPayHisInfo();
                        planPayHis.setPlanCode(planPayDTO.getPlanCode());
                        planPayHis.setPlanName(planPayDTO.getPlanName());
                        planPayHis.setCaseNo(planPayDTO.getCaseNo());
                        planPayHis.setDutyList(dutyHisList);
                        planHisList.add(planPayHis);
                    }
                    if(ListUtils.isEmptyList(planHisList)){
                        continue;
                    }
                    riskDTO.setPlanList(planHisList);
                    riskDTO.setPlanPayArr(null);
                    riskHisList.add(riskDTO);
                }
                if(riskHisList.size() < 1){
                    continue;
                }
                riskGroupDTO.setRiskPropertyInfoList(riskHisList);
                groupHisList.add(riskGroupDTO);
            }

            if(groupHisList.size() < 1){
                continue;
            }
            PolicyPayHisInfo policyHis = new PolicyPayHisInfo();
            policyHis.setRiskGroupList(groupHisList);
            policyHis.setPolicyNo(policy.getPolicyNo());
            policyHis.setReportNo(policy.getReportNo());
            policyHis.setCaseTimes(policy.getCaseTimes());
            policyHis.setCaseNo(policy.getCaseNo());
            policyPayHisInfos.add(policyHis);
        }
        return policyPayHisInfos;
    }

    private BigDecimal convertDutyFee(DutyPayDTO dutyPayDTO){
        BigDecimal arbFee = BigDecimalUtils.nvl(dutyPayDTO.getArbitrageFee(),BaseConstant.STRING_0);
        BigDecimal lawsAmt = BigDecimalUtils.nvl(dutyPayDTO.getLawsuitFee(),BaseConstant.STRING_0);
        BigDecimal lawyAmt = BigDecimalUtils.nvl(dutyPayDTO.getLawyerFee(),BaseConstant.STRING_0);
        BigDecimal execuAmt = BigDecimalUtils.nvl(dutyPayDTO.getExecuteFee(),BaseConstant.STRING_0);
        BigDecimal checkAmt = BigDecimalUtils.nvl(dutyPayDTO.getVerifyAppraiseFee(),BaseConstant.STRING_0);
        BigDecimal evaluAmt = BigDecimalUtils.nvl(dutyPayDTO.getCommonEstimateFee(),BaseConstant.STRING_0);
        BigDecimal totalAmt = arbFee.add(lawsAmt).add(lawyAmt).add(execuAmt).add(checkAmt).add(evaluAmt);
        return totalAmt;
    }


    private void removeUnSettled(List<PolicyPayHisInfo> policyPayHisInfos){
        policyPayHisInfos.forEach(policy->{
            List<PlanPayHisInfo> planPayHisInfos = policy.getPlanList();
            planPayHisInfos.forEach(plan->{
                List<DutyPayHisInfo> dutyPayHisInfos = plan.getDutyList();
                dutyPayHisInfos.forEach(duty->{
                    List<DutyDetailPayHisInfo> dutyDetailPayHisInfos = duty.getDutyDetailList();
                    dutyDetailPayHisInfos.removeIf(dutyDetail-> BigDecimalUtils.isNullOrZero(dutyDetail.getSettleAmount()));
                });
                dutyPayHisInfos.removeIf(duty->duty.getDutyDetailList().isEmpty());
            });
        });
    }

    private void updateLinkMan(List<LinkManVO> linkManVOS){
        if (linkManVOS != null && !linkManVOS.isEmpty()){
            linkManVOS.forEach(vo->{
                //校验联系人信息是否修改
                LinkManEntity entity = vo.convertToEntity();
                if (linkManMapper.isLinkManInfoChanged(entity) == 0){
                    entity.setUpdatedBy(WebServletContext.getUserId());
                    linkManMapper.updateByReportNo(entity);
                }
            });

        }
    }

    private void notSendMessage(String reportNo,Integer caseTimes){
        LinkManEntity entity = new LinkManEntity();
        entity.setUpdatedBy(BaseConstant.SYSTEM);
        entity.setReportNo(reportNo);
        entity.setCaseTimes(caseTimes.shortValue());
        entity.setSendMessage(ConstValues.NO);
        linkManMapper.updateByReportNo(entity);

    }

    @Override
    public void deletePolicyPays(String reportNo,Integer caseTimes){
        //赔付数据
        BatchDTO batch = clmBatchService.getBatchInfo(reportNo, caseTimes);
        if (batch != null) {
            String idClmsBatch = batch.getIdAhcsBatch();
            dutyDetailPayDao.deleteByBatchId(idClmsBatch,SettleConst.CLAIM_TYPE_PAY);
            dutyPayDao.deleteByBatchId(idClmsBatch,SettleConst.CLAIM_TYPE_PAY);
            planPayDao.deleteByBatchId(idClmsBatch,SettleConst.CLAIM_TYPE_PAY);
        }
        policyPayDao.deletePolicyPays(reportNo, caseTimes);
        //支付项
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
        paymentItemService.delPaymentItem(paymentItemDTO);

        //共保代付
        PaymentItemDTO paymentItemDTO2 = new PaymentItemDTO();
        paymentItemDTO2.setReportNo(reportNo);
        paymentItemDTO2.setCaseTimes(caseTimes);
        paymentItemDTO2.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        paymentItemDTO2.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
        paymentItemService.delPaymentItem(paymentItemDTO2);
        //批单信息
        endorsementService.deleteByPolicyPayId(reportNo, caseTimes);
        //费用 的不清
//        feePayService.clearFee(reportNo,caseTimes);

    }

    @Override
    public Boolean isPolicyHasPrePay(String reportNo, Integer caseTimes,String policyNo) {
        return policyPayDao.policyHasPrePay(reportNo,caseTimes,policyNo) > 0;
    }

    @Override
    public BigDecimal getLastPolicyPayAmount(String reportNo, Integer caseTimes) {
        BigDecimal lastPolicyPayAmount = BigDecimal.ZERO;
        for (int i = caseTimes - 1; i > 0; i--) {
            lastPolicyPayAmount = policyPayDao.getPolicyPay(reportNo, i);
            if (lastPolicyPayAmount.compareTo(BigDecimal.ZERO) > 0) {
                return lastPolicyPayAmount;
            }
        }
        return lastPolicyPayAmount;
    }

    @Override
    public BigDecimal getLastPolicyPayAmount(String reportNo, Integer caseTimes, String policyNo) {
        for (int i = caseTimes - 1; i > 0; i--) {
            PolicyPayBaseInfoDTO policyPayInfo = policyPayDao.getPolicyPayByPolicyNo(reportNo, i, policyNo);
            if (Objects.nonNull(policyPayInfo)) {
                return Objects.nonNull(policyPayInfo.getPolicyPay()) ? policyPayInfo.getPolicyPay() : BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 检查与被保险人关系字段
     * @param reportNo
     * @param caseTimes
     */
    private void checkClientRelation(String reportNo, int caseTimes) {
        //查询支付信息录入表
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setReportNo(reportNo);
        paymentInfoDTO.setCaseTimes(caseTimes);
        List<PaymentInfoDTO> list = paymentInfoMapper.getPaymentInfo(paymentInfoDTO);
        //循环查询出的数据，判断当账号类型为个人时，与被保险人字段是否为空
        if(CollectionUtils.isEmpty(list)) {
            return;
        }

        for (PaymentInfoDTO dto : list) {
            if(NcbsConstant.PERSON_ALACCOUNT.equals(dto.getBankAccountAttribute()) && StringUtils.isBlank(dto.getClientRelation())) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_CLIENT_RELATION, "请完善支付信息！");
            }
        }
    }

    /**
     * 检查支付信息中领款人必做在案件中存在
     *
     * @param reportNo
     * @param paymentItemList
     */
    private void checkPayeeInfo(String reportNo, List<PaymentItemComData> paymentItemList) {
        // 支付信息为空过
        if (CollectionUtils.isEmpty(paymentItemList)) {
            return;
        }
        for (PaymentItemComData paymentItem : paymentItemList) {
            PaymentInfoDTO paymentInfoDTO = paymentInfoMapper.getPaymentInfoById(paymentItem.getIdClmPaymentInfo());
            if (null == paymentInfoDTO || !StringUtils.equals(paymentInfoDTO.getReportNo(), reportNo)) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, MessageFormat.format(
                        "请注意，领款人：{0}，在案件：{1}中不存在！", paymentItem.getClientName(), reportNo));
            }
        }
    }

    private void setRollback(List<PolicyPayDTO> copyPolicyPays){
        if(ListUtils.isEmptyList(copyPolicyPays)){
            return;
        }
        copyPolicyPays.get(0).setRollback(true);
    }
    private void setDutyLimitData(List<PolicyPayDTO> policyPays) {
        for (PolicyPayDTO dto :policyPays) {
            List<DutyBillLimitInfoDTO> lists =new ArrayList<>();
            List<ClmsDutyDetailBillSettleDTO> detailBillSettleList = new ArrayList<>();
            for (PlanPayDTO plan :dto.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    for (DutyDetailPayDTO detail :duty.getDutyDetailPayArr()) {
                        if(CollectionUtils.isNotEmpty(detail.getDutyBillLimitInfoDTOList())){
                            lists.addAll(detail.getDutyBillLimitInfoDTOList());
                        }
                    }
                }
            }
            //处理日限额
            if(CollectionUtils.isNotEmpty(lists)){
                dutyBillLimitInfoService.saveList(lists);
            }
        }

    }

    /**
     * 根据报案号+赔付次数 更新案件状态
     * @param reportNo  报案号
     * @param caseTimes 赔付次数
     * @param caseStatus    案件状态
     */
    @Override
    public void updateCaseStatus(String reportNo,Integer caseTimes,String caseStatus) {
        if(com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(reportNo) && caseTimes!=null) {
            WholeCaseBaseDTO wholeCaseBaseDTO = new WholeCaseBaseDTO();
            wholeCaseBaseDTO.setReportNo(reportNo);
            wholeCaseBaseDTO.setCaseTimes(caseTimes);
            wholeCaseBaseDTO.setWholeCaseStatus(caseStatus);
            this.caseBaseMapper.batchModifyCaseBaseDTO(wholeCaseBaseDTO);
            this.wholeCaseBaseMapper.modifyWholeCaseBase(wholeCaseBaseDTO);
        }
    }
}
