package com.paic.ncbs.claim;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@ServletComponentScan
@EnableTransactionManagement
@SpringBootApplication
@MapperScan("com.paic.ncbs.claim.dao.mapper")
@ComponentScan(basePackages = {"com.samsung.common","com.paic.ncbs.claim"})
@EnableFeignClients(basePackages = {"com.samsung.common","com.paic.ncbs.claim"})
@EnableAsync
//@EnableSwagger2
public class ClaimApplication {

    public static void main(String[] args) {
        SpringApplication.run(ClaimApplication.class);
    }
}
