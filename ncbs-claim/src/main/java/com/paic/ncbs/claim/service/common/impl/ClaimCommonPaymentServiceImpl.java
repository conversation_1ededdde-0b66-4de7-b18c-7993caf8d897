package com.paic.ncbs.claim.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureRecordDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.common.ClaimCommonPaymentService;
import com.paic.ncbs.claim.service.common.ClmBatchService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Slf4j
@Service
public class ClaimCommonPaymentServiceImpl implements ClaimCommonPaymentService {
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private ClmBatchService clmBatchService;
    @Autowired
    private CommonService commonService;
    @Override
    @Transactional
    public void updatePaymentItems(List<PaymentItemComData> paymentItems, List<PolicyPayDTO> policyPayDTOS,String reportNo,Integer caseTimes){

        String idAhcsBatch = clmBatchService.insertBatch(reportNo,caseTimes, SettleConst.SETTLE_STATUS_ON);

        if(paymentItems != null && !paymentItems.isEmpty()){
            Map<String,List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(reportNo);
            Map<String, List<CoinsureRecordDTO>> coinsureRecordMap = coinsureService.getCoinsureRecordByReportNo(reportNo);
            log.info("共保信息reportNo={}, coinsMap={}", reportNo, JSON.toJSONString(coinsMap));

            policyPayDTOS.forEach(policy->{
                String caseNo = policy.getCaseNo();
                String policyNo = policy.getPolicyNo();
                String generateCaseNo = commonService.generateNo( NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,policy.getDepartmentCode());
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setCaseNo(caseNo);
                paymentItemDTO.setReportNo(reportNo);
                paymentItemDTO.setCaseTimes(caseTimes);
                paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
                paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
                paymentItemService.delPaymentItem(paymentItemDTO);

                // 删除代付
                PaymentItemDTO param = new PaymentItemDTO();
                param.setCaseNo(caseNo);
                param.setReportNo(reportNo);
                param.setCaseTimes(caseTimes);
                param.setClaimType(SettleConst.CLAIM_TYPE_PAY);
                param.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
                paymentItemService.delPaymentItem(param);

                paymentItems.stream()
                        .filter(item->caseNo.equals(item.getCaseNo()))
                        .collect(Collectors.toList())
                        .forEach(paymentItemComData -> {
                            PaymentItemDTO dto = paymentItemComData.convertToDTO();
                            PaymentInfoDTO paymentInfo = paymentInfoService.getPaymentInfoById(dto.getIdClmPaymentInfo());
                            BeanUtils.copyProperties(paymentInfo,dto);
                            // 计算书号
                            dto.setCompensateNo(generateCaseNo);
                            dto.setReportNo(reportNo);
                            dto.setCaseTimes(caseTimes);
                            dto.setCreatedBy(WebServletContext.getUserId());
                            dto.setUpdatedBy(WebServletContext.getUserId());
                            dto.setCollectPayApproach(paymentInfo.getCollectPayApproach());
                            dto.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
                            dto.setPolicyNo(policyNo);
                            dto.setCaseNo(caseNo);
                            dto.setClaimType(PrintConstValues.CLAIM_TYPE_PAY);
                            dto.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
                            dto.setCollectPaySign(SettleConst.COLLECTION);
                            dto.setMergeSign(SettleConst.NOT_MERGE);
                            dto.setIdClmBatch(idAhcsBatch);
                            dto.setPaymentCurrencyCode(SettleConst.RMB);
                            dto.setPayType(paymentInfo.getPayType());
                            dto.setOpenId(paymentInfo.getOpenId());
                            LogUtil.audit("报案号{}理算提交生成PaymentItem入参：{}",reportNo, JSONObject.toJSONString(dto));
                            paymentItemService.addPaymentItem(dto);

                            List<CoinsureDTO> coinsureList = coinsMap.get(policyNo);
                            if (CollectionUtils.isNotEmpty(coinsureList)) {
                                if (coinsureRecordMap.containsKey(policyNo) && !BaseConstant.STRING_1.equals(dto.getIsFullPay())) {
                                    throw new GlobalBusinessException(String.format("当前案件该保单已经做过全额给付，不能再选择非全额给付，保单号：%s", policyNo));
                                }

                                paymentItemService.addPaymentItemList(buildCoinsurePaymemtItem(dto, coinsureList));
                            }
                        });
            });
        }else {
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setCaseTimes(caseTimes);
            paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
            paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
            paymentItemService.delPaymentItem(paymentItemDTO);

            // 删除代付
            PaymentItemDTO param = new PaymentItemDTO();
            param.setReportNo(reportNo);
            param.setCaseTimes(caseTimes);
            param.setClaimType(SettleConst.CLAIM_TYPE_PAY);
            param.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
            paymentItemService.delPaymentItem(param);
        }
    }




    @Override
    public List<PaymentItemDTO> buildCoinsurePaymemtItem(PaymentItemDTO paymentItem, List<CoinsureDTO> coinsureList) {
        if (Objects.isNull(paymentItem.getCoinsuranceActualAmount())) {
            throw new GlobalBusinessException(String.format("共保实付金额不能为空，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
        }

        List<PaymentItemDTO> resultList = new ArrayList<>();
        CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
        // 从共没有代付
        if (Objects.isNull(coinsureDTO) || BaseConstant.STRING_2.equals(coinsureDTO.getCoinsuranceType())) {
            if (!BaseConstant.STRING_1.equals(paymentItem.getIsFullPay())) {
                throw new GlobalBusinessException(String.format("从共全额给付不能选择否，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
            }

            if (paymentItem.getCoinsuranceActualAmount().compareTo(paymentItem.getPaymentAmount()) != 0) {
                throw new GlobalBusinessException(String.format("我司承担金额错误，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
            }
            return resultList;
        }

        coinsureList = coinsureList.stream().sorted(Comparator.comparing(CoinsureDTO::getCompanyFlag)).collect(Collectors.toList());
        BigDecimal paymentAmount = paymentItem.getPaymentAmount();
        BigDecimal otherAmount = BigDecimal.ZERO;
        for (int i = 0; i < coinsureList.size(); i++) {
            CoinsureDTO coins = coinsureList.get(i);
            BigDecimal coinsuranceActualAmount;

            if (i == coinsureList.size() - 1) {
                coinsuranceActualAmount = paymentAmount.subtract(otherAmount);
                if (coinsuranceActualAmount.compareTo(paymentItem.getCoinsuranceActualAmount()) != 0) {
                    log.info("我司承担金额错误, 保单号: {}, 户名: {}, 入参金额: {}, 计算金额: {}", paymentItem.getPolicyNo(), paymentItem.getClientName(), paymentItem.getCoinsuranceActualAmount(), coinsuranceActualAmount);
                    throw new GlobalBusinessException(String.format("我司承担金额错误，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
                }
            } else {
                coinsuranceActualAmount = paymentAmount.multiply(nvl(coins.getReinsureScale(), 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                otherAmount = otherAmount.add(coinsuranceActualAmount);
            }

            if (i < coinsureList.size() - 1 && BaseConstant.STRING_1.equals(paymentItem.getIsFullPay())) {
                PaymentItemDTO itemDTO = new PaymentItemDTO();
                BeanUtils.copyProperties(paymentItem, itemDTO);
                itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
                itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
                itemDTO.setPaymentAmount(coinsuranceActualAmount);
                itemDTO.setCoinsuranceMark(BaseConstant.STRING_1);
                itemDTO.setAcceptInsuranceFlag(coins.getAcceptInsuranceFlag());
                itemDTO.setCoinsuranceCompanyCode(coins.getReinsureCompanyCode());
                itemDTO.setCoinsuranceCompanyName(coins.getReinsureCompanyName());
                itemDTO.setCoinsuranceRatio(coins.getReinsureScale());
                itemDTO.setCoinsuranceActualAmount(coinsuranceActualAmount);
                itemDTO.setIsCoinsure(ConstValues.YES);
                resultList.add(itemDTO);
            }
        }
        return resultList;
    }
}
