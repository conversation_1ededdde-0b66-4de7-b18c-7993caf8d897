package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 *  赔款类型：(13-赔款 1J直接理赔费用 C13共保摊回赔款)
 */
public enum PaymentTypeEnum {

	PAY("13", "赔款","P60"),
	FEE("1J", "费用","P61"),
	COIN_PAY("C13", "共保代付赔款","P80"),
	COIN_FEE("C1J", "共保代付费用","P90"),

	PRE_PAY("11", "预赔赔款","P60"),
	PRE_FEE("11J", "预赔费用","P61"),
	PRE_COIN_PAY("P13", "共保代付预赔赔款","P80"),
	PRE_COIN_FEE("P1J", "共保代付预赔费用","P90");


	private final String type;
	private final String name;
	private final String code;
	PaymentTypeEnum(String type, String name,String code) {
		this.type = type;
		this.name = name;
		this.code=  code;
	}

	public String getType() {
		return type;
	}


	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
	public static String getName(String type) {
		if(StrUtil.isEmpty(type)){
			return null;
		}
		for (PaymentTypeEnum paymentInfoType : PaymentTypeEnum.values()) {
			if (type.equals(paymentInfoType.getType())) {
				return paymentInfoType.getName();
			}
		}
		return null;
	}
	
	public static String getType(String name) {
		if(StrUtil.isEmpty(name)){
			return null;
		}
		for (PaymentTypeEnum paymentInfoType : PaymentTypeEnum.values()) {
			if (name.equals(paymentInfoType.getName())) {
				return paymentInfoType.getType();
			}
		}
		return null;
	}
	public static String getCode(String type) {
		if(StrUtil.isEmpty(type)){
			return null;
		}
		for (PaymentTypeEnum paymentInfoType : PaymentTypeEnum.values()) {
			if (type.equals(paymentInfoType.getType())) {
				return paymentInfoType.getCode();
			}
		}
		return null;
	}
}
