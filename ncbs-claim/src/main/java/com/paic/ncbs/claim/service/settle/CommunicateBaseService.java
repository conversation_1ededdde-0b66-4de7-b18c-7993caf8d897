package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.openapi.TpaCommunicateResponseDto;
import com.paic.ncbs.claim.model.vo.communicate.CommunicateBaseVO;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;

import java.util.List;


public interface CommunicateBaseService {


    void sendCommunicateBaseInfo(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException;

    CommunicateBaseVO getCommunicateDetailByCommunicateBaseId(String idAhcsCommunicateBase) throws GlobalBusinessException;

    void finishCommunicate(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException;

    CommunicateBaseVO initCommunicateBaseInfo(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException;

    CommunicateBaseVO getHistoryCommunicateBaseList(String reportNo, int caseTimes) throws GlobalBusinessException;

    Integer getCommunicateBaseTimes(String reportNo, int caseTimes);

    CommunicateBaseVO getNotCommunicateTask(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException;

    /**
     * TPA发起沟通接口
     * @param communicateBaseVO
     * @return
     */
    TpaCommunicateResponseDto sendCommunicateBaseInfoForTPA(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException;

    /**
     * 获取沟通部门列表
     * @return 部门列表
     */
    List<DepartmentVO> getCommunicateDepartmentList();

}
