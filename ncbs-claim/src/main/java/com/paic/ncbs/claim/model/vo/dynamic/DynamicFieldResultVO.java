package com.paic.ncbs.claim.model.vo.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.util.List;

@Data
@ApiModel("动态字段结果信息VO-DynamicFieldResultVO")
public class DynamicFieldResultVO {

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("低码表单key")
    private String templateId;

    @ApiModelProperty("字段明细")
    private List<FieldDetailVo> fieldDetailList;

    @Data
    public static class FieldDetailVo {
        @ApiModelProperty("字段名")
        private String fieldCode;

        @ApiModelProperty("字段值")
        private String value;

        @ApiModelProperty("字段映射")
        private String valueMapper;
    }

}
