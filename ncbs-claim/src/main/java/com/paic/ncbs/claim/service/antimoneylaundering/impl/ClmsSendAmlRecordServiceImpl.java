package com.paic.ncbs.claim.service.antimoneylaundering.impl;

import com.google.common.collect.Maps;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.AmlCodeEnum;
import com.paic.ncbs.claim.common.enums.ClientTypePersonalEnum;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsSendAmlRecordEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AmlNacMainMapper;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.ClmsAmlSendRecordMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.AmlCheckResultDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlAuditResult;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlSendRecordVO;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.PaymentTransAmlData;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;
import com.paic.ncbs.claim.sao.AntiMoneyLaunderingSAO;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsSendAmlRecordService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.settle.SettleValidateService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@RefreshScope
@Slf4j
@Service
public class ClmsSendAmlRecordServiceImpl implements ClmsSendAmlRecordService {

    @Autowired
    private PaymentInfoService paymentInfoService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SettleValidateService settleValidateService;

    @Autowired
    private AntiMoneyLaunderingSAO amlSAO;

    @Autowired
    private ClmsAmlSendRecordMapper sendRecordMapper;
    
    @Autowired
    private AmlNacMainMapper nacMainMapper;

    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;

    @Value("${switch.checkPaymentItem:Y}")
    private String switchCheckPaymentItem;

    @Override
    public AmlCheckResultDTO checkPaymentItem(SettlesFormVO dto) {
        // 参数必填校验
        if (StringUtils.isBlank(dto.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空！");
        }
        if (Objects.isNull(dto.getCaseTimes())) {
            throw new GlobalBusinessException("赔付次数不能为空！");
        }
        if(CollectionUtils.isEmpty(dto.getPaymentItemArr())){
            throw new GlobalBusinessException("赔付信息不能为空！");
        }
        dto.getPaymentItemArr().forEach(i -> {
            if (StringUtils.isBlank(i.getPolicyNo())) {
                throw new GlobalBusinessException("赔付信息保单号不能为空！");
            }
            if (StringUtils.isBlank(i.getCaseNo())) {
                throw new GlobalBusinessException("赔付信息赔案号不能为空！");
            }
            if (StringUtils.isBlank(i.getIdClmPaymentInfo())) {
                throw new GlobalBusinessException("赔付信息支付信息主键不能为空！");
            }
            if (Objects.isNull(i.getPaymentAmount())) {
                throw new GlobalBusinessException("赔付信息支付金额不能为空！");
            }
        });

        String userId = WebServletContext.getUserId();
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        List<PaymentItemComData> paymentItemArr = dto.getPaymentItemArr();

        Map<String, PaymentInfoDTO> paymentInfoMap = Maps.newHashMap();
        paymentItemArr.forEach(i -> {
            PaymentInfoDTO paymentInfo = paymentInfoService.getPaymentInfoById(i.getIdClmPaymentInfo());
            if (Objects.isNull(paymentInfo)) {
                throw new GlobalBusinessException("赔付信息支付信息主键错误");
            }
            paymentInfoMap.put(i.getIdClmPaymentInfo(), paymentInfo);
        });

        // 金额校验，领款人赔款≥1W且未录入反洗钱
        settleValidateService.checkAntiMoneyLaundering(reportNo, caseTimes, paymentItemArr, paymentInfoMap);

        // 反洗钱开关
        if (ConstValues.NO.equals(switchCheckPaymentItem)) {
            return AmlCheckResultDTO.builder().result(BaseConstant.STRING_0).build();
        }

        List<PaymentTransAmlData> amlDataList = Lists.newArrayList();
        for (PaymentItemComData item : paymentItemArr) {
            PaymentInfoDTO paymentInfo = paymentInfoMap.get(item.getIdClmPaymentInfo());
            if(StringUtils.isBlank(paymentInfo.getCustomerNo())){
                ReportCustomerInfoEntity reportCustomerInfo = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
                paymentInfo.setCustomerNo(reportCustomerInfo.getClientNo());
            }
            String nationCode = CommonConstant.CHINA_COUNTRY_CODE; // 国籍目前只有中国
            String clientName = paymentInfo.getClientName();
            String clientCertificateNo;
            String clientCertificateType;
            if (BaseConstant.STRING_0.equals(paymentInfo.getBankAccountAttribute())) {
                // 公司
                clientCertificateNo = paymentInfo.getOrganizeCode();
                clientCertificateType=paymentInfo.getCompanyCardType();
            } else {
                // 个人
                clientCertificateNo = paymentInfo.getClientCertificateNo();
                clientCertificateType = paymentInfo.getClientCertificateType();
            }

            // 洗钱黑名单
            int moneyLaunderingBlackListCount = nacMainMapper.getMoneyLaunderingBlackListCount(clientName, clientCertificateNo);
            if (moneyLaunderingBlackListCount > 0) {
                ClmsSendAmlRecordEntity query = ClmsSendAmlRecordEntity.builder()
                        .reportNo(reportNo)
                        .caseTimes(caseTimes)
                        .amlCode(AmlCodeEnum.AML_CODE_00001.getType())
                        .clientName(clientName)
                        .clientCertificateNo(clientCertificateNo)
                        .clientCertificateType(clientCertificateType)
                        .build();
                ClmsSendAmlRecordEntity one = sendRecordMapper.selectOne(query);
                if (Objects.isNull(one)) {
                    PaymentTransAmlData amlData = convertToAmlData(paymentInfo, AmlCodeEnum.AML_CODE_00001.getType(), item.getPolicyNo(),
                            item.getCaseNo(), nationCode, item.getPaymentAmount(), clientName, clientCertificateNo, clientCertificateType);
                    amlDataList.add(amlData);
                } else {
                    if (StringUtils.isBlank(one.getAuditFlag())) {
                        throw new GlobalBusinessException("黑名单待审批！");
                    }

                    if (ConstValues.YES.equals(one.getAuditFlag())) {
                        throw new GlobalBusinessException("当前案件涉及可疑交易！");
                    }
                }
            }

            // 高风险国家地区黑名单
            int highRiskCountryBlackListCount = nacMainMapper.getHighRiskCountryBlackListCount(nationCode);
            if (highRiskCountryBlackListCount > 0) {
                ClmsSendAmlRecordEntity query = ClmsSendAmlRecordEntity.builder()
                        .reportNo(reportNo)
                        .caseTimes(caseTimes)
                        .amlCode(AmlCodeEnum.AML_CODE_00002.getType())
                        .clientName(clientName)
                        .clientCertificateNo(clientCertificateNo)
                        .clientCertificateType(clientCertificateType)
                        .nationCode(nationCode)
                        .build();
                ClmsSendAmlRecordEntity one = sendRecordMapper.selectOne(query);
                if (Objects.isNull(one)) {
                    PaymentTransAmlData amlData = convertToAmlData(paymentInfo, AmlCodeEnum.AML_CODE_00002.getType(), item.getPolicyNo(),
                            item.getCaseNo(), nationCode, item.getPaymentAmount(), clientName, clientCertificateNo, clientCertificateType);
                    amlDataList.add(amlData);
                }
            }

            // 领款人类型为其他
            if (ClientTypePersonalEnum.CLIENT_TYPE_09.getCode().equals(paymentInfo.getClientType())) {
                ClmsSendAmlRecordEntity query = ClmsSendAmlRecordEntity.builder()
                        .reportNo(reportNo)
                        .caseTimes(caseTimes)
                        .amlCode(AmlCodeEnum.AML_CODE_00008.getType())
                        .clientName(clientName)
                        .clientCertificateNo(clientCertificateNo)
                        .clientCertificateType(clientCertificateType)
                        .build();
                ClmsSendAmlRecordEntity one = sendRecordMapper.selectOne(query);
                if (Objects.isNull(one)) {
                    PaymentTransAmlData amlData = convertToAmlData(paymentInfo, AmlCodeEnum.AML_CODE_00008.getType(), item.getPolicyNo(),
                            item.getCaseNo(), nationCode, item.getPaymentAmount(), clientName, clientCertificateNo, clientCertificateType);
                    amlDataList.add(amlData);
                }
            }
        }

        // 送global可疑数据并保存送反洗钱记录表
        if (CollectionUtils.isNotEmpty(amlDataList)) {
            // 判断是否需要弹窗二次确认
            if (BaseConstant.STRING_0.equals(dto.getStatus())) {
                Set<String> amlCodeSet = amlDataList.stream().map(PaymentTransAmlData::getAntimoneyCd).collect(Collectors.toSet());
                if (amlCodeSet.contains(AmlCodeEnum.AML_CODE_00001.getType()) && amlCodeSet.contains(AmlCodeEnum.AML_CODE_00008.getType())) {
                    return AmlCheckResultDTO.builder().result(BaseConstant.STRING_2).message("投保人、被保险或者受益人名称与洗钱黑名单中的名称一致！<br/>赔款汇向被保险人、受益人以外的第三人。<br/>是否继续提交？").build();
                } else if (amlCodeSet.contains(AmlCodeEnum.AML_CODE_00001.getType())) {
                    return AmlCheckResultDTO.builder().result(BaseConstant.STRING_2).message("投保人、被保险或者受益人名称与洗钱黑名单中的名称一致！是否继续提交？").build();
                } else if (amlCodeSet.contains(AmlCodeEnum.AML_CODE_00008.getType())) {
                    return AmlCheckResultDTO.builder().result(BaseConstant.STRING_2).message("赔款汇向被保险人、受益人以外的第三人，是否继续提交？").build();
                }
            }

            // 送global
            amlSAO.reportAmlTarget(amlDataList);

            // 保存送反洗钱记录表
            List<ClmsSendAmlRecordEntity> entityList = Lists.newArrayList();
            boolean resultFail = false; // 调用global系统失败标记
            boolean flag = false; // 存在反洗钱名单标记
            for (PaymentTransAmlData amlData : amlDataList) {
                if (StringUtils.isBlank(amlData.getSerialNo()) || NcbsConstant.RESULT_FAIL.equals(amlData.getCode())) {
                    resultFail = true;
                    continue;
                }

                if (AmlCodeEnum.AML_CODE_00001.getType().equals(amlData.getAntimoneyCd())) {
                    flag = true;
                }

                ClmsSendAmlRecordEntity entity = new ClmsSendAmlRecordEntity();
                entity.setReportNo(reportNo);
                entity.setCaseTimes(caseTimes);
                entity.setSerialNo(amlData.getSerialNo());
                entity.setAmlCode(amlData.getAntimoneyCd());
                entity.setClientNo(amlData.getClientNo());
                entity.setClientName(amlData.getClientName());
                entity.setClientCertificateNo(amlData.getClientCertificateNo());
                entity.setClientCertificateType(amlData.getClientCertificateType());
                entity.setNationCode(amlData.getNationCode());
                entity.setCreatedBy(userId);
                entity.setUpdatedBy(userId);
                entityList.add(entity);
            }

            if (CollectionUtils.isNotEmpty(entityList)) {
                sendRecordMapper.batchInsert(entityList);
            }

            if (resultFail) {
                throw new GlobalBusinessException("反洗钱上报失败！");
            }
            if (flag) {
                return AmlCheckResultDTO.builder().result(BaseConstant.STRING_1).message("洗钱黑名单上报成功").build();
            }
        }

        return AmlCheckResultDTO.builder().result(BaseConstant.STRING_0).build();
    }

    @Override
    public List<AmlSendRecordVO> getRecordList(String reportNo, Integer caseTimes) {
        List<AmlSendRecordVO> voList = sendRecordMapper.selectList(reportNo, caseTimes);
        voList.forEach(i -> i.setSubmitter(userInfoService.getUserNameById(i.getSubmitter())));
        return voList;
    }

    @Override
    public void auditResult(AmlAuditResult amlAuditResult) {
        if (null == amlAuditResult) {
            throw new GlobalBusinessException("审核结果不能为空！");
        }
            List<ClmsSendAmlRecordEntity> entityList = new ArrayList<>();
            ClmsSendAmlRecordEntity entity = new ClmsSendAmlRecordEntity();
            if (StringUtils.isBlank(amlAuditResult.getDealNo())) {
                throw new GlobalBusinessException("可疑交易识别流水号不能为空！");
            }
            entity.setSerialNo(amlAuditResult.getDealNo());

            if (StringUtils.isBlank(amlAuditResult.getAuditFlag())) {
                throw new GlobalBusinessException("审核结果标识不能为空！");
            }
            entity.setAuditFlag(amlAuditResult.getAuditFlag());

            try {
                Date auditTime = DateUtils.parseToFormatDate(amlAuditResult.getAuditTime(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS);
                entity.setAuditTime(auditTime);
            } catch (ParseException e) {
                throw new GlobalBusinessException("审批时间格式错误！");
            }

            entity.setSubmissionInstructions(amlAuditResult.getSubmissionInstructions());
            entity.setAuditorCode(amlAuditResult.getAuditorCode());
            entity.setAuditorName(amlAuditResult.getAuditorName());
            entity.setAuditDescription(amlAuditResult.getAuditDescription());
            entityList.add(entity);

        sendRecordMapper.batchUpdate(entityList);
    }

    /**
     * 转换反洗接口对象
     *
     * @param paymentInfo 支付信息
     * @param antimoneyCd 反洗钱监测标准代码
     * @param policyNo 保单号
     * @param caseNo 赔案号
     * @param nationCode 国籍
     * @param paymentAmount 支付金额
     * @param clientName 名称
     * @param clientCertificateNo 证件号
     * @param clientCertificateType 证件类型
     * @return 反洗接口对象
     */
    private PaymentTransAmlData convertToAmlData(PaymentInfoDTO paymentInfo, String antimoneyCd, String policyNo,
                                                 String caseNo, String nationCode, BigDecimal paymentAmount,
                                                 String clientName, String clientCertificateNo, String clientCertificateType) {
        PaymentTransAmlData amlData = new PaymentTransAmlData();
        amlData.setReportNo(paymentInfo.getReportNo());
        amlData.setCaseTimes(paymentInfo.getCaseTimes());
        amlData.setAntimoneyCd(antimoneyCd);
        amlData.setPolicyNo(policyNo);
        amlData.setCaseNo(caseNo);
        amlData.setNationCode(nationCode);
        amlData.setPaymentAmount(paymentAmount);
        amlData.setClientNo(paymentInfo.getCustomerNo());
        amlData.setClientName(clientName);
        amlData.setClientCertificateNo(clientCertificateNo);
        amlData.setClientCertificateType(clientCertificateType);
        amlData.setClientMobile(paymentInfo.getClientMobile());
        amlData.setClientBankCode(paymentInfo.getClientBankCode());
        amlData.setClientBankName(paymentInfo.getClientBankName());
        amlData.setClientBankAccount(paymentInfo.getClientBankAccount());
        amlData.setBankAccountAttribute(paymentInfo.getBankAccountAttribute());
        return amlData;
    }
}
