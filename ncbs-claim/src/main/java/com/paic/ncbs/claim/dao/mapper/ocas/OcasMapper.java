package com.paic.ncbs.claim.dao.mapper.ocas;

import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.model.dto.ahcs.PolicyCopySendDetail;
import com.paic.ncbs.claim.model.dto.customer.ClientManagementDTO;
import com.paic.ncbs.claim.model.dto.ocas.*;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyParamDTO;
import com.paic.ncbs.claim.model.dto.settle.BeneficaryDTO;
import com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.ocas.OcasReportQueryVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;
import java.util.Map;

@MapperScan
public interface OcasMapper {

    List<OcasDTO> getInsuredList(OcasDTO ocasDTO);

    String getRiskGroupNo(@Param("policyNo") String policyNo,@Param("certificateNo")String certificateNo, @Param("insuredName")  String insuredName);

    List<OcasPolicyPlanDutyDTO> getPolicyPlanDuty(@Param("policyNoList") List<OcasPolicyDTO> policyNoList,
                                                  @Param("certificateNo") String certificateNo,
                                                  @Param("insuredName")  String insuredName);

    List<OcasInsuredDTO> getReportInsuredList(OcasReportQueryVO queryVO);

    List<OcasPolicyDTO> getReportPolicyList(OcasPolicyQueryVO queryVO);

    OcasInsuredDTO getInsuredBaseInfo(@Param("idPlyRiskPerson") String idPlyRiskPerson);

    String getPersionRiskId(@Param("certificateType") String certificateType,@Param("certificateNo") String certificateNo,@Param("policyNo") String policyNo);

    List<OcasDTO> getInsuredListPage(OcasDTO param);

    PolicyCopySendDetail policyCopyAndSendDetail(@Param("policyNo") String policyNo);

    Map<String,String> getPlyBaseInfo(@Param("policyNo") String policyNo);

    String getPlyPlanInfo(@Param("policyNo") String policyNo,@Param("planCode") String planCode);

    List<Map<String,String>> getPolicyPlan(@Param("planCode") String planCode);

    List<BeneficaryDTO> benefitInfos(@Param("policyNo") String policyNo, @Param("certificateNo")String certificateNo, @Param("insuredName")String insuredName);

    List<OcasInsuredDTO> getRiskPersonnalList(@Param("policyList") List<String> policyList);

    List<OcasPolicyPlanDutyDTO> getPolicyCopyDuty(@Param("policyNo") String policyNo,@Param("certificateNo") String certificateNo,
                                                  @Param("insuredName")  String insuredName);

    String getPolicyNoFromBase(@Param("policyNo") String policyNo);

    String getClientInfoByFiveInfo(ReportCustomerInfoEntity reportCustomerInfo);

    Date getPolicyStopDate(@Param("policyNo") String policyNo);

    String getRiskPersonNo(@Param("policyNo") String policyNo,@Param("certificateNo")String certificateNo,@Param("insuredName")String insuredName);

    String getRiskPersonCode(@Param("policyNo") String policyNo,@Param("certificateNo")String certificateNo,@Param("insuredName")String insuredName);

    List<AhcsPolicyInfoEntity> getPolicyActualPremiumList(@Param("reportNo") String reportNo);

    AhcsPolicyInfoEntity getPolicyActualPremium(@Param("policyNo") String policyNo);

    Map<String,String> getPlyAgentByPolicyNo(@Param("policyNo") String policyNo);

    Map<String,String> getPlySaleByPolicyNo(@Param("policyNo") String policyNo);

    OcasPolicyPayDTO getPolicyInfoForPay(@Param("policyNo") String policyNo, @Param("clientNo")String clientNo);

    /**
     * 根据idPlyDuty查询ply_risk_duty_relation表中责任的起止日期
     * @param idPlyDuty
     * @return
     */
    OcasPolicyPlanDutyDTO getPlyRiskDutyRelation(@Param("idPlyDuty") String idPlyDuty ,@Param("idRiskClass") String idRiskClass);

    /**
     * 根据三项信息查询客户号
     * @param reportCustomerInfo
     * @return
     */
    String getClientNo(ReportCustomerInfoEntity reportCustomerInfo);

    /**
      *
      * @Description 根据保单号获取投保人信息
      * <AUTHOR>
      * @Date 2023/11/25 14:55
      **/
    OcasApplicantInfoDTO getApplicantInfo(@Param("policyNo") String policyNo);

    void saveClientNo(ClientManagementDTO clientManagementDTO);

    OcasPolicyPlanDutyDTO getPolicyInfoByPolicyNo(@Param("policyNo") String policyNo);

    Map<String, String> getRiskGroupByPolicyNo(@Param("policyNo") String policyNo, @Param("clientNo") String clientNo);

    String getTargetType(@Param("policyNo") String policyNo);

    ProductInfoDTO getPrudocutInfo(@Param("policyNo") String policyNo);

    List<OcasPolicyPlanDutyDTO> getPolicyCopyDutyList(OcasPolicyQueryVO queryVO);

    List<CaseRiskPropertyDTO> getRiskPropertyList(OcasPolicyQueryVO queryVO);

    List<OcasPolicyPlanDutyDTO> getPolicyPlanDutyList(OcasPolicyQueryVO queryVO);

    String getPolicyApplyMode(String policyNo);

    String getPolicyDocumentId(String policyNo);

    String getDocumentIdByPolicyNo(String policyNo);

    List<OcasInsuredDTO> getRiskPersonnalListByName(@Param("policyList") List<String> policyList, @Param("name") String name);

    List<RiskPropertyParamDTO> getRiskPropertyParamByPolicyNo(@Param("policyNo") String policyNo);

    List<String> getPlanClassByInsured(@Param("certificateNo") String certificateNo,@Param("policyNoList") List<String> policyNoList, @Param("insuredName") String insuredName);

    String getRiskGroupType(@Param("policyNo") String policyNo);
}
