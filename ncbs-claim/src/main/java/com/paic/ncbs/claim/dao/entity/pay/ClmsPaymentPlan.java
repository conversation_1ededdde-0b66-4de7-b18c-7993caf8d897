package com.paic.ncbs.claim.dao.entity.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 赔款拆分险种表(ClmsPaymentPlan)实体类
 *
 * <AUTHOR>
 * @since 2023-04-24 20:59:55
 */
@Data
public class ClmsPaymentPlan implements Serializable {
    private static final long serialVersionUID = -80430985048542783L;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;
    /**
     * 表主键
     */
    private String idClmsPaymentPlan;
    /**
     * 支付项目表CLM_PAYMENT_ITEM主键
     */
    private String idClmPaymentItem;
    /**
     * 险种编码
     */
    private String planCode;
    /**
     * 险种大类
     */
    private String kindCode;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品大类
     */
    private String productLineCode;
    /**
     * 险种分摊金额(价税合计金额)
     */
    private BigDecimal planPayAmount;
    /**
     * 不含税金额
     */
    private BigDecimal noTaxAmount;
    /**
     * 税额
     */
    private BigDecimal taxAmount;

    private List<ClmsPaymentDuty> clmsPaymentDutyList;

}

