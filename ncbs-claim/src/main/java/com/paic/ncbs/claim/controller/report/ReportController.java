package com.paic.ncbs.claim.controller.report;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.AccidentReasonDetailTypeEnum;
import com.paic.ncbs.claim.common.enums.AccidentReasonTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyStatusEnum;
import com.paic.ncbs.claim.common.enums.ReportSubModeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsInsuredPresonDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyPlanDTO;
import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.PlyApplyFreeze;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.report.ReportInfoDTO;
import com.paic.ncbs.claim.model.dto.report.ReportStatDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.ReportRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskDomainDTO;
import com.paic.ncbs.claim.model.dto.settle.PetDetailDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoExDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.RiskObjectDTO;
import com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.report.LinkManVO;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.model.vo.report.ReportInfoExVO;
import com.paic.ncbs.claim.model.vo.report.ReportQueryVO;
import com.paic.ncbs.claim.model.vo.report.SaveReportInfoVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.accident.HugeAccidentInfoService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.report.AcceptRecordService;
import com.paic.ncbs.claim.service.report.EstimateLossService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import com.paic.ncbs.claim.service.report.ReportService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.trace.PersonTraceService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报案
 */
@Api(tags = "报案信息")
@RestController
@RequestMapping("/report")
@RefreshScope
public class ReportController {
    
    @Autowired
    private ReportService reportService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ReportInfoExService reportInfoExService;

    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;

    @Autowired
    private AcceptRecordService acceptRecordService;
    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private EstimateLossService estimateLossService;
    @Autowired
    private HugeAccidentInfoService accidentInfoService;
    @Autowired
    private OcasMapper ocasMapper;

    @Value("${pos.api.enable:true}")
    private boolean posApiEnable;

    @Value("${productCode.childMedical}")
    private String CLAIM_HEALTH_INSURANCE_PRODUCT_CODE;
    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private PersonTraceService personTraceService;

    @RequestMapping(value = "/updataReportInfoEx", method = RequestMethod.POST)
    @ApiOperation(value = "根据保单号更新报案信息")
    public ResponseResult<Object> updataReportInfoEx(@RequestBody ReportInfoExVO reportInfoExVO) {
        if (RapeCheckUtil.isEmpty(reportInfoExVO.getReportNo())) {
            return ResponseResult.fail(GlobalResultStatus.NULL_ERROR.getCode(),GlobalResultStatus.NULL_ERROR.format("reportNo"));
        }
        if (RapeCheckUtil.isEmpty(reportInfoExVO.getIsRepeatReport())) {
            return ResponseResult.fail(GlobalResultStatus.NULL_ERROR.getCode(),GlobalResultStatus.NULL_ERROR.format("isRepeatReport"));
        }

        ReportInfoExEntity reportInfoExEntity = new ReportInfoExEntity();
        BeanUtils.copyProperties(reportInfoExVO, reportInfoExEntity);
        reportInfoExService.updataReportInfoExByReportNo(reportInfoExEntity);
        return ResponseResult.success();
    }


    ///saveReportInfo入参校验
    private void reportInfoCheck(SaveReportInfoVO reportInfo) {
        if (CommonConstant.YES.equals(reportInfo.getIsHugeAccident())) {
            RapeCheckUtil.checkParamEmpty(reportInfo.getHugeAccidentName(), "重灾名称");
            if (RapeCheckUtil.isLengthExceed(reportInfo.getHugeAccidentName(),200)){
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("重灾名称长度不能超过200"));
            }
        }

        //RapeCheckUtil.checkParamEmpty(reportInfo.getInsuredApplyStatus(), "出险者现状");
        //RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentType(), "事故类型");
        //RapeCheckUtil.checkParamEmpty(reportInfo.getInsuredApplyType(), "出险类型");
        RapeCheckUtil.checkParamEmpty(reportInfo.getLossClass(), "损失类别");
        RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentCauseLevel2(), "出险原因");
       /* if(Objects.equals(reportInfo.getAccidentCauseLevel1(), AccidentReasonTypeEnum.ACCIDENT_004.getCode()) && Objects.equals(reportInfo.getAccidentCauseLevel2(), AccidentReasonDetailTypeEnum.ACCIDENT_DETAIL_TYPE_2.getCode())){
            RapeCheckUtil.checkParamEmpty(reportInfo.getInjuryReasonCode(), "损伤外部原因代码");
        }*/
        if (RapeStringUtils.getLength(reportInfo.getAccidentDetail()) > 1000) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("事故描述过长"));
        }
        RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentDate(), "事故日期");
        // RapeCheckUtil.checkParamEmpty(reportInfo.getCertificateNo(), "证件号");
        if ("AHCS_BATCH_ACCEPT_RECORD".equals(reportInfo.getReportAcceptUm())) {
            return;
        }

        RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentPlace(), "事故地点空");
        RapeCheckUtil.checkParamEmpty(reportInfo.getWhetherOutSideAccident(), "是否境外出险空");

        if ("0".equals(reportInfo.getWhetherOutSideAccident())) {
            RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentCity(), "城市");
            RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentProvince(), "省份");
            RapeCheckUtil.checkParamEmpty(reportInfo.getAccidentCounty(), "区县");
        }
        if (CommonConstant.YES.equals(reportInfo.getIsEasyCase())) {
            return;
        }

        if (StringUtils.isEmptyStr(reportInfo.getReportAcceptUm())) {
            RapeCheckUtil.checkParamEmpty(WebServletContext.getUserId(), "UM");
        }

        if (StringUtils.isNotEmpty(reportInfo.getCostEstimate()) && !(reportInfo.getCostEstimate().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
        }
        //RapeCheckUtil.checkParamEmpty(reportInfo.getCaseClass(), "案件类别");
        //RapeCheckUtil.checkParamEmpty(reportInfo.getIsSuffice(), "单证");
        List<LinkManEntity> linkManList = reportInfo.getLinkManList();
        for (LinkManEntity linkMan : linkManList) {
            RapeCheckUtil.checkParamEmpty(linkMan.getLinkManName(), "联系人姓名");
            if (RapeCheckUtil.isLengthExceed(linkMan.getLinkManName(),40)){
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("联系人长度不能超过40"));
            }
            if (RapeCheckUtil.isLengthExceed(linkMan.getApplicantPerson(),40)){
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("报案人长度不能超过40"));
            }
            RapeCheckUtil.checkParamEmpty(linkMan.getLinkManRelation(), "与被保险人关系");
            RapeCheckUtil.checkParamEmpty(linkMan.getLinkManTelephone(), "联系电话");
            RapeCheckUtil.checkParamEmpty(linkMan.getSendMessage(), "短信发送");
        }
        if (reportInfo.getReportAccidentFlight() != null) {
            if (StringUtils.isNotEmpty(reportInfo.getReportAccidentFlight().getFlightNo()) && reportInfo.getReportAccidentFlight().getFlightNo().length() > 20) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("航班号位数不应超过20位"));
            }
            if (StringUtils.isNotEmpty(reportInfo.getReportAccidentFlight().getReplaceFlight()) && reportInfo.getReportAccidentFlight().getReplaceFlight().length() > 20) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("代替航班号位数不应超过20位"));
            }
            if (StringUtils.isNotEmpty(reportInfo.getReportAccidentFlight().getEleTicketNo()) && reportInfo.getReportAccidentFlight().getEleTicketNo().length() > 20) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("电子客票号位数不应超过20位"));
            }
        }
        if (reportInfo.getReportAccidentBaggage() != null) {
            if (StringUtils.isNotEmpty(reportInfo.getReportAccidentBaggage().getFlightNo()) && reportInfo.getReportAccidentBaggage().getFlightNo().length() > 20) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("航班号位数不应超过20位"));
            }
            if (reportInfo.getReportAccidentBaggage().getCostEstimate() != null) {
                if (!(reportInfo.getReportAccidentBaggage().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                    throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
                }
            }

        }
        if (reportInfo.getReportAccidentTravel() != null && reportInfo.getReportAccidentTravel().getCostEstimate() != null) {
            if (!(reportInfo.getReportAccidentTravel().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
            }
        }
        if (reportInfo.getReportAccidentLoss() != null && reportInfo.getReportAccidentLoss().getCostEstimate() != null) {
            if (!(reportInfo.getReportAccidentLoss().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
            }
        }
        if (reportInfo.getReportAccidentExam() != null && reportInfo.getReportAccidentExam().getCostEstimate() != null) {
            if (!(reportInfo.getReportAccidentExam().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
            }
        }
        if (reportInfo.getReportAccidentOther() != null && reportInfo.getReportAccidentOther().getCostEstimate() != null) {
            if (!(reportInfo.getReportAccidentOther().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
            }
        }
        if (reportInfo.getReportAccidentTraffic() != null && reportInfo.getReportAccidentTraffic().getCostEstimate() != null) {
            if (!(reportInfo.getReportAccidentTraffic().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
            }
        }
        if (reportInfo.getReportAccidentPet() != null && reportInfo.getReportAccidentPet().getCostEstimate() != null) {
            if (!(reportInfo.getReportAccidentPet().getCostEstimate().toString().matches(RegexStrings.REG_MONEY_COST_ESTIMATE))) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估金额不能过千万且最多只支持两位小数"));
            }
        }

//        if (StringUtils.isEmptyStr(reportInfo.getAccidentDetail()) ) {
//            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("出险过程描述为空"));
//        }
    }

    /**
     * 重复报案校验
     * @param reportInfoDto 报案info
     * @param policyDomainDtoList 保单明细列表
     * @param expiredPolicyNos
     */
    private void validateReportData(ReportInfoDTO reportInfoDto, List<AhcsPolicyDomainDTO> policyDomainDtoList, List<String> expiredPolicyNos) {
        long start = System.currentTimeMillis();

        List<String> newPlocyNos = new ArrayList<>();
        Set<String> reportNos = new HashSet<>();
        if (RapeCheckUtil.isNotEmpty(policyDomainDtoList)) {
            for (AhcsPolicyDomainDTO dto : policyDomainDtoList) {
                newPlocyNos.add(dto.getAhcsPolicyInfo().getPolicyNo());
            }
        }
        List<String> oldPolicyNos = new ArrayList<>();
        Date accidentDate = reportInfoDto.getAccidentDate();
        List<HistoryCaseDTO> list = reportService.getAccidentDateReport(accidentDate, reportInfoDto.getCertificateNo(),reportInfoDto.getClientName(),reportInfoDto.getCertificateType());
        if (RapeCheckUtil.isNotEmpty(list)) {
            for (HistoryCaseDTO dto : list) {
                reportNos.add(dto.getReportNo());
                oldPolicyNos.add(dto.getPolicyNo());
            }
        }
        LogUtil.info("客户报案新关联保单={}", JSON.toJSONString(newPlocyNos));
        LogUtil.info("客户报案已关联保单={}", JSON.toJSONString(oldPolicyNos));

        newPlocyNos.removeAll(oldPolicyNos);
        LogUtil.info("客户剩下保单={}", JSON.toJSONString(newPlocyNos));

        if (RapeCheckUtil.isListEmpty(newPlocyNos)) {
            StringBuilder s = new StringBuilder();
            for (String reportNo : reportNos) {
                s.append(reportNo).append(",");
            }
            throw new GlobalBusinessException(GlobalResultStatus.EX_REPORT_944000.getCode(),
                    GlobalResultStatus.EX_REPORT_944000.format(s.substring(0, s.length() - 1)));
        }
        policyDomainDtoList.forEach(p->{
            if (oldPolicyNos.contains(p.getAhcsPolicyInfo().getPolicyNo())){
                expiredPolicyNos.add(p.getAhcsPolicyInfo().getPolicyNo());
            }
        });
        policyDomainDtoList.removeIf(ahcsPolicyDomainDTO -> oldPolicyNos.contains(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getPolicyNo()));
        long end = System.currentTimeMillis();
        LogUtil.info("校验重复报案耗时:{}", end - start);
    }


    private AcceptRecordDTO saveReceiveVoucherInfo(ReportInfoDTO reportInfo) {
        long start = System.currentTimeMillis();
        AcceptRecordDTO acceptRecordDTO = this.buildReceiveVoucherInfo(reportInfo);
        //判断是否单证齐全不为空
        if (StringUtils.isNotEmpty(acceptRecordDTO.getIsSuffice())) {

            LogUtil.info("构建案件联系人信息:{}", JSON.toJSONString(acceptRecordDTO));
            acceptRecordService.addAcceptRecord(acceptRecordDTO);
        }
        long end = System.currentTimeMillis();
        LogUtil.info("保存收单耗时:{}", end - start);
        return acceptRecordDTO;
    }

    private AcceptRecordDTO buildReceiveVoucherInfo(ReportInfoDTO reportInfoDTO) {
        String uid = WebServletContext.getUserId();
        AcceptRecordDTO acceptRecordDTO = new AcceptRecordDTO();
        List<LinkManVO> linkManList = reportInfoDTO.getLinkManList().stream().map(linkManEntity -> {
            LinkManVO linkManVO = new LinkManVO();
            BeanUtils.copyProperties(linkManEntity, linkManVO);
            return linkManVO;
        }).collect(Collectors.toList());
        acceptRecordDTO.setLinkManList(linkManList);
        acceptRecordDTO.setReportNo(reportInfoDTO.getReportNo());
        acceptRecordDTO.setIsSuffice(reportInfoDTO.getIsSuffice());
        acceptRecordDTO.setPriorityReason(reportInfoDTO.getPriorityReason());
        acceptRecordDTO.setRemark(reportInfoDTO.getRemark());
        acceptRecordDTO.setCaseTimes(Integer.valueOf(reportInfoDTO.getCaseTimes()));
        acceptRecordDTO.setCreatedBy(uid);
        acceptRecordDTO.setReceiveVoucherUm(uid);
        acceptRecordDTO.setCaseType(reportInfoDTO.getCaseType());
        acceptRecordDTO.setTacheName("report");
        return acceptRecordDTO;
    }


    /**
     * 对接批改实现报案抄单
     * @param reportInfoVO
     * @return
     *
     * 入参示例
     * {
     *     "clientNo": "P00880680248",
     *     "policyNos": ["11100006600010000618"],
     *     "isSpecialReport": "N",
     *     "clientName": "白凤九",
     *     "birthday": "1979-12-17",
     *     "certificateNo": "620100197912173047",
     *     "caseClass": "1",
     *     "isSuffice": "Y",
     *     "remark": "",
     *     "priorityReason": "",
     *     "caseType": "",
     *     "linkManList": [
     *         {
     *             "applicantPerson": "李子起",
     *             "applicantType": "01",
     *             "certificateType": "",
     *             "certificateNo": "",
     *             "linkManName": "李子起",
     *             "linkManRelation": "00",
     *             "linkManTelephone": "12312341234",
     *             "sendMessage": "N"
     *         }
     *     ],
     *     "reportFlowFlag": "N",
     *     "insuredApplyStatus": "IS_0101",
     *     "accidentDate": "2022-05-10 00:00:00",
     *     "therapyType": [
     *         "THE_0301"
     *     ],
     *     "accidentType": "AT_0402",
     *     "isHugeAccident": "N",
     *     "hugeAccidentType": "",
     *     "hugeAccidentId": "",
     *     "whetherOutSideAccident": "0",
     *     "accidentProvince": "110000",
     *     "accidentCity": "110100",
     *     "accidentCounty": "110118",
     *     "accidentArea": "",
     *     "accidentAreaText": "",
     *     "accidentNationList": [],
     *     "accidentNation": "",
     *     "accidentNationText": "",
     *     "accidentOutPlace": "",
     *     "accidentPlace": "北京北京市密云区87"
     * }
     * 新建报案调用该接口：校验、调用保全接口获取保单数据抄单、初始化报案跟踪和收单等业务表、创建报案跟踪任务
     *
     */
    @PostMapping(value = "/saveReportInfoStore")
    public ResponseResult<Object> saveReportInfoStore(@RequestBody SaveReportInfoVO reportInfoVO) {
        LogUtil.audit("新建报案保存报案入参{}",JSON.toJSONString(reportInfoVO));
        long a = System.currentTimeMillis();
        // 检验
        this.reportInfoCheck(reportInfoVO);
        long b = System.currentTimeMillis();

        // 报案数据
        ReportInfoDTO reportInfo = this.initReportInfoStore(reportInfoVO);
        long c = System.currentTimeMillis();

        List<String> expiredPolicyNos =new ArrayList<>();

        // 补充报案领域模型
        AhcsDomainDTO ahcsDomainDTO = this.initReportDomainDataStore(reportInfo,expiredPolicyNos);

        //报案标的
        initCaseRiskProperty(reportInfoVO,ahcsDomainDTO);

        // 保存报案 人伤信息初始化
        initReportRiskSubProp(reportInfoVO,ahcsDomainDTO);

        //操作记录
        operationRecordService.insertOperationRecordByLabour(ahcsDomainDTO.getReportNo(), BpmConstants.OC_REPORT, "柜面报案", null);
        long d = System.currentTimeMillis();

        /**
         * ########## 报案信息保存 start ################
         * 保存案件信息  新建报案 + 启动工作流  + 案件调度规则查询并确定理赔处理人
         */
        reportService.createReport(reportInfo, ahcsDomainDTO);
        /**
         * ########## 报案信息保存 end ################
         */

        long f = System.currentTimeMillis();
        Map<String, Object> map = new HashMap<>();

        // 保存收单信息
        AcceptRecordDTO acceptRecordDTO = this.saveReceiveVoucherInfo(reportInfo);
        map.put("taskId", acceptRecordDTO.getTaskId());
        map.put("caseTypeName", acceptRecordDTO.getCaseTypeName());
        map.put("reportNo", ahcsDomainDTO.getReportNo());
        if (expiredPolicyNos.isEmpty()){
            map.put("msg", null);
        }else {
            map.put("msg", "被剔除的保单:"+expiredPolicyNos.toString());
        }
        long g = System.currentTimeMillis();
        LogUtil.info(
                "REPORT_TIME:检验入参耗时={}，报案数据耗时={}，补充报案领域模型耗时={}，保存案件信息耗时、新建报案启动工作流耗时={}，保存收单信息（调AHSC接口）耗时={}",
                b - a, c - b, d - c, f - d, g - f);
        LogUtil.info("REPORT_TIME:报案总耗时={}", g - a);

        //发送报案短信
        sendReportSms(ahcsDomainDTO.getReportNo(),reportInfoVO);

        //执行报即立规则
        estimateLossService.runReportEstimateRule(ahcsDomainDTO,reportInfoVO.getInsuredApplyType(),reportInfoVO.getReportAcceptUm());


        //mq同步
        LogUtil.info("mq同步 casetimes :{}",ahcsDomainDTO.getCaseTimes());
//        mqProducerRegistService.syncRegistLink(ahcsDomainDTO.getReportNo(),1);
        // 报案成功返回报案号
        personTraceService.saveTraceMain(ahcsDomainDTO.getReportNo(),Integer.valueOf(ahcsDomainDTO.getCaseTimes()));
        return ResponseResult.success(map);
    }

    private void initCaseRiskProperty(SaveReportInfoVO reportInfoVO,AhcsDomainDTO ahcsDomainDTO){
        try {
            List<CaseRiskPropertyDTO> saveCaseRiskList = new ArrayList<>();
            for (AhcsPolicyDomainDTO policyDomain : ahcsDomainDTO.getAhcsPolicyDomainDTOs()) {
                // 判断是否存储标的信息
                if (!riskPropertyService.isRiskProperty(policyDomain.getTargetType(), policyDomain.getAhcsPolicyInfo().getProductClass())) {
                    continue;
                }

                // 组装标的信息
                List<CaseRiskPropertyDTO> riskPropertyList = reportInfoVO.getRiskPropertyList();
                if(ListUtils.isNotEmpty(riskPropertyList)) {
                    for (CaseRiskPropertyDTO caseRiskPropertyDTO : riskPropertyList) {

                        caseRiskPropertyDTO.setIdCaseRiskProperty(UuidUtil.getUUID());
                        caseRiskPropertyDTO.setReportNo(ahcsDomainDTO.getReportNo());
                        caseRiskPropertyDTO.setCaseTimes(1);
                        caseRiskPropertyDTO.setPolicyNo(policyDomain.getAhcsPolicyInfo().getPolicyNo());
                        caseRiskPropertyDTO.setTaskId("report1");
                        caseRiskPropertyDTO.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
                        caseRiskPropertyDTO.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());

                        caseRiskPropertyDTO.setRiskGroupNo(reportInfoVO.getRiskGroupNo());
                        caseRiskPropertyDTO.setRiskGroupName(reportInfoVO.getRiskGroupName());
                        caseRiskPropertyDTO.setIdPlyRiskProperty(
                                Optional.ofNullable(reportInfoVO.getRiskPropertyList())
                                        .filter(list -> !list.isEmpty())
                                        .map(list -> list.get(0))
                                        .map(riskProperty -> riskProperty.getRiskPropertyMap())
                                        .map(map -> (String) map.get("idPlyRiskProperty"))
                                        .orElse("")
                        );

                        // 对象转换成json字符串存储，标的信息动态展示用
                        caseRiskPropertyDTO.setRiskDetail(JSON.toJSONString(reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap()));

                        // 标的类型
                        caseRiskPropertyDTO.setRiskGroupType(reportInfoVO.getRiskPropertyList().get(0).getRiskGroupType());

                        // 雇主责任险的情况，雇员信息按字段存储，数据组报送或取数用？
                        if("996".equals(caseRiskPropertyDTO.getRiskGroupType())){
                            caseRiskPropertyDTO.setCertificateType((String) reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap().get("certificateType"));
                            caseRiskPropertyDTO.setCertificateNo((String) reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap().get("certificateNo"));
                            caseRiskPropertyDTO.setName((String) reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap().get("name"));
                            caseRiskPropertyDTO.setAge((Integer) reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap().get("age"));
                            caseRiskPropertyDTO.setSex((String) reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap().get("sex"));
                            caseRiskPropertyDTO.setBirthDay(DateUtils.parse2Date((String) reportInfoVO.getRiskPropertyList().get(0).getRiskPropertyMap().get("birthday")));
                        }

                        saveCaseRiskList.add(caseRiskPropertyDTO);
                    }
                }
            }
            ahcsDomainDTO.setCaseRiskPropertyList(saveCaseRiskList);
        }catch (Exception e){
            LogUtil.warn("组装【标的信息】异常,不影响报案流程",e);
        }

    }

    private void initReportRiskSubProp(SaveReportInfoVO reportInfoVO,AhcsDomainDTO ahcsDomainDTO){

//        if (!riskPropertyService.isRiskProperty(ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(0).getTargetType(), ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(0).getAhcsPolicyInfo().getProductClass()))
//        	return;
        if(reportInfoVO.getNoPeopleHurtVO() == null || reportInfoVO.getNoPeopleHurtVO().getClmsPersonalInjuryDeathInfoDTO() == null)
        	return;
    	ahcsDomainDTO.setClmsPersonalInjuryDeathInfoDTO(reportInfoVO.getNoPeopleHurtVO().getClmsPersonalInjuryDeathInfoDTO());
    }

    private RiskDomainDTO setValues(CaseRiskPropertyDTO dto,AhcsDomainDTO ahcsDomainDTO){
        if(dto != null){
            dto.setReportNo(ahcsDomainDTO.getReportNo());
            dto.setCaseTimes(1);
            dto.setTaskId("report1");
            dto.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            dto.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            dto.setIdCaseRiskProperty(UuidUtil.getUUID());
        }

        RiskDomainDTO riskDomainDTO = new RiskDomainDTO();
        riskDomainDTO.setReportNo(ahcsDomainDTO.getReportNo());
        riskDomainDTO.setCaseTimes(1);
        riskDomainDTO.setTaskId("report1");
        riskDomainDTO.setUserId(ahcsDomainDTO.getReportAcceptUm());
        return riskDomainDTO;
    }

    private ReportInfoDTO initReportInfoStore(SaveReportInfoVO reportInfoVO) {
        if (StringUtils.isNotEmpty(reportInfoVO.getReportAcceptUm())) {
            reportInfoVO.setReportAcceptUm(reportInfoVO.getReportAcceptUm());
        } else {
            reportInfoVO.setReportAcceptUm(WebServletContext.getUserId());
        }
        ReportInfoDTO reportInfo = new ReportInfoDTO();

        reportInfo.setInjuryReasonCode(reportInfoVO.getInjuryReasonCode());

        // 从画面过来的标的信息初始化赋值
        if (reportInfoVO.getRiskPropertyList()!= null && !reportInfoVO.getRiskPropertyList().isEmpty()) {
            reportInfo.setRiskPropertyList(reportInfo.getRiskPropertyList());
        }

        if (reportInfoVO.getReportAccidentBaggage() != null) {
            ReportAccidentBaggageEntity reportAccidentBaggage = new ReportAccidentBaggageEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentBaggage(), reportAccidentBaggage);
            reportInfo.setReportAccidentBaggage(reportAccidentBaggage);
            reportInfo.getReportAccidentBaggage().setBaggageDelayType(RapeStringArrayUtil
                    .appendVerticalBar(reportInfoVO.getReportAccidentBaggage().getBaggageDelayType()));
        }
        if (reportInfoVO.getReportAccidentTravel() != null) {
            ReportAccidentTravelEntity reportAccidentTravel = new ReportAccidentTravelEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentTravel(), reportAccidentTravel);
            reportInfo.setReportAccidentTravel(reportAccidentTravel);
            reportInfo.getReportAccidentTravel().setChangeType(
                    RapeStringArrayUtil.appendVerticalBar(reportInfoVO.getReportAccidentTravel().getChangeType()));
        }
        if (reportInfoVO.getReportAccidentLoss() != null) {
            ReportAccidentLossEntity reportAccidentLoss = new ReportAccidentLossEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentLoss(), reportAccidentLoss);
            reportInfo.setReportAccidentLoss(reportAccidentLoss);
            reportInfo.getReportAccidentLoss().setLossType(
                    RapeStringArrayUtil.appendVerticalBar(reportInfoVO.getReportAccidentLoss().getLossType()));
        }
        if (reportInfoVO.getReportAccidentExam() != null) {
            ReportAccidentExamEntity reportAccidentExam = new ReportAccidentExamEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentExam(), reportAccidentExam);
            reportInfo.setReportAccidentExam(reportAccidentExam);
            reportInfo.getReportAccidentExam().setNoPassType(
                    RapeStringArrayUtil.appendVerticalBar(reportInfoVO.getReportAccidentExam().getNoPassType()));
        }
        if (reportInfoVO.getReportAccidentOther() != null) {
            ReportAccidentOtherEntity reportAccidentOther = new ReportAccidentOtherEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentOther(), reportAccidentOther);
            reportInfo.setReportAccidentOther(reportAccidentOther);
            reportInfo.getReportAccidentOther().setOtherType(
                    RapeStringArrayUtil.appendVerticalBar(reportInfoVO.getReportAccidentOther().getOtherType()));
        }
        if (reportInfoVO.getReportAccidentTraffic() != null) {
            ReportAccidentTrafficEntity reportAccidentTraffic = new ReportAccidentTrafficEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentTraffic(), reportAccidentTraffic);
            reportInfo.setReportAccidentTraffic(reportAccidentTraffic);
            reportInfo.getReportAccidentTraffic().setSteamerDelayCase(RapeStringArrayUtil
                    .appendVerticalBar(reportInfoVO.getReportAccidentTraffic().getSteamerDelayCase()));
        }
        if (reportInfoVO.getReportAccidentFlight() != null) {
            ReportAccidentFlightEntity reportAccidentFlight = new ReportAccidentFlightEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentFlight(), reportAccidentFlight);
            reportInfo.setReportAccidentFlight(reportAccidentFlight);
        }
        if(reportInfoVO.getReportAccidentPet() != null){
            ReportAccidentPetEntity reportAccidentPet = new ReportAccidentPetEntity();
            BeanUtils.copyProperties(reportInfoVO.getReportAccidentPet(), reportAccidentPet);
            reportInfo.setReportAccidentPet(reportAccidentPet);
        }

        if(RapeCheckUtil.isEmpty(reportInfoVO.getLinkManList())){
            List<LinkManEntity> linkMans = new ArrayList<>();
            LinkManEntity linkMan = new LinkManEntity();
            linkMan.setLinkManName(reportInfoVO.getClientName());
            linkMan.setLinkManTelephone(reportInfoVO.getReportPersonConPhone());
            linkMan.setLinkManRelation("00"); //本人
            linkMan.setApplicantPerson(reportInfoVO.getClientName());
            linkMan.setApplicantType(ReportConstant.APPLICANT_TYPE_ONE);	//被保险人
            linkMan.setCertificateType(reportInfoVO.getCertificateType());
            linkMan.setCertificateNo(reportInfoVO.getCertificateNo());
            linkMans.add(linkMan);
            reportInfoVO.setLinkManList(linkMans);
        }
        BeanUtils.copyProperties(reportInfoVO, reportInfo);

        LogUtil.info("报案入参VO转换后的dto出参:{}", JSON.toJSONString(reportInfo));
        return reportInfo;
    }

    private AhcsDomainDTO initReportDomainDataStore(ReportInfoDTO reportInfoDto, List<String> expiredPolicyNos) {

        AhcsDomainDTO ahcsDomainDTO = new AhcsDomainDTO();
        ReportInfoEntity reportInfo = new ReportInfoEntity();
        List<AhcsPolicyDomainDTO> policyDomainDtoList = new ArrayList<>();
        // 有效保单列表
        Map<String,String> param = new HashMap<>();
        List<String> policyNos = reportInfoDto.getPolicyNos();
        //这个参数设置是给后面查询保单理赔历史核保信息用。
        ahcsDomainDTO.setQueryUwInfoPolicys(policyNos);
        List<String> idPlyRiskPersons = reportInfoDto.getIdPlyRiskPersons();
        // 保单号为空校验
        if(ListUtils.isEmptyList(policyNos)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("保单号不能为空"));
        }

        checkTargetType(policyNos);

        // 根据保单号调用保全接口获取保单明细，根据被保人的id筛去保单明细中的其它被保人
        String accidentDateStr = DateUtils.dateFormat(reportInfoDto.getAccidentDate(), DateUtils.FULL_DATE_STR);
        if(posApiEnable){
            for (int i = 0; i < policyNos.size(); i++) {
                CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNos.get(i),
                        accidentDateStr);
//                queryVO.setTime(queryVO.getTime().substring(0, queryVO.getTime().length()-8) + "23:59:59");
                queryVO.setTime(queryVO.getTime());
                queryVO.setRiskPersonId(idPlyRiskPersons.get(i));
                queryVO.setName(reportInfoDto.getClientName());
                queryVO.setCertificateNo(reportInfoDto.getCertificateNo());
                queryVO.setCertificateType(reportInfoDto.getCertificateType());
                policyDomainDtoList.add(customerInfoStoreSAO.getPolicyDomainInfo(queryVO)) ;
            }
        }else{
            for (int i = 0; i < policyNos.size(); i++) {
                param.put("policyNo", policyNos.get(i));
                param.put("idPlyRiskPerson", idPlyRiskPersons.get(i));
                param.put("time", accidentDateStr);

                param.put("name", reportInfoDto.getClientName());
                param.put("certificateType", reportInfoDto.getCertificateNo());
                param.put("certificateNo", reportInfoDto.getCertificateType());
                policyDomainDtoList.addAll(customerInfoStoreSAO.getPolicyDomainInfo(param)) ;
            }
        }

        // 剔除保单出险日都在范围外的保单 出险时间取当天 23:59:59 处理T+0当天报案问题
//        Date accidentDate = DateUtils.endOfDay(reportInfoDto.getAccidentDate());
        Date accidentDate = reportInfoDto.getAccidentDate();
        for (AhcsPolicyDomainDTO policyDomain : policyDomainDtoList) {
            AhcsPolicyInfoEntity policy = policyDomain.getAhcsPolicyInfo();
            PolicyInfoExDTO policyInfoExDTO = policyDomain.getPolicyInfoExDTO();

            // 删除批改在途
            // if (ConfigConstValues.YES.equals(policyDomain.getIsProcess()) && !ConstValues.SCENE20022.equals(policyDomain.getScenceList())) {
            //     throw new GlobalBusinessException(GlobalResultStatus.ERROR.format(policy.getPolicyNo()+"批改在途！"));
            // }

            if(PolicyStatusEnum.TWO.getType().equals(policy.getPolicyStatus())){
                //注销
                expiredPolicyNos.add(policy.getPolicyNo());
                continue;
            }
            if (policy.getInsuranceBeginTime().equals(policy.getInsuranceEndTime())){
                // 未生效退保
                expiredPolicyNos.add(policy.getPolicyNo());
                continue;
            }
            //追溯期、报告期:保单起期-追溯期<=事故日期<=保单止期+报告期
            int extendReportDate = Objects.nonNull(policy.getExtendReportDate()) ? policy.getExtendReportDate() : 0;
            int prosecutionPeriod = Objects.nonNull(policy.getProsecutionPeriod()) ? policy.getProsecutionPeriod() : 0;

            if ((DateUtils.addDate(DateUtils.beginOfDay(policy.getInsuranceBeginTime()),-prosecutionPeriod).after(accidentDate) && DateUtils.addDate(DateUtils.beginOfDay(policyInfoExDTO.getInsuranceBeginTime()),-prosecutionPeriod).after(accidentDate))
                    || (DateUtils.addDate(DateUtils.endOfDay(policy.getInsuranceEndTime()),extendReportDate).before(accidentDate) && DateUtils.addDate(DateUtils.endOfDay(policyInfoExDTO.getInsuranceEndTime()),extendReportDate).before(accidentDate))){
                //不在起止期内
                expiredPolicyNos.add(policy.getPolicyNo());
                continue;
            }


            if(PolicyStatusEnum.STOP.getType().equals(policy.getPolicyStatus())){
                Date stopDate = ocasMapper.getPolicyStopDate(policy.getPolicyNo());
                if(stopDate != null && stopDate.before(accidentDate)){
                    //中止：批改生效日期
                    expiredPolicyNos.add(policy.getPolicyNo());
                }

            }

            if(ListUtils.isNotEmpty(policyDomain.getPlyApplyFreezes())){
                //冻结起止期
                for (PlyApplyFreeze freeze : policyDomain.getPlyApplyFreezes()) {
                    if(freeze.getFreezeStarttime()==null || freeze.getFreezeEndtime()==null){
                        continue;
                    }
                    LocalDateTime accidentLocalDate = DateUtils.date2LocalDateTime(accidentDate);
                    if(freeze.getFreezeStarttime().isBefore(accidentLocalDate) && freeze.getFreezeEndtime().isAfter(accidentLocalDate)){
                        //在冻结期内
                        expiredPolicyNos.add(policy.getPolicyNo());
                        break;
                    }

                }

            }

        }

        LogUtil.info("剔除保单出险日都在范围外的保单-1-={}", JSON.toJSONString(expiredPolicyNos));
        policyDomainDtoList.removeIf(policyDTO -> expiredPolicyNos.contains(policyDTO.getAhcsPolicyInfo().getPolicyNo()));

        if(RapeCheckUtil.isEmpty(policyDomainDtoList)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("客户出险时间无有效保单"));
        }

        AhcsPolicyDomainDTO ahcsPolicyDomainDTO = policyDomainDtoList.get(CommonConstant.ZERO);
        if(RapeCheckUtil.isEmpty(ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs())){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("无被保险人信息 "));
        }

        //少儿医疗产品报案校验 mapeiyuan20231117 start
        /**if(null != ahcsPolicyDomainDTO && null != ahcsPolicyDomainDTO.getPayInfoList() && null != ahcsPolicyDomainDTO.getAhcsPolicyInfo()){
            LogUtil.info("productCode:"+ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductCode());
            if(CLAIM_HEALTH_INSURANCE_PRODUCT_CODE.contains(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductCode())){
                for(PayInfoDTO payInfoDTO : ahcsPolicyDomainDTO.getPayInfoList()){
                    //实交保费=总保费才可报案
                    LogUtil.info("保单:"+ahcsPolicyDomainDTO.getAhcsPolicyInfo().getPolicyNo()+"实交保费"+payInfoDTO.getActualPremium()+"总保费"+payInfoDTO.getAgreePremium());
                    if(null == payInfoDTO.getActualPremium() || payInfoDTO.getActualPremium().compareTo(payInfoDTO.getAgreePremium()) != 0){
                        throw new GlobalBusinessException((GlobalResultStatus.ERROR.format("保单"+ahcsPolicyDomainDTO.getAhcsPolicyInfo().getPolicyNo()+"保费未缴全，不允许出险")));
                    }
                }
            }
        }else{
            throw new GlobalBusinessException((GlobalResultStatus.ERROR.format("无保单支付信息")));
        }
        LogUtil.info("少儿医疗产品报案校验通过");
         */
        // 少儿医疗产品报案校验 mapeiyuan20231117 end

        /*
         * 重新赋值出险时间：如果出险时间小于每一个保单的生效时间，则将出险时间赋值为出险时间当天+ 23:59:59
         * 不太好的逻辑补偿，针对门诊险这样的t+0责任处理（为什么一开始不要求他们正常传值）
         * 当出险时间小于所有保单的生效时间时，将出险时间赋值为出险时间当天+ 23:59:59
         * 过滤保单的生效时间小于出险时间的保单 ： 重新赋值出险时间
         *
         */
        Optional<AhcsPolicyDomainDTO> conformPolicyOpt = policyDomainDtoList.stream().filter(item ->
                DateUtils.compareTimeBetweenDate(item.getAhcsPolicyInfo().getInsuranceBeginTime(),
                        reportInfoDto.getAccidentDate())).findAny();
        if (!conformPolicyOpt.isPresent()) {
            // accidentDate 这里上面已经使用endOfDay处理了 直接赋值
//            reportInfoDto.setAccidentDate(accidentDate);
            reportInfoDto.setAccidentDate(new Date());
        }

        // 重复报案校验,2022-11-28 应业务老师要求,  不校验重复报案
//        this.validateReportData(reportInfoDto, policyDomainDtoList,expiredPolicyNos);
        LogUtil.info("剔除保单出险日都在范围外的保单-2-={}", JSON.toJSONString(expiredPolicyNos));
        ReportCustomerInfoEntity customer = matchCustomer(ahcsPolicyDomainDTO,reportInfoDto);
        RapeCheckUtil.checkParamEmpty(customer,"无被保险人信息 ");
        if("200".equals(customer.getClientCluster()) || "020".equals(customer.getClientCluster())){
            customer.setCertificateType(StringUtils.cancelNull(customer.getCertificateType()));
            customer.setCertificateNo(StringUtils.cancelNull(customer.getCertificateNo()));
        } else {
            RapeCheckUtil.checkParamEmpty(reportInfoDto.getCertificateNo(), "证件号");
        }
        LogUtil.info("抄单返回被保险人信息-={}", JSON.toJSONString(customer));
        String clientNo = customer.getClientNo();
        if (StringUtils.isEmptyStr(clientNo)){
            ReportCustomerInfoEntity customerQuery = new ReportCustomerInfoEntity();
            customerQuery.setCertificateNo(customer.getCertificateNo());
            customerQuery.setCertificateType(customer.getCertificateType());
            customerQuery.setName(customer.getName());
            clientNo = ocasMapper.getClientInfoByFiveInfo(customerQuery);
            customer.setClientNo(clientNo);
        }
        if (StringUtils.isEmptyStr(clientNo)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("被保险人客户号为空，请联系批改维护！"));
        }
        ahcsDomainDTO.setReportCustomerInfo(customer);
        ahcsDomainDTO.setClientNo(clientNo);
        // 新建报案 创建一个报案号 转正不需新建
        String departmentCode = policyDomainDtoList.get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode();
        reportInfoDto.setDepartmentCode(departmentCode);
        if (StringUtils.isEmptyStr(reportInfoDto.getReportNo())) {
            reportInfoDto.setReportNo( commonService.generateNo( NoConstants.REPORT_NO, VoucherTypeEnum.REPORT_CASE_NO,departmentCode)) ;
        }
        ahcsDomainDTO.setAcceptDepartmentCode(departmentCode);// 接报案机构
        reportInfo.setReportType(ReportConstant.NORMAL);// 正常报案
        ahcsDomainDTO.setAhcsPolicyDomainDTOs(policyDomainDtoList);// 保单列表

        ahcsDomainDTO.setReportAcceptUm(reportInfoDto.getReportAcceptUm());// 设置接报案人
        ahcsDomainDTO.setReportNo(reportInfoDto.getReportNo());// 报案号
        if (StringUtils.isNotEmpty(reportInfoDto.getReportMode())) {
            reportInfo.setReportMode(reportInfoDto.getReportMode());// 报案来源
        } else {
            reportInfo.setReportMode(ReportConstant.REPORT_MODE_COUNTER);// 报案来源
        }
        if(ReportConstant.REPORTMODE_INTERNAL.equals(reportInfo.getReportMode())) {
            reportInfo.setReportSubMode(ReportSubModeEnum.INTERNAL_SUB_01.getType());
        }
        ahcsDomainDTO.setReportInfo(reportInfo);

        ReportAccidentEntity reportAccident = new ReportAccidentEntity();
        //出险原因大类
        reportAccident.setAccidentCauseLevel1(reportInfoDto.getAccidentCauseLevel1());
        //出险原因明细类
        reportAccident.setAccidentCauseLevel2(reportInfoDto.getAccidentCauseLevel2());
        reportAccident.setAccidentArea(reportInfoDto.getAccidentArea());
        reportAccident.setOverseaNationCode(reportInfoDto.getAccidentNation());
        reportAccident.setAccidentDate(reportInfoDto.getAccidentDate());
        reportAccident.setAccidentCityCode(reportInfoDto.getAccidentCity());
        reportAccident.setProvinceCode(reportInfoDto.getAccidentProvince());
        reportAccident.setAccidentCountyCode(reportInfoDto.getAccidentCounty());
        reportAccident.setAccidentPlace(reportInfoDto.getAccidentPlace());
        reportAccident.setOverseasOccur(reportInfoDto.getWhetherOutSideAccident());
        // {被保人}于{事故日期}因{出险原因}申请理赔
        String visitReason = reportInfoDto.getVisitReason();
        String accidentDetail;
        if (StringUtils.isNotEmpty(visitReason)){
            accidentDetail = customer.getName() + "于" + accidentDateStr + "因"
                    + AccidentReasonDetailTypeEnum.getName(reportInfoDto.getAccidentCauseLevel2()) + visitReason + "申请理赔";
        } else{
            accidentDetail = customer.getName() + "于" + accidentDateStr + "因"
                    + AccidentReasonDetailTypeEnum.getName(reportInfoDto.getAccidentCauseLevel2()) + "申请理赔";
        }
        reportAccident.setAccidentDetail(StringUtils.isEmptyStr(reportInfoDto.getAccidentDetail()) ? accidentDetail : reportInfoDto.getAccidentDetail());
        ahcsDomainDTO.setReportAccident(reportAccident);

        ReportAccidentExEntity reportAccidentEx = new ReportAccidentExEntity();
        reportAccidentEx.setAccidentType(reportInfoDto.getAccidentType());// 事故类型
        reportAccidentEx.setTherapyType(reportInfoDto.getTherapyType());// 治疗类型
        // 出险者现状
        reportAccidentEx.setInsuredApplyStatus(reportInfoDto.getInsuredApplyStatus());
        reportAccidentEx.setAccidentStatusDetails(reportInfoDto.getAccidentStatusDetails());// 事故现状详情
        // 新增出险类型
        reportAccidentEx.setInsuredApplyType(reportInfoDto.getInsuredApplyType());
        List<String> subCaseClass = reportInfoDto.getSubCaseClass();
        if(ListUtils.isNotEmpty(subCaseClass) && subCaseClass.contains(ChecklossConst.SUB_CLASS_PET_INJURE)){
            List<RiskObjectDTO> riskObjList = ahcsPolicyDomainDTO.getRiskObjectList();
            if(ListUtils.isNotEmpty(riskObjList)){
                PetDetailDTO petDetailDTO = Optional.ofNullable(riskObjList.get(0)).map(RiskObjectDTO::getPetDetailDTO).orElse(null);
                if(petDetailDTO != null){
                    reportAccidentEx.setAccidentExtendInfo(JSON.toJSONString(petDetailDTO));
                }
            }
        }
        ahcsDomainDTO.setReportAccidentEx(reportAccidentEx);

        List<WholeCaseBaseEntity> wholeCaseBaseList = new ArrayList<>();
        WholeCaseBaseEntity wholeCaseBase = new WholeCaseBaseEntity();
        wholeCaseBase.setIsHugeAccident(reportInfoDto.getIsHugeAccident());// 是否重灾
        if (CommonConstant.YES.equals(reportInfoDto.getIsHugeAccident())) {
            wholeCaseBase.setHugeAccidentCode(accidentInfoService.getAccidentCode(reportInfoDto.getHugeAccidentName()));// 重灾编码
        }
        if (StringUtils.isNotEmpty(reportInfoDto.getCaseType())) {
            wholeCaseBase.setCaseType(reportInfoDto.getCaseType());
        }
        if (ReportConstant.REPORT_MODE_PTS.equals(reportInfoDto.getReportMode())) {
            wholeCaseBase.setCaseType("05");
        }
        wholeCaseBaseList.add(wholeCaseBase);
        ahcsDomainDTO.setWholeCaseBase(wholeCaseBaseList);

        ahcsDomainDTO.setReportAccidentBaggage(reportInfoDto.getReportAccidentBaggage());
        ahcsDomainDTO.setReportAccidentExam(reportInfoDto.getReportAccidentExam());
        ahcsDomainDTO.setReportAccidentFlight(reportInfoDto.getReportAccidentFlight());
        ahcsDomainDTO.setReportAccidentLoss(reportInfoDto.getReportAccidentLoss());
        ahcsDomainDTO.setReportAccidentOther(reportInfoDto.getReportAccidentOther());
        ahcsDomainDTO.setReportAccidentTraffic(reportInfoDto.getReportAccidentTraffic());
        ahcsDomainDTO.setReportAccidentTravel(reportInfoDto.getReportAccidentTravel());
        ahcsDomainDTO.setReportAccidentPet(reportInfoDto.getReportAccidentPet());
        ahcsDomainDTO.setLinkMans(reportInfoDto.getLinkManList());
        return ahcsDomainDTO;
    }

    private void checkTargetType(List<String> policyNos) {
        riskPropertyService.checkTargetType(policyNos);
    }

    /**
     * 因为查询保单时候已经剔除了其它被保人的信息，所以这里只要获取其中一条就可以了
     * @param ahcsPolicyDomainDTO
     * @param reportInfoDto
     * @return
     */
    private ReportCustomerInfoEntity matchCustomer(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, ReportInfoDTO reportInfoDto){
        ReportCustomerInfoEntity customer = new ReportCustomerInfoEntity();
        LogUtil.info("matchCustomer-客户名称-={}", reportInfoDto.getClientName());
        List<AhcsInsuredPresonDTO> ahcsInsuredPresonDTOs = ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs();
        AhcsInsuredPresonDTO ahcsInsuredPresonDTO = ahcsInsuredPresonDTOs.get(CommonConstant.ZERO);
        if (ahcsInsuredPresonDTO!=null && ahcsInsuredPresonDTO.getAhcsInsuredPreson() !=null){
            BeanUtils.copyProperties(ahcsInsuredPresonDTO.getAhcsInsuredPreson(),customer);
            customer.setCertificateType(reportInfoDto.getCertificateType());
            customer.setName(reportInfoDto.getClientName());
            customer.setClientCluster(ahcsInsuredPresonDTO.getAhcsInsuredPreson().getPersonnelAttribute());
        }
        return customer;
    }

    //发送报案短信
    void sendReportSms(String reportNo,SaveReportInfoVO reportInfoVO){

        try {
            if(ListUtils.isEmptyList(reportInfoVO.getLinkManList())){
                return;
            }
            Set<String> phoneNumbers = new HashSet<>();
            for (LinkManEntity man : reportInfoVO.getLinkManList()){
                String phoneNo = man.getLinkManTelephone();
                if(!ConstValues.YES.equals(man.getSendMessage()) || phoneNo == null || phoneNo.length() != 11){
                    continue;
                }
                if(phoneNumbers.add(phoneNo)){
                    //手机号去重发送
                    String txt = String.format(Constants.REPORT_SMS_TEMPLATE,reportNo);
                    smsInfoService.sendSmsByAsync(new SmsInfoDTO(reportNo,phoneNo,txt,Constants.SMS_LINK_REPORT));
                }
            }
        }catch (Exception e){

        }

    }

    @ApiOperation("查询报案联系人列表")
    @GetMapping(value = "/getLinkManByReportNo")
    public ResponseResult<Object> getLinkManByReportNo(@RequestParam("reportNo")String reportNo,@RequestParam("caseTimes")Integer  caseTimes){
        List<LinkManVO> linkManVOList = new ArrayList<>();
        List<LinkManEntity> linkManEntities = reportService.getLinkMans(reportNo,caseTimes);
        if (linkManEntities != null && !linkManEntities.isEmpty()){
            linkManVOList = linkManEntities.stream().map(LinkManEntity::convertToVo).collect(Collectors.toList());
        }
        return ResponseResult.success(linkManVOList);

    }

    /**
     * 实名化被保人
     * @param customerInfoVO
     * @return
     */
    @PostMapping(value = "/updateCustomerById")
    public ResponseResult updateCustomerById(@RequestBody ReportCustomerInfoVO customerInfoVO) throws InterruptedException {
        if(StringUtils.isEmptyStr(customerInfoVO.getIdAhcsReportCustomer()) || StringUtils.isEmptyStr(customerInfoVO.getIdPlyRiskPerson())){
            return ResponseResult.fail(GlobalResultStatus.FAIL);
        }

        reportCustomerInfoService.updateCustomerById(customerInfoVO);

        return ResponseResult.success();

    }

    /**
     * 获取事故日期报案统计
     */
    @PostMapping(value = "/getAccidentDateReportStat")
    public ResponseResult<ReportStatDTO> getAccidentDateReportStat(@RequestBody ReportQueryVO queryVO){
        return ResponseResult.success(reportService.getAccidentDateReportStat(queryVO.buildGetAccidentDateReportStatQueryParam()));
    }

    /**
     * 从抄单表获取险种，责任，责任明细信息
     */
    @GetMapping("/getReportPlanDuty")
    public ResponseResult<List<ReportPlanDutyVo>> getPolicyPlanDuty(@RequestParam("reportNo")String reportNo, @RequestParam("caseTimes")Integer caseTimes) {
        return ResponseResult.success(reportService.getReportPlanDuty(reportNo));
    }

}