package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleTemplateDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 定制化产品方案月赔付次数实现
 */
@Slf4j
@Service
@Order(4)
@RefreshScope
public class PolicyDayOrgCountLimitServiceImpl implements ExtendedLimitService {
    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;

    /**
     * 保单月限额配置方案
     */
    @Value("#{${policyLimit.dayOrgCount}}")
    private Map<String, Integer> policyDayOrgCountLimitMap;

    /**
     * 执行保单是否匹配的本扩展
     *
     * @param policyPayDTO
     * @return
     */
    @Override
    public boolean isMatch(PolicyPayDTO policyPayDTO) {
        return policyDayOrgCountLimitMap != null && policyDayOrgCountLimitMap.containsKey(policyPayDTO.getProductPackage());
    }

    @Override
    public void cumulativeLimit(PolicyPayDTO policyPayDTO) {
        if (!isMatch(policyPayDTO)) {
            return;
        }
        log.info("案件:{},配置了保单日机构赔付次数开始处理！", policyPayDTO.getReportNo());
        Integer limitCount = policyDayOrgCountLimitMap.get(policyPayDTO.getProductPackage());

        Map<Date, List<String>> limitMap = new HashMap<>();
        ClmsDutyDetailBillSettleDTO billSettleDTOQuery = new ClmsDutyDetailBillSettleDTO();
        billSettleDTOQuery.setPolicyNo(policyPayDTO.getPolicyNo());
        List<ClmsDutyDetailBillSettleDTO> clmsPayBillSettleList = clmsDutyDetailBillSettleMapper.getClmsPayBillSettleList(billSettleDTOQuery);
        clmsPayBillSettleList = clmsPayBillSettleList.stream().filter(i -> !i.getReportNo().equals(policyPayDTO.getReportNo()) && (null == i.getSettleAmount() || BigDecimal.ZERO.compareTo(i.getSettleAmount()) != 0)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(clmsPayBillSettleList)) {
            for (ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO : clmsPayBillSettleList) {
                Date billDate = clmsDutyDetailBillSettleDTO.getBillDate();
                List<String> hospitalList = limitMap.get(billDate);
                if (CollectionUtils.isEmpty(hospitalList)) {
                    hospitalList = new ArrayList<>();
                    hospitalList.add(clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getHospitalName());
                    limitMap.put(billDate, hospitalList);
                } else {
                    boolean matchResult = false;
                    for (int i = 0; i < hospitalList.size(); i++) {
                        String matchKey = hospitalList.get(i);
                        if (matchKey.contains(clmsDutyDetailBillSettleDTO.getHospitalCode()) || matchKey.contains(clmsDutyDetailBillSettleDTO.getHospitalName())) {
                            matchResult = true;
                            String newMatchKey = matchKey + clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getHospitalName();
                            hospitalList.set(i, newMatchKey);
                            break;
                        }
                    }
                    if (!matchResult) {
                        hospitalList.add(clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getHospitalName());
                        limitMap.put(billDate, hospitalList);
                    }
                }

            }
        }

        List<PlanPayDTO> plans = policyPayDTO.getPlanPayArr();
        for (PlanPayDTO planPayDTO : plans) {
            List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr();
            for (DutyPayDTO dutyPayDTO : dutyPayDTOS) {
                List<DutyDetailPayDTO> details = dutyPayDTO.getDutyDetailPayArr();
                for (DutyDetailPayDTO detailPayDTO : details) {
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getBillSettleResultDTOList())) {
//                        List<BIllSettleResultDTO> settleResultDTOList = detailPayDTO.getBillSettleResultDTOList().stream()
//                                .sorted(Comparator.comparing((BIllSettleResultDTO inv) ->
//                                        !"公立".equals(inv.getHospitalPropertyDes()) // 非公立排后面（返回 true 时会升序）
//                                )) // 保留原顺序或其他字段排序
//                                .collect(Collectors.toList());
//                        settleResultDTOList = settleResultDTOList.stream().sorted(Comparator.comparing(BIllSettleResultDTO::getAutoSettleAmount).reversed()).collect(Collectors.toList());
                        List<BIllSettleResultDTO> billSettleResultDTOList = detailPayDTO.getBillSettleResultDTOList();
                        billSettleResultDTOList.sort(new Comparator<BIllSettleResultDTO>() {
                            @Override
                            public int compare(BIllSettleResultDTO h1, BIllSettleResultDTO h2) {
                                // 第一优先级：按医院性质和名称分组（0为优先组，1为非优先组）
                                int priority1 = ("公立".equals(h1.getHospitalPropertyDes()) &&
                                        null != h1.getHospitalName() &&
                                        !h1.getHospitalName().contains("牙") &&
                                        !h1.getHospitalName().contains("口腔")) ? 0 : 1;
                                int priority2 = ("公立".equals(h2.getHospitalPropertyDes()) &&
                                        null != h2.getHospitalName() &&
                                        !h2.getHospitalName().contains("牙") &&
                                        !h2.getHospitalName().contains("口腔")) ? 0 : 1;

                                // 先比较优先级
                                if (priority1 != priority2) {
                                    return priority1 - priority2;
                                }

                                // 优先级相同后，按金额降序（使用BigDecimal的compareTo方法）
                                return h2.getAutoSettleAmount().compareTo(h1.getAutoSettleAmount());
                            }
                        });
                        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO = detailPayDTO.getDetailSettleReasonTemplateDTO();
                        BigDecimal sumAutoAmount = BigDecimal.ZERO;
                        List<EverySettleTemplateDTO> everySetttleList = detailSettleReasonTemplateDTO.getEverySetttleList();
                        Set<Date> everySettleSet = new HashSet<>();
                        Map<Date, BigDecimal> dayPayAmountMap = new HashMap<>();
                        for (BIllSettleResultDTO bIllSettleResultDTO : billSettleResultDTOList) {
                            if (!"0".equals(bIllSettleResultDTO.getSettleType())) {
                                continue;
                            }
                            List<String> hospitalList = limitMap.get(bIllSettleResultDTO.getBillDate());
                            if (CollectionUtils.isEmpty(hospitalList)) {
                                hospitalList = new ArrayList<>();
                                hospitalList.add(bIllSettleResultDTO.getHospitalCode() + bIllSettleResultDTO.getHospitalName());
                                limitMap.put(bIllSettleResultDTO.getBillDate(), hospitalList);
                                sumAutoAmount = sumAutoAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                BigDecimal dayPayAmount = dayPayAmountMap.get(bIllSettleResultDTO.getBillDate());
                                if (null == dayPayAmount) {
                                    dayPayAmount = BigDecimal.ZERO;
                                }
                                dayPayAmount = dayPayAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                dayPayAmountMap.put(bIllSettleResultDTO.getBillDate(), dayPayAmount);
                            } else {
                                boolean matchResult = false;
                                for (int i = 0; i < hospitalList.size(); i++) {
                                    String matchKey = hospitalList.get(i);
                                    if (matchKey.contains(bIllSettleResultDTO.getHospitalCode()) || matchKey.contains(bIllSettleResultDTO.getHospitalName())) {
                                        matchResult = true;
                                        String newMatchKey = matchKey + bIllSettleResultDTO.getHospitalCode() + bIllSettleResultDTO.getHospitalName();
                                        hospitalList.set(i, newMatchKey);
                                        break;
                                    }
                                }
                                if (!matchResult) {
                                    if (hospitalList.size() >= limitCount) {
                                        updateBillSettleResult(bIllSettleResultDTO, limitCount, everySettleSet);
                                    } else {
                                        hospitalList.add(bIllSettleResultDTO.getHospitalCode());
                                        limitMap.put(bIllSettleResultDTO.getBillDate(), hospitalList);
                                        sumAutoAmount = sumAutoAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                        BigDecimal dayPayAmount = dayPayAmountMap.get(bIllSettleResultDTO.getBillDate());
                                        if (null == dayPayAmount) {
                                            dayPayAmount = BigDecimal.ZERO;
                                        }
                                        dayPayAmount = dayPayAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                        dayPayAmountMap.put(bIllSettleResultDTO.getBillDate(), dayPayAmount);
                                    }
                                }else {
                                    sumAutoAmount = sumAutoAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                    BigDecimal dayPayAmount = dayPayAmountMap.get(bIllSettleResultDTO.getBillDate());
                                    if (null == dayPayAmount) {
                                        dayPayAmount = BigDecimal.ZERO;
                                    }
                                    dayPayAmount = dayPayAmount.add(bIllSettleResultDTO.getAutoSettleAmount());
                                    dayPayAmountMap.put(bIllSettleResultDTO.getBillDate(), dayPayAmount);
                                }
                            }
                        }
                        billSettleResultDTOList = billSettleResultDTOList.stream().sorted(Comparator.comparing(BIllSettleResultDTO::getBillDate)).collect(Collectors.toList());
                        detailPayDTO.setBillSettleResultDTOList(billSettleResultDTOList);
                        // 每个责任明细的总金额
                        detailPayDTO.setAutoSettleAmount(sumAutoAmount);
                        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(sumAutoAmount));
                        if (!CollectionUtils.isEmpty(everySettleSet)) {
                            setEverySetttleListValue(everySetttleList, everySettleSet, detailPayDTO.getBillSettleResultDTOList());
                        }
                        if (!CollectionUtils.isEmpty(dayPayAmountMap)) {
                            List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList = detailPayDTO.getDutyBillLimitInfoDTOList();
                            if (CollectionUtils.isEmpty(dutyBillLimitInfoDTOList)) {
                                log.warn("一日一院获取发票限额列表为空，reportNo:{},dutyCode:{}", policyPayDTO.getReportNo(), dutyPayDTO.getDutyCode());
                                continue;
                            }
                            Set<Date> dateSet = dayPayAmountMap.keySet();
                            for (Date date : dateSet) {
                                BigDecimal dayPayAmount = dayPayAmountMap.get(date);
                                List<DutyBillLimitInfoDTO> collect = dutyBillLimitInfoDTOList.stream().filter(i -> date.equals(i.getBillDate())).collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(collect)) {
                                    log.warn("一日一院过滤发票限额列表结果为空，reportNo:{},dutyCode:{}，billDate:{}", policyPayDTO.getReportNo(), dutyPayDTO.getDutyCode(), date);
                                    continue;
                                }
                                for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : collect) {
                                    dutyBillLimitInfoDTO.setSettleClaimAmount(dayPayAmount);
                                }
                            }

                            List<DutyBillLimitInfoDTO> collect = dutyBillLimitInfoDTOList.stream().filter(i -> !dateSet.contains(i.getBillDate())).collect(Collectors.toList());
                            if(!CollectionUtils.isEmpty(collect)){
                                for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : collect) {
                                    dutyBillLimitInfoDTO.setSettleClaimAmount(BigDecimal.ZERO);
                                }
                            }
                        }
                    }
                }
            }
        }

    }


    /**
     * 更新责任明细发票数据
     */
    private void updateBillSettleResult(BIllSettleResultDTO bIllSettleResultDTO, Integer limitCount, Set<Date> everySettleSet) {
        bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
//        bIllSettleResultDTO.setExceedMothPayDays("Y");
        bIllSettleResultDTO.setSettleType("5");
        bIllSettleResultDTO.setRemark("发票所在日期累计赔付次数已达日限次数" + limitCount + "次，本次可赔付金额为0");
        log.info("报案号={},发票所在日期累计赔付次数已达日限次数，limitCount：{}，billDate：{}，该发票责任明细发票理算数据为0", bIllSettleResultDTO.getReportNo(), limitCount, bIllSettleResultDTO.getBillDate());
        everySettleSet.add(bIllSettleResultDTO.getBillDate());
    }

    /**
     * 更新理算依据对象
     */
    private void setEverySetttleListValue(List<EverySettleTemplateDTO> everySetttleList, Set<Date> everySettleSet, List<BIllSettleResultDTO> billSettleResultDTOList) {
        if (CollectionUtil.isEmpty(everySetttleList)) {
            return;
        }
        for (Date date : everySettleSet) {
            String strbillDate = DateUtils.dateFormat(date, DateUtils.SIMPLE_DATE_STR);
            for (EverySettleTemplateDTO e : everySetttleList) {
                if (Objects.equals(e.getStrBillDate(), strbillDate)) {
                    e.setExceedDayOrgLimit("Y");
                    BigDecimal reduce = billSettleResultDTOList.stream().filter(i -> date.equals(i.getBillDate())).map(BIllSettleResultDTO::getAutoSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    e.setExceedDayOrgLimitAmount(BigDecimalUtils.toString(reduce));
                }
            }
        }

    }


}
