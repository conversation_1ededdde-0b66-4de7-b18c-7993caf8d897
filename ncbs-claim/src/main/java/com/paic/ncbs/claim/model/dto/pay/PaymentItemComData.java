package com.paic.ncbs.claim.model.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paic.ncbs.claim.model.convert.BeanConvert;
import com.paic.ncbs.claim.model.vo.ahcs.PaymentClientInfoFullVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties({"createdBy", "createdDate", "updatedDate", "updatedBy", "currentPage", "perPageSize",
        "pageNumEnd", "pageNumBegin", "userId"})
@ApiModel("支付项目")
@Data
public class PaymentItemComData {
    @ApiModelProperty("主键")
    private String idClmPaymentItem;
    @ApiModelProperty("clm_payment_item主键")
    private String idAhcsPaymentItem;
    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("电子保单号")
    private String policyCerNo;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔案号")
    private String caseNo;
    @ApiModelProperty("赔付批次号")
    private String idClmBatch;
    @ApiModelProperty("ahcs_batch批次号")
    private String idAhcsBatch;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("赔付类型(理算支付类型)（1-赔付 2-预陪 3-垫付 4-追偿 5-垫付转回 6-代位追偿 7-预付 8-残值 9-营改增  10-追偿预付  11-追偿预收 12-一赔预收 13-风险）")
    private String claimType;
    @ApiModelProperty("赔付类型名称")
    private String claimTypeName;
    @ApiModelProperty("赔款类型次数，预陪/垫付/追偿 次数")
    private Integer subTimes;
    @ApiModelProperty("赔款类型：(13-赔款 11-预赔 12-垫付 1H-追偿,1J直接理赔费用)对应表clm_common_parameter中PAYMENT_TYPE")
    private String paymentType;
    @ApiModelProperty("赔款类型名称")
    private String paymentTypeName;
    @ApiModelProperty("收付标识:(收款＝0，付款＝1)")
    private String collectPaySign;
    @ApiModelProperty("支付总金额")
    private BigDecimal paymentAmount;
    @ApiModelProperty("支付币种")
    private String paymentCurrencyCode;
    @ApiModelProperty("客户名称")
    private String clientName;
    @ApiModelProperty("客户证件类型：(01-身份证;02-护照;03-军人证;05-驾驶证;06-港澳回乡证或台胞证;99-其它;)对应表clm_common_parameter中ZJLX00")
    private String clientCertificateType;
    @ApiModelProperty("客户证件类型名称")
    private String clientCertificateTypeName;
    @ApiModelProperty("客户证件号码")
    private String clientCertificateNo;
    @ApiModelProperty("客户银行代码")
    private String clientBankCode;
    @ApiModelProperty("客户银行名称")
    private String clientBankName;
    @ApiModelProperty("客户帐号")
    private String clientBankAccount;
    @ApiModelProperty("客户手机")
    private String clientMobile;
    @ApiModelProperty("客户类型：(01=被保险人,02=受益人,03=受害人,04=法院,05=被委托人,99=其他等)，是否对应表clm_common_parameter中collection_code=KHLB00,")
    private String clientType;
    @ApiModelProperty("客户类型名称")
    private String clientTypeName;
    @ApiModelProperty("省份名称")
    private String provinceName;
    @ApiModelProperty("城市名称")
    private String cityName;
    @ApiModelProperty("地区码")
    private String regionCode;
    @ApiModelProperty("支付方式：(01-公司柜面;02-实时支付03-批量转账,对应表clm_common_parameter中SFTJ00)")
    private String collectPayApproach;
    @ApiModelProperty("支付方式名称")
    private String collectPayApproachName;
    @ApiModelProperty("帐号类型:个人帐号=1,公司帐号=0")
    private String bankAccountAttribute;
    @ApiModelProperty("案件合并标识：(01=不合并,02=待合并,03=已合并)")
    private String mergeSign;
    @ApiModelProperty("是否合并支付(Y-是，N-否)")
    private String isMergePay;
    @ApiModelProperty("案件合并标识名称")
    private String mergeSignName;
    @ApiModelProperty("是否合并支付(Y-是，N-否)")
    private String isMergePayName;
    @ApiModelProperty("支付项目状态：(20=暂存,24=审批通过,28=审批退回,30＝待开单,40=已开单,50=退回待修改,60=停止支付,61=暂停支付,90=作废)")
    private String paymentItemStatus;
    @ApiModelProperty("支付项目状态名称")
    private String paymentItemStatusName;
    @ApiModelProperty("有效终止时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date invalidateDate;
    @ApiModelProperty("备注/附言")
    private String remark;
    @ApiModelProperty("是否共保摊回：Y=是，N=否")
    private String isCoinsure;
    @ApiModelProperty("数据来源，区分理赔各子领域以及其新老数据，默认n代表新车险理赔数据，c代码原车险理赔数据，np代表新财产险理赔数据，op代表原财产险理赔数据，a代表新农险数据")
    private String migrateFrom;
    @ApiModelProperty("关联支付信息表主键")
    private String idClmPaymentInfo;
    @ApiModelProperty("历史通知单主键(产生于:支付手工撤回、财务网银退票)")
    private String hisIdClmPaymentNotice;
    @ApiModelProperty("收付编号")
    private String collectPayNo;
    @ApiModelProperty("代位追偿理算表主键")
    private String idClmSubrogationSettle;
    @ApiModelProperty("转换后金额")
    private BigDecimal convertAmount;
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
    @ApiModelProperty("支付信息类型(00-整案，01-先结，02-垫付，03-预付，04-减损费用(调查),05-垫付退款,06-追偿费用支出,07-追偿收入（赔款、费用收入）,08-保单先结,P1-赔款,P2-费用)")
    private String paymentInfoType;
    private String paymentInfoTypeName;
    @ApiModelProperty("机构编码")
    private String organizeCode;
    @ApiModelProperty("支付信息状态（是否有效）0=有效  1＝作废  2=暂存")
    private String paymentInfoStatus;
    @ApiModelProperty("一般纳税人标识:Y=是;N=否")
    private String isTaxpayer;
    @ApiModelProperty("支付客户信息列表")
    private List<PaymentClientInfoFullVO> paymentClientInfoFullVO;
    @ApiModelProperty("来源id")
    private String sourceId;
    @ApiModelProperty("修改人员,UM编码")
    private String updatedBy;
    @ApiModelProperty("是否加密")
    private String isEncrypt;
    @ApiModelProperty("是否预付")
    private String isPrepaid;
    @ApiModelProperty("该保单为体外共保且我司次承保")
    private String isExternalCoinsure;
    @ApiModelProperty("支付时间")
    private String payDate;
    @ApiModelProperty("标的共享保额,1：是，0：否")
    private String shareInsuredAmount;
    @ApiModelProperty("反对支付原因")
    private String counterPaymentReason;
    @ApiModelProperty("反对支付原因名称")
    private String counterPaymentReasonName;
    @ApiModelProperty("银行类型编码")
    private String bankTypeCode;
    @ApiModelProperty("银行类型名称")
    private String bankTypeName;
    @ApiModelProperty("公司id")
    private String companyId;
    @ApiModelProperty("支付用途")
    private String paymentUsage;
    @ApiModelProperty("损失对象代码")
    private String lossObjectNo;
    @ApiModelProperty("支付金额")
    private String paymentAmountStr;
    @ApiModelProperty("")
    private String reportAccept;
    @ApiModelProperty("是否跳过校验")
    private Boolean isChecked;
    @ApiModelProperty("支付用途")
    private String paymentUsageName;
    @ApiModelProperty("银行账户属性名称(公司/个人)")
    private String bankAccountAttributeName;

    @ApiModelProperty("开户行明细")
    private String bankDetail ;
    @ApiModelProperty("市code")
    private String cityCode ;

    // 计算书号
    @ApiModelProperty("计算书号")
    private String compensateNo ;
    //序号
    @ApiModelProperty("序号")
    private Integer serialNo ;
    //费用类型
    @ApiModelProperty("费用类型")
    private String feeType;

    /**
     * 机构类型
     */
    @ApiModelProperty("机构类型：AgencyTypeEnum")
    private String agencyType;
    /**
     * 客户号
     */
    @ApiModelProperty("客户号")
    private String customerNo;

    @ApiModelProperty("公司证件类型/企业证件类型：CertificateTypeEnum枚举类，统一社会信用代码-610099，组织机构代码-610001，税务登记证-610007，营业执照-610005，其他证件-619999")
    private String companyCardType;

    @ApiModelProperty("共保标志 0非共保1共保")
    private String coinsuranceMark;

    @ApiModelProperty("是否主承保 0否1是")
    private String acceptInsuranceFlag;

    @ApiModelProperty("共保公司编码")
    private String coinsuranceCompanyCode;

    @ApiModelProperty("共保公司名称")
    private String coinsuranceCompanyName;

    @ApiModelProperty("共保比例")
    private BigDecimal coinsuranceRatio;

    @ApiModelProperty("是否全额给付 0否1是")
    private String isFullPay;

    @ApiModelProperty("共保实付金额")
    private BigDecimal coinsuranceActualAmount;

    public PaymentItemDTO convertToDTO(){
        PaymentItemConvert convert = new PaymentItemConvert();
        return convert.convert(this);
    }

    private static class PaymentItemConvert implements BeanConvert<PaymentItemComData,PaymentItemDTO>{

        @Override
        public PaymentItemDTO convert(PaymentItemComData paymentItemComData) {
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            BeanUtils.copyProperties(paymentItemComData,paymentItemDTO);
            return paymentItemDTO;
        }
    }

}
