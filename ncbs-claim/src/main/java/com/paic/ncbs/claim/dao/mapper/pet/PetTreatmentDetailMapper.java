package com.paic.ncbs.claim.dao.mapper.pet;

import com.paic.ncbs.claim.model.dto.pet.PetTreatmentDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PetTreatmentDetailMapper {

    void addPetTreatmentDetailList(@Param("treatmentDetailList") List<PetTreatmentDetailDTO> treatmentDetailList);

    List<PetTreatmentDetailDTO> getPetTreatmentDetailList(@Param("idClmsPetInjure")String idClmPetInjure);

    void delPetTreatmentDetailList(@Param("idClmsPetInjure")String idClmsPetInjure,@Param("updatedBy")String updatedBy);

}