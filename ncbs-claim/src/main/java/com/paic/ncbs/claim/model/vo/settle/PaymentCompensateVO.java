package com.paic.ncbs.claim.model.vo.settle;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.vo.checkloss.LossReduceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("送收付补推信息")
@Data
public class PaymentCompensateVO extends EntityDTO {

    private static final long serialVersionUID = -1447676314012723972L;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("二次支付的时候传过来的唯一标识")
    private String paySerialNo;
    @ApiModelProperty("第一次核赔提交送收付的时候需要考虑案件重开的特殊场景 是否是核赔处提交")
    private boolean isVerifyFirstPay;
    @ApiModelProperty("是否预赔")
    private boolean isPrePay;
}
