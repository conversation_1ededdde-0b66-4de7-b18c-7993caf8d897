package com.paic.ncbs.claim.service.duty.impl;

import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeDetailMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDisabilityMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyAttributeDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import com.paic.ncbs.claim.service.duty.DutyAttrAsyncInitService;
import com.paic.ncbs.claim.service.settle.ClmsSettleBillRuleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@RefreshScope
@Service("dutyAttrAsyncInitService")
public class DutyAttrAsyncInitServiceImpl implements DutyAttrAsyncInitService {

    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;
    @Autowired
    private DutyAttributeDetailMapper dutyAttributeDetailMapper;
    @Autowired
    private PersonDisabilityMapper personDisabilityMapper;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Autowired
    private ClmsSettleBillRuleService clmsSettleBillRuleService;
    @Value("${project.projectCode:N}")
    private String configProjectCode;
    /**
     * 生产环境和dev产品，方案编码都不一样
     * dev C00094,生产是C00034
     */
    @Value("${planCode.outpatientCode:C00094}")
    private String configDutyCode;
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;

    @Value("${product.autoProductCode}")
    private List<String> autoProductCodeList;

    @Override
    public void setDetailPayAttr(PolicyPayDTO policy, CountDownLatch latch) throws GlobalBusinessException {
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        String policyNo = policy.getPolicyNo();
        try {
            LogUtil.audit("--setDetailPayAttr从抄单表中查询责任属性,报案号={},赔付次数={},保单号={}，configProjectCodeList={}", reportNo, caseTimes, policyNo);
            List<PlanPayDTO> planPayList = policy.getPlanPayArr();
            planPayList.forEach(plan -> {
                List<DutyPayDTO> dutyPayList = plan.getDutyPayArr();
                dutyPayList.forEach(duty -> getAttrByDutyDetailType(duty, reportNo, caseTimes));
            });
        } finally {
            latch.countDown();
        }
    }

    @Override
    public void getAttrByDutyDetailType(DutyPayDTO duty, String reportNo, int caseTimes) {
        List<DutyDetailPayDTO> detailPayList = duty.getDutyDetailPayArr();
        String idPolicyDuty = duty.getIdCopyDuty();
        detailPayList.forEach(detail -> {
            String dutyDetailType = Optional.ofNullable(detail.getDutyDetailType()).orElse("");
            switch (dutyDetailType) {
                //伤残类型:伤残比例
                case SettleConst.DETAIL_TYPE_DISABILITY:
                    getDisabilityAttr(detail, reportNo, caseTimes, idPolicyDuty);
                    break;
                //医疗类型:免赔额、赔付比例
                case SettleConst.DETAIL_TYPE_MEDICAL:
                    //getMedicalAttr(detail,reportNo,caseTimes,idPolicyDuty);//老逻辑
                    getMedicalAttrNew(detail, reportNo, caseTimes, idPolicyDuty);//2024-2-5好版本新逻辑
                    break;
                //津贴类型:免赔天数、津贴日额
                case SettleConst.DETAIL_TYPE_ALLOWANCE:
                    getAllowanceAttr(detail, reportNo, caseTimes, idPolicyDuty);
                    break;
                default:
                    break;
            }
        });

    }

    /**
     * 获取残疾类型责任属性
     */
    @Override
    public void getDisabilityAttr(DutyDetailPayDTO detail, String reportNo, int caseTimes, String idPolicyDuty) {
        LogUtil.audit("---获取残疾类型责任属性---");
        //伤残信息
        PersonDisabilityDTO personDisability = new PersonDisabilityDTO();
        personDisability.setReportNo(reportNo);
        personDisability.setCaseTimes(caseTimes);
        personDisability.setTaskId(BpmConstants.CHECK_DUTY);
        List<PersonDisabilityDTO> disableList = personDisabilityMapper.getList(personDisability);
        Map<String, Map<String, String>> attributes = dutyAttributeMapper.getAttributeByAttrCode(idPolicyDuty,
                Arrays.asList(DutyAttributeConst.DISABILITY_RATE));
        if (!attributes.isEmpty()) {
            String disabilityGrade = null;
            if (!disableList.isEmpty() && SettleConst.DETAIL_TYPE_DISABILITY.equals(detail.getDutyDetailType())) {
                disabilityGrade = disableList
                        .stream().filter(Objects::nonNull)
                        .min(Comparator.comparingInt(d -> Integer.parseInt(d.getDisabilityGrade()))).get().getDisabilityGrade();
            }
            //根据收单录入的【伤残等级】取产品工厂中对应的【残疾对应赔付比例】
            List<AhcsDutyAttributeDetailEntity> attributeDetailList = dutyAttributeDetailMapper.getInfoByDutyAttributeId(attributes.get(DutyAttributeConst.DISABILITY_RATE)
                    .get(DutyAttributeConst.ID_AHCS_DUTY_ATTRIBUTE));
            if (attributeDetailList.isEmpty()) {
                detail.setDisabilityRate(BigDecimal.ONE);
            } else {
                String grade = DutyAttributeConst.DISABILITY_GRADE_RATE_MAP.get(disabilityGrade);
                LogUtil.audit("---伤残等级：{}，属性code：{}", disabilityGrade, grade);
                attributeDetailList.forEach(a -> {
                    if (a.getAttributeDetailCode().equals(grade)) {
                        LogUtil.audit("---伤残比例:{}", a.getAttributeDetailValue());
                        //百分比转小数
                        detail.setDisabilityRate(nvl(BigDecimalUtils.toPercent(new BigDecimal(a.getAttributeDetailValue())), 0));
                    }
                });
            }
        } else {
            LogUtil.audit("伤残类型责任属性为空,案件reportNo={},caseTimes={},保单号={}", reportNo, caseTimes, detail.getPolicyNo());
        }
    }

    /**
     * 获取医疗类型责任属性
     */
    @Override
    public void getMedicalAttr(DutyDetailPayDTO detail, String reportNo, int caseTimes, String idPolicyDuty) {
        LogUtil.audit("---获取医疗类型责任属性---");
        //发票总额
        MedicalBillInfoDTO medicalBillInfoDTO = new MedicalBillInfoDTO();
        medicalBillInfoDTO.setReportNo(reportNo);
        medicalBillInfoDTO.setCaseTimes(caseTimes);
        MedicalBillInfoPageVO medicalBillInfoPage = Optional.ofNullable(medicalBillInfoMapper.getMedicalBillAllSumAmount(medicalBillInfoDTO))
                .orElse(new MedicalBillInfoPageVO());
        BigDecimal reasonableAmount = medicalBillInfoPage.getReasonableAmountSum();
        String therapyType = null;
        List<String> therapyTypeList = medicalBillInfoMapper.getBillClassByReportNo(reportNo, caseTimes);
        if (therapyTypeList.size() > 1) {
            therapyType = BaseConstant.STRING_3;
        } else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_ONE.equals(therapyTypeList.get(0))) {
            therapyType = BaseConstant.STRING_2;
        } else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_TWO.equals(therapyTypeList.get(0))) {
            therapyType = BaseConstant.STRING_1;
        }
        String therapyTypeFinal = therapyType;
        detail.setPayProportion(BigDecimal.ONE);
        Map<String, Map<String, String>> attributes = dutyAttributeMapper.getAttributeByAttrCode(idPolicyDuty,
                Arrays.asList(DutyAttributeConst.REMIT_AMOUNT, DutyAttributeConst.REMIT_AMOUNT_TYPE, DutyAttributeConst.PAY_PROPORTION, DutyAttributeConst.PAY_PROPORTION_NONSTANDARD, DutyAttributeConst.PAY_LIMIT_6, DutyAttributeConst.PAY_LIMIT_TYPE));
        if (!attributes.isEmpty()) {
            detail.setReasonableAmount(reasonableAmount);
            detail.setReasonableAmountType(therapyTypeFinal);
            if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT)) {
                detail.setRemitAmount(nvl(new BigDecimal(attributes.get(DutyAttributeConst.REMIT_AMOUNT)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE)), 0));
                //免赔额类型
                if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT_TYPE)) {
                    //免赔额类型
                    detail.setRemitAmountType(attributes.get(DutyAttributeConst.REMIT_AMOUNT_TYPE).get(DutyAttributeConst.ATTRIBUTE_VALUE));
                } else {
                    LogUtil.info("责任:" + detail.getDutyCode() + "配置了免赔额属性但没有配置免赔额类型,默认为按次");
                    detail.setRemitAmountType("0");
                }

            }
            //赔偿限额2024-01-31 start
            if (attributes.containsKey(DutyAttributeConst.PAY_LIMIT_TYPE)) {
                detail.setPayLimitType(attributes.get(DutyAttributeConst.PAY_LIMIT_TYPE).get(DutyAttributeConst.ATTRIBUTE_VALUE));
                if (attributes.containsKey(DutyAttributeConst.PAY_LIMIT_6)) {
                    String payLimit = attributes.get(DutyAttributeConst.PAY_LIMIT_6).get(DutyAttributeConst.ATTRIBUTE_VALUE);
                    detail.setPayLimit(BigDecimalUtils.getBigDecimal(payLimit));
                } else {
                    LogUtil.info("责任" + detail.getDutyCode() + "配置了赔付限额类型属性，但没有配置限额，默认不处理限额");
                }
            } else {
                LogUtil.info("责任" + detail.getDutyCode() + "没配置赔付限额类型，就不存在处理限额");
            }

            //赔偿限额2024-01-31 end

            //若为固定赔付比例，则直接取
            if (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION)) {
                detail.setPayProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributes.get(DutyAttributeConst.PAY_PROPORTION)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE))), 1));
            } else if (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD)) {
                //若赔付比例为“非标准级距”，根据（合理费用-免赔额）的金额，根据级距区间进行匹配
                handleNSInterval(detail, reasonableAmount, attributes);
            }
        } else {
            LogUtil.audit("医疗类型责任属性为空,案件reportNo={},caseTimes={},保单号={}", reportNo, caseTimes, detail.getPolicyNo());
        }
    }

    /**
     * @Description 抽方法 处理非标准级距的计算
     * <AUTHOR>
     * @Date 2023/5/23 14:14
     **/
    private void handleNSInterval(DutyDetailPayDTO detail, BigDecimal reasonableAmount, Map<String, Map<String, String>> attributes) {
        List<AhcsDutyAttributeDetailEntity> attributeDetailList = dutyAttributeDetailMapper.getInfoByDutyAttributeId(attributes.get(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD)
                .get(DutyAttributeConst.ID_AHCS_DUTY_ATTRIBUTE));
        if (!attributeDetailList.isEmpty()) {
            detail.setPayProportionType("2");
            detail.setPayProportion(BigDecimalUtils.NEGATIVE_ONE);
            BigDecimal result = BigDecimal.ZERO;
            StringBuilder settleReason = new StringBuilder();
            // 合理费用
            BigDecimal amount = nvl(reasonableAmount, 0);
            // 免赔额
            BigDecimal remitAmount = nvl(detail.getRemitAmount(), 0);
            // 实际赔付金额
            BigDecimal actualAmount = amount.subtract(remitAmount);
            // 如果合理费用小于免赔 则不用计算
            if (actualAmount.compareTo(BigDecimal.ZERO) <= 0) {
                detail.setTempSettleAmount(result);
                detail.setDetailSettleReason(settleReason.append("不足免赔额"));
                return;
            }

            Map<String, List<AhcsDutyAttributeDetailEntity>> a1 = attributeDetailList.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeColumnNo))
                    .collect(Collectors.groupingBy(AhcsDutyAttributeDetailEntity::getAttributeColumnNo, LinkedHashMap::new, Collectors.toList()));
            Set<String> interval = new HashSet();
            a1.forEach((k, v) -> {
                v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                if (actualAmount.compareTo(left) > 0 && actualAmount.compareTo(right) <= 0) {
                    interval.add(k);
                }
            });
            Iterator iterator = a1.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                String k = (String) entry.getKey();
                List<AhcsDutyAttributeDetailEntity> v = (List<AhcsDutyAttributeDetailEntity>) entry.getValue();
                v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                BigDecimal percent = new BigDecimal(v.get(2).getAttributeDetailValue());
                BigDecimal rate = BigDecimalUtils.toPercent(percent);
                if (interval.contains(k)) {
                    //判断 actualAmount
                    result = result.add(BigDecimalUtils.getPositive((actualAmount.subtract(left)).multiply(rate)));
                    setNoStardardSettleValue(settleReason, amount, remitAmount, left, percent);
                    //settleReason.append("(").append(amount).append("-").append(remitAmount).append("-").append(left).append(")").append("×").append(percent).append("%");

                    break;
                } else {
                    result = result.add((right.subtract(left)).multiply(rate));
                    setValue(settleReason, right, left, percent);
                    //settleReason.append("(").append(right).append("-").append(left).append(")").append("×").append(percent).append("% +");
                }

            }
            detail.setTempSettleAmount(result);
            detail.setDetailSettleReason(settleReason.append("=").append(result.setScale(2, BigDecimal.ROUND_HALF_UP)));
        }
    }

    private void setValue(StringBuilder settleReason, BigDecimal right, BigDecimal left, BigDecimal percent) {
        if (left.compareTo(BigDecimal.ZERO) == 0) {
            settleReason.append(right).append("×").append(percent).append("% +");

        } else {
            settleReason.append("(").append(right).append("-").append(left).append(")").append("×").append(percent).append("% +");
        }

    }

    private void setNoStardardSettleValue(StringBuilder settleReason, BigDecimal amount, BigDecimal remitAmount, BigDecimal left, BigDecimal percent) {
        if (remitAmount.compareTo(BigDecimal.ZERO) == 0 && left.compareTo(BigDecimal.ZERO) == 0) {
            settleReason.append(amount).append("×").append(percent).append("%");
        } else if (remitAmount.compareTo(BigDecimal.ZERO) == 0 && left.compareTo(BigDecimal.ZERO) != 0) {
            settleReason.append("(").append(amount).append("-").append(left).append(")").append("×").append(percent).append("%");
        } else if (remitAmount.compareTo(BigDecimal.ZERO) != 0 && left.compareTo(BigDecimal.ZERO) == 0) {
            settleReason.append("(").append(amount).append("-").append(remitAmount).append(")").append("×").append(percent).append("%");
        } else {
            settleReason.append("(").append(amount).append("-").append(remitAmount).append("-").append(left).append(")").append("×").append(percent).append("%");
        }

    }

    /**
     * 获取津贴类型责任属性
     */
    @Override
    public void getAllowanceAttr(DutyDetailPayDTO detail, String reportNo, int caseTimes, String idPolicyDuty) {
        LogUtil.audit("---获取津贴类型责任属性---");
        Map<String, Map<String, String>> attributes = dutyAttributeMapper.getAttributeByAttrCode(idPolicyDuty,
                Arrays.asList(DutyAttributeConst.REMIT_DAYS, DutyAttributeConst.ALLOWANCE_AMOUNT));
        if (!attributes.isEmpty()) {
            if (attributes.containsKey(DutyAttributeConst.REMIT_DAYS)) {
                detail.setRemitDays(nvl(new BigDecimal(attributes.get(DutyAttributeConst.REMIT_DAYS)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE)), 0));
            }
            if (attributes.containsKey(DutyAttributeConst.ALLOWANCE_AMOUNT)) {
                detail.setAllowanceAmount(nvl(new BigDecimal(attributes.get(DutyAttributeConst.ALLOWANCE_AMOUNT)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE)), 0));
            }
        } else {
            LogUtil.audit("津贴类型责任属性为空,案件reportNo={},caseTimes={},保单号={}", reportNo, caseTimes, detail.getPolicyNo());
        }
    }

    /**
     * 获取医疗类型责任属性并计算非标准
     */
    @Override
    public void getMedicalAttrAndCalculate(DutyDetailPayDTO detail, String reportNo, int caseTimes, String idPolicyDuty) {
        Map<String, Map<String, String>> attributes = dutyAttributeMapper.getAttributeByAttrCode(idPolicyDuty,
                Arrays.asList(DutyAttributeConst.REMIT_AMOUNT, DutyAttributeConst.PAY_PROPORTION, DutyAttributeConst.PAY_PROPORTION_NONSTANDARD));
        if (!attributes.isEmpty()) {
            handleNSInterval(detail, detail.getReasonableAmount(), attributes);
        }
    }

    /**
     * 查询所有其他属性
     */
    @Override
    public List<com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO> getOtherDutyAttributeList(String idAhcsPolicyDuty) {
        List<com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO> attributes = dutyAttributeMapper.getAllAttributes(idAhcsPolicyDuty);
        attributes = attributes.stream().filter(a -> !DutyAttributeConst.NECESSARY_ATTR_LIST.contains(a.getAttrCode())).collect(Collectors.toList());
        return attributes;
    }


    /**
     * 获取医疗类型责任属性
     */

    public void getMedicalAttrNew(DutyDetailPayDTO detail, String reportNo, int caseTimes, String idPolicyDuty) {
        List<DutyAttributeDTO> dutySocAttributeDTOList = new ArrayList<>();
        List<String> therapyTypeList = medicalBillInfoMapper.getBillClassByReportNo(reportNo, caseTimes);
        String therapyType = null;
        if (therapyTypeList.size() > 1) {
            therapyType = BaseConstant.STRING_3;
        } else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_ONE.equals(therapyTypeList.get(0))) {
            therapyType = BaseConstant.STRING_2;
        } else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_TWO.equals(therapyTypeList.get(0))) {
            therapyType = BaseConstant.STRING_1;
        }
        String therapyTypeFinal = therapyType;


        LogUtil.audit("---获取账单类型---{}", JsonUtils.toJsonString(therapyTypeList));
        //账单日期合理费用  按日期,就诊类型(门诊，住院)，发票类型分组，
        List<MedicalBillInfoVO> oldBillInfoVOLists = medicalBillInfoMapper.getBillInfoGroupDateList(reportNo, caseTimes);
        Map<String, Map<String, String>> attributes = dutyAttributeMapper.getAttributeByAttrCode(idPolicyDuty,
                Arrays.asList(DutyAttributeConst.IS_DISTINGUISH_SOCIA_360, DutyAttributeConst.REMIT_AMOUNT, DutyAttributeConst.REMIT_AMOUNT_TYPE, DutyAttributeConst.PAY_PROPORTION, DutyAttributeConst.PAY_PROPORTION_NONSTANDARD, DutyAttributeConst.PAY_LIMIT_6, DutyAttributeConst.PAY_LIMIT_TYPE, DutyAttributeConst.WAIT_DAYS,DutyAttributeConst.EVERY_DAY_PAY_DAYS,DutyAttributeConst.YEARLY_PAY_DAYS));
        LogUtil.info("获取医疗类型责任属性报案号={}，责任属性={},账单信息={}", reportNo, JsonUtils.toJsonString(attributes), JsonUtils.toJsonString(oldBillInfoVOLists));
        Map<String, Map<String, String>> attributesdetailMap = dutyAttributeMapper.getAttributeDetailByidPolicyDuty(idPolicyDuty);
        LogUtil.info("责任编码={}，属性明细={}", detail.getDutyCode(), JsonUtils.toJsonString(attributesdetailMap));
        //等待期
        detail.setWaitDays(0);//初始化等待期为0
        if (!attributes.isEmpty()) {
            if (attributes.containsKey(DutyAttributeConst.WAIT_DAYS)) {
                String configDays = attributes.get(DutyAttributeConst.WAIT_DAYS)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE);
                detail.setWaitDays(Integer.valueOf(configDays));
            }
            //每月赔付天数
            if (attributes.containsKey(DutyAttributeConst.EVERY_DAY_PAY_DAYS)) {
                String payDays= attributes.get(DutyAttributeConst.EVERY_DAY_PAY_DAYS)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE);
                if(StringUtils.isNotEmpty(payDays)){
                    detail.setConfigPayDays(Integer.valueOf(payDays));
                }

            }
            //年度赔付天数
            if (attributes.containsKey(DutyAttributeConst.YEARLY_PAY_DAYS)) {
                String yearPayDays= attributes.get(DutyAttributeConst.YEARLY_PAY_DAYS)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE);
                if(StringUtils.isNotEmpty(yearPayDays)){
                    detail.setConfiYearlyPayDays(Integer.valueOf(yearPayDays));
                }

            }

        }
        if (CollectionUtils.isEmpty(oldBillInfoVOLists)) {
            LogUtil.info("报案号未录入账单信息={}", reportNo);
            return;
        }

        //过滤符合计算的发票
        List<MedicalBillInfoVO> billInfoVOLists = clmsSettleBillRuleService.dealData(reportNo, caseTimes, detail, oldBillInfoVOLists, therapyTypeFinal);
        if (CollectionUtils.isEmpty(billInfoVOLists)) {
            LogUtil.info("录入的发票日期都不在保单有效期内或都是住院发票={}", reportNo);
            return;
        }
        //记录总的合理费用，如果是按及距去匹配赔付比例时用这个责任的所有合理费用去匹配级距比例
        BigDecimal sumReasonableAmount = BigDecimal.ZERO;
        //按发票日期分组
        Map<Date, List<MedicalBillInfoVO>> billDateMap = billInfoVOLists.stream().collect(Collectors.groupingBy(MedicalBillInfoVO::getStartDate));
        for (Map.Entry<Date, List<MedicalBillInfoVO>> entry : billDateMap.entrySet()) {
            List<MedicalBillInfoVO> billInfoVOList = entry.getValue();
            List<DutyAttributeDTO> dtoList = getDutyAttributeDTOList(billInfoVOList, detail, entry.getKey());
            if (!CollectionUtils.isEmpty(dtoList)) {
                for (DutyAttributeDTO dto : dtoList) {
                    sumReasonableAmount = sumReasonableAmount.add(dto.getReasonableAmount());
                }
                dutySocAttributeDTOList.addAll(dtoList);
            }

        }
        filterBillData(dutySocAttributeDTOList,detail);
        //年度赔付天数
        checkYearlyPayDays(dutySocAttributeDTOList,detail);
        detail.setDutyAttributeDTOList(dutySocAttributeDTOList);
        detail.setPayProportion(BigDecimal.ONE);
        detail.setReasonableAmountType(therapyTypeFinal);
        if (Objects.equals(detail.getBeInHospitalFlag(), BaseConstant.BEIN_HOSPITAL_FLAG)) {
            detail.setReasonableAmountType(BaseConstant.STRING_2);//只有门诊发票
        }
        if (!attributes.isEmpty()) {
            //360-是否区分社保：属性没有配置360肯定不需要区分社保，配置了360需要看对应的value值，值为0表示区分，否则就不区分（跟产品邹俊确认）
            if (attributes.containsKey(DutyAttributeConst.IS_DISTINGUISH_SOCIA_360)) {
                if (Objects.equals("0", attributes.get(DutyAttributeConst.IS_DISTINGUISH_SOCIA_360).get(DutyAttributeConst.ATTRIBUTE_VALUE))) {
                    detail.setIsDistinguishSocia("Y");
                    //如果是需要区分社保表示，取是否经医保结算属性
                    if (!attributesdetailMap.isEmpty()) {
                        LogUtil.info("责任" + detail.getDutyCode() + "需要区分社保属性");
                        setAttributeValue(attributesdetailMap, detail);
                    }

                } else {
                    LogUtil.info("对应的360属性值不为0 按不区分社保处理");
                    detail.setIsDistinguishSocia("N");
                    //不区分社保 但有可能区分是否经医保结算，但目前没有这种业务数据
                    if (!attributesdetailMap.isEmpty()) {
                        setAttributeValue(attributesdetailMap, detail);
                    }
                }
            } else {
                LogUtil.info("不会存在这种业务数据和业务leao会议确认过");
                detail.setIsDistinguishSocia("N");//是否需要区分社保 ：N-不区分：不区分社保的赔付比列取外层配置的
            }

            if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT)) {
                detail.setRemitAmount(nvl(new BigDecimal(attributes.get(DutyAttributeConst.REMIT_AMOUNT)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE)), 0));
                //免赔额类型
                if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT_TYPE)) {
                    //免赔额类型
                    detail.setRemitAmountType(attributes.get(DutyAttributeConst.REMIT_AMOUNT_TYPE).get(DutyAttributeConst.ATTRIBUTE_VALUE));
                } else {
                    LogUtil.info("责任:" + detail.getDutyCode() + "配置了免赔额属性但没有配置免赔额类型,默认为按次");
                    detail.setRemitAmountType("0");
                }

            }
            //赔偿限额2024-01-31 start
            if (attributes.containsKey(DutyAttributeConst.PAY_LIMIT_TYPE)) {
                detail.setPayLimitType(attributes.get(DutyAttributeConst.PAY_LIMIT_TYPE).get(DutyAttributeConst.ATTRIBUTE_VALUE));
                if (attributes.containsKey(DutyAttributeConst.PAY_LIMIT_6)) {
                    String payLimit = attributes.get(DutyAttributeConst.PAY_LIMIT_6).get(DutyAttributeConst.ATTRIBUTE_VALUE);
                    detail.setPayLimit(BigDecimalUtils.getBigDecimal(payLimit));
                } else {
                    LogUtil.info("责任" + detail.getDutyCode() + "配置了赔付限额类型属性，但没有配置限额，默认不处理限额");
                }
            } else {
                LogUtil.info("责任" + detail.getDutyCode() + "没配置赔付限额类型，就不存在处理限额");
            }

            //若为固定赔付比例，则直接取
            if (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION)) {
                detail.setPayProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributes.get(DutyAttributeConst.PAY_PROPORTION)
                        .get(DutyAttributeConst.ATTRIBUTE_VALUE))), 1));
            } else if (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD) || (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD_364) && Objects.equals("2", attributes.get(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD_364).get(DutyAttributeConst.ATTRIBUTE_VALUE)))) {
                //若赔付比例为“非标准级距”，根据（合理费用-免赔额）的金额，根据级距区间进行匹配
                //非级距的合理费用 应该用当前保单的历史报案录入的已结案的发票的合理费用+本次报案的合理费用
                //BigDecimal noStandReasonableAmount=sumReasonableAmount.add(detail.getPolicyBillHistoryAmount());
                detail.setReasonableAmount(sumReasonableAmount);//
                //handleNSIntervalNew(detail, sumReasonableAmount, attributes);//测试功能代码
                handleNSInterval(detail, sumReasonableAmount, attributes);
            } else if (!attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD_364)) {
                //没有配置赔付比例类型那就默认按固定比例
                if(!attributes.containsKey(DutyAttributeConst.PAY_PROPORTION) && autoProductCodeList.contains(detail.getProductCode())){
                    throw new GlobalBusinessException("责任"+detail.getDutyCode()+"未配置固定赔付比例");
                }
                BigDecimal proportion = BigDecimal.valueOf(100L);
                Map<String, String> stringStringMap = attributes.get(DutyAttributeConst.PAY_PROPORTION);
                if(!CollectionUtils.isEmpty(stringStringMap) && Objects.nonNull(stringStringMap.get(DutyAttributeConst.ATTRIBUTE_VALUE))){
                    proportion = new BigDecimal(stringStringMap.get(DutyAttributeConst.ATTRIBUTE_VALUE));
                }
                detail.setPayProportion(BigDecimalUtils.toPercent(proportion));
            }

        } else {
            LogUtil.audit("医疗类型责任属性为空,案件reportNo={},caseTimes={},保单号={}", reportNo, caseTimes, detail.getPolicyNo());
        }

    }


    /**
     * 组装属性
     *
     * @param attributesdetailMap
     * @param detail
     */
    private void setAttributeValue(Map<String, Map<String, String>> attributesdetailMap, DutyDetailPayDTO detail) {
        //经医保结算赔付比例
        if (attributesdetailMap.containsKey(DutyAttributeConst.IS_SOCIA_361_4000)) {
            //有社保经医保结算赔付比例
            detail.setIsSocMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.IS_SOCIA_361_4000)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.IS_SOCIA_361_4001)) {
            //有社保未经医保结算的赔付比列
            detail.setIsSocNoMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.IS_SOCIA_361_4001)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.IS_SOCIA_361_4002)) {
            //有社保没有经医保结算的赔付罚则
            detail.setIsSocPenaltyProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.IS_SOCIA_361_4002)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        //无社保的经医保结算的比列
        if (attributesdetailMap.containsKey(DutyAttributeConst.NO_SOCIA_362_4000)) {
            //无社保的 经医保结算的赔付比列
            detail.setNoSocMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.NO_SOCIA_362_4000)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.NO_SOCIA_362_4001)) {
            //无社保 未经医保结算的赔付比列
            detail.setNoSocNoMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.NO_SOCIA_362_4001)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.NO_SOCIA_362_4002)) {
            //无社保 未经医保结算的赔付罚则
            detail.setNoSocPenaltyProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.NO_SOCIA_362_4002)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.MEDICAL_361_1003)) {
            detail.setIsSocialMedicalSettle(attributesdetailMap.get(DutyAttributeConst.MEDICAL_361_1003)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE));
            LogUtil.info("责任" + detail.getDutyCode() + "不需要否区分是否经医保结算");
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.MEDICAL_362_1003)) {
            detail.setNoSocialMedicalSettle(attributesdetailMap.get(DutyAttributeConst.MEDICAL_362_1003)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE));
            LogUtil.info("责任" + detail.getDutyCode() + "不需要否区分是否经医保结算");
        }

        LogUtil.info("责任={},责任属性={}", detail.getDutyCode(), JsonUtils.toJsonString(detail));
    }

    /**
     * 计算每一天  经医保结算和非医保结算的合理费用
     * billInfoVOList：是一天的所有发票类型的数据
     *
     * @param billInfoVOList
     * @param detail
     * @param billDate
     */
    private List<DutyAttributeDTO> getDutyAttributeDTOList(List<MedicalBillInfoVO> billInfoVOList, DutyDetailPayDTO detail, Date billDate) {
        List<DutyAttributeDTO> dtoList = new ArrayList<>();
        //过滤出所有经医保计算的数据
        List<MedicalBillInfoVO> medicalBillInfoVOList = billInfoVOList.stream().filter(b -> Objects.equals("BT_3601", b.getBillType()) || Objects.equals("BT_3603", b.getBillType())).collect(Collectors.toList());
        //过滤所有非医保结算的发票数据
        List<MedicalBillInfoVO> noMedicalBillInfoVOList = billInfoVOList.stream().filter(b -> !Objects.equals("BT_3601", b.getBillType()) && !Objects.equals("BT_3603", b.getBillType())).collect(Collectors.toList());
        DutyAttributeDTO mediclDutyAttributeDTO = getDutyAttributeDTO(medicalBillInfoVOList, "Y", detail, billDate);
        if (Objects.nonNull(mediclDutyAttributeDTO)) {
            LogUtil.info("经医保结算的合理费用信息={}", JsonUtils.toJsonString(mediclDutyAttributeDTO));
            dtoList.add(mediclDutyAttributeDTO);
        }
        // //没有经医保结算的
        DutyAttributeDTO noMediclDutyAttributeDTO = getDutyAttributeDTO(noMedicalBillInfoVOList, "N", detail, billDate);
        if (Objects.nonNull(noMediclDutyAttributeDTO)) {
            LogUtil.info("没有经医保结算的合理费用信息={}", JsonUtils.toJsonString(noMediclDutyAttributeDTO));
            dtoList.add(noMediclDutyAttributeDTO);
        }
        return dtoList;
    }

    /**
     * 计算合理费用组装DutyAttributeDTO
     *
     * @param medicalBillInfoVOList
     * @param detail
     * @param billDate
     */
    private DutyAttributeDTO getDutyAttributeDTO(List<MedicalBillInfoVO> medicalBillInfoVOList, String mediclSettleFlag, DutyDetailPayDTO detail, Date billDate) {
        DutyAttributeDTO dutyAttributeDTO = null;
        if (!CollectionUtils.isEmpty(medicalBillInfoVOList)) {
            BigDecimal reasonableAmount; //合理费用
            BigDecimal sumBillAmount = BigDecimal.ZERO;//发票总金额
            BigDecimal sumPartialDeductible = BigDecimal.ZERO;//部分自费总金额
            BigDecimal sumDeductibleAmount = BigDecimal.ZERO;//自费总金额
            BigDecimal sumPrepaidAmount = BigDecimal.ZERO;//第三方支付总金额
            BigDecimal sumimmoderateAmount = BigDecimal.ZERO;//不合理总金额
            for (MedicalBillInfoVO medicalBillInfoVO : medicalBillInfoVOList) {
                sumBillAmount = sumBillAmount.add(medicalBillInfoVO.getBillAmount());
                sumPartialDeductible = sumPartialDeductible.add(medicalBillInfoVO.getPartialDeductible());
                sumDeductibleAmount = sumDeductibleAmount.add(medicalBillInfoVO.getDeductibleAmount());
                sumPrepaidAmount = sumPrepaidAmount.add(medicalBillInfoVO.getPrepaidAmount());
                sumimmoderateAmount = sumimmoderateAmount.add(medicalBillInfoVO.getImmoderateAmount());
            }
            List<String> configProjectCodeList = StringUtils.getListWithSeparator(configProjectCode, ",");
            LogUtil.info("configDutyCode配置的责任编码={}，入参责任编码={}，配置的方案编码={}，入参方案编码={}", configDutyCode, detail.getDutyCode(), JsonUtils.toJsonString(configProjectCodeList), detail.getProductPackage());
            if (null != detail.getDutyCode() && configDutyCode.contains(detail.getDutyCode()) && configProjectCodeList.contains(detail.getProductPackage())) {
                //看病报销金·门诊升级版 产品编码-02P00004  的合理金额计算为：发票金额-不合理金额-第三方支付金额
                reasonableAmount = nvl(sumBillAmount, 0).subtract(sumimmoderateAmount).subtract(sumPrepaidAmount);
            } else {
                reasonableAmount = nvl(sumBillAmount, 0).subtract(nvl(sumPartialDeductible, 0)).subtract(nvl(sumDeductibleAmount, 0)).subtract(nvl(sumPrepaidAmount, 0)).subtract(nvl(sumimmoderateAmount, 0));
            }
            if(reasonableAmount.compareTo(BigDecimal.ZERO) < 0){
                reasonableAmount = BigDecimal.ZERO;
            }
            //计算合理费用
            dutyAttributeDTO = new DutyAttributeDTO();
            dutyAttributeDTO.setReasonableAmount(reasonableAmount);
            dutyAttributeDTO.setBillDate(billDate);//
            dutyAttributeDTO.setMedicalSettleFlag(mediclSettleFlag);
            if (Objects.equals("N", dutyAttributeDTO.getMedicalSettleFlag())) {
                dutyAttributeDTO.setSerialNo(1);
            } else {
                dutyAttributeDTO.setSerialNo(2);
            }

        }
        return dutyAttributeDTO;
    }

    /**
     * @Description 抽方法 处理非标准级距的计算
     * <AUTHOR>
     * @Date 2023/5/23 14:14
     **/
    private void handleNSInterval_New(DutyDetailPayDTO detail, Map<String, Map<String, String>> attributes, List<MedicalBillInfoVO> billInfoVOList) {
        List<AhcsDutyAttributeDetailEntity> attributeDetailList = dutyAttributeDetailMapper.getInfoByDutyAttributeId(attributes.get(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD)
                .get(DutyAttributeConst.ID_AHCS_DUTY_ATTRIBUTE));
        BigDecimal sumResult = BigDecimal.ZERO;
        StringBuilder settleReason = new StringBuilder();
        LogUtil.info("非标准及距计算信息账单信息billInfoVOList={}", JsonUtils.toJsonString(billInfoVOList));
        LogUtil.info("非标准及距计算信息险种明细detail={}", JsonUtils.toJsonString(detail));
        for (MedicalBillInfoVO vo : billInfoVOList) {
            BigDecimal billAmount = vo.getBillAmount();
            BigDecimal partialDeductible = vo.getPartialDeductible();
            BigDecimal deductibleAmount = vo.getDeductibleAmount();
            BigDecimal prepaidAmount = vo.getPrepaidAmount();
            BigDecimal immoderateAmount = vo.getImmoderateAmount();
            BigDecimal reasonableAmount = nvl(billAmount, 0).subtract(nvl(partialDeductible, 0)).subtract(nvl(deductibleAmount, 0)).subtract(nvl(prepaidAmount, 0)).subtract(nvl(immoderateAmount, 0));
            LogUtil.info("非标准及距计算合理费用reasonableAmount={}", BigDecimalUtils.toString(reasonableAmount));
            if (!attributeDetailList.isEmpty()) {
                detail.setPayProportion(BigDecimalUtils.NEGATIVE_ONE);
                BigDecimal result = BigDecimal.ZERO;

                // 合理费用
                BigDecimal amount = nvl(reasonableAmount, 0);
                // 免赔额
                BigDecimal remitAmount = nvl(detail.getRemitAmount(), 0);
                BigDecimal actualAmount = amount.subtract(remitAmount);
                // 如果合理费用小于免赔 则不用计算
                if (actualAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    detail.setTempSettleAmount(result);
                    detail.setDetailSettleReason(settleReason.append("不足免赔额"));
                    return;
                }

                Map<String, List<AhcsDutyAttributeDetailEntity>> a1 = attributeDetailList.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeColumnNo))
                        .collect(Collectors.groupingBy(AhcsDutyAttributeDetailEntity::getAttributeColumnNo, LinkedHashMap::new, Collectors.toList()));
                Set<String> interval = new HashSet();
                a1.forEach((k, v) -> {
                    v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                    BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                    BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                    if (actualAmount.compareTo(left) > 0 && actualAmount.compareTo(right) <= 0) {
                        interval.add(k);
                    }
                });
                Iterator iterator = a1.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry entry = (Map.Entry) iterator.next();
                    String k = (String) entry.getKey();
                    List<AhcsDutyAttributeDetailEntity> v = (List<AhcsDutyAttributeDetailEntity>) entry.getValue();
                    v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                    BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                    BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                    BigDecimal percent = new BigDecimal(v.get(2).getAttributeDetailValue());
                    BigDecimal rate = BigDecimalUtils.toPercent(percent);
                    if (interval.contains(k)) {
                        result = result.add(BigDecimalUtils.getPositive((actualAmount.subtract(left)).multiply(rate)));
                        settleReason.append("(").append(amount).append("-").append(remitAmount).append("-").append(left).append(")").append("×").append(percent).append("%");
                        break;
                    } else {
                        result = result.add((right.subtract(left)).multiply(rate));
                        settleReason.append("(").append(right).append("-").append(left).append(")").append("×").append(percent).append("% +");
                    }

                }
                sumResult = sumResult.add(result);

            }
        }
        detail.setTempSettleAmount(sumResult);
        detail.setDetailSettleReason(settleReason.append("=").append(sumResult.setScale(2, BigDecimal.ROUND_HALF_UP)));

    }

    /**
     * @Description 抽方法 处理非标准级距的计算
     * @Date 2023/5/23 14:14
     **/
    private void handleNSIntervalNew(DutyDetailPayDTO detail, BigDecimal reasonableAmount, Map<String, Map<String, String>> attributes) {
        List<AhcsDutyAttributeDetailEntity> attributeDetailList = dutyAttributeDetailMapper.getInfoByDutyAttributeId(attributes.get(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD)
                .get(DutyAttributeConst.ID_AHCS_DUTY_ATTRIBUTE));
        if (!attributeDetailList.isEmpty()) {
            detail.setPayProportionType("2");
            detail.setPayProportion(BigDecimalUtils.NEGATIVE_ONE);
            BigDecimal result = BigDecimal.ZERO;
            StringBuilder settleReason = new StringBuilder();
            // 合理费用
            BigDecimal amount = nvl(reasonableAmount, 0);
            // 免赔额
            BigDecimal remitAmount = nvl(detail.getRemitAmount(), 0);
            // 实际赔付金额
            BigDecimal actualAmount = amount.subtract(remitAmount);
            BigDecimal sumHistoryAmount = actualAmount.add(detail.getPolicyBillHistoryAmount());
            // 如果合理费用小于免赔 则不用计算
            if (actualAmount.compareTo(BigDecimal.ZERO) <= 0) {
                detail.setTempSettleAmount(result);
                detail.setDetailSettleReason(settleReason.append("不足免赔额"));
                return;
            }

            Map<String, List<AhcsDutyAttributeDetailEntity>> entityMap = attributeDetailList.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeColumnNo))
                    .collect(Collectors.groupingBy(AhcsDutyAttributeDetailEntity::getAttributeColumnNo, LinkedHashMap::new, Collectors.toList()));
            List<String> keyList = new ArrayList<>();
            entityMap.forEach((k, v) -> {
                v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                if (sumHistoryAmount.compareTo(left) > 0 && sumHistoryAmount.compareTo(right) <= 0) {
                    keyList.add(k);
                }
            });
            Integer locationKey = Integer.valueOf(keyList.get(0));
            //历史发票金额抵扣后剩余的
            BigDecimal residueHistoryAmount = detail.getPolicyBillHistoryAmount();
            BigDecimal residueactualAmount = actualAmount;
            for (Map.Entry<String, List<AhcsDutyAttributeDetailEntity>> entry : entityMap.entrySet()) {
                String key = entry.getKey();
                if (Integer.valueOf(key) > locationKey) {
                    break;
                }
                List<AhcsDutyAttributeDetailEntity> entityList = entry.getValue();
                entityList = entityList.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                BigDecimal left = new BigDecimal(entityList.get(0).getAttributeDetailValue());
                BigDecimal right = new BigDecimal(entityList.get(1).getAttributeDetailValue());
                BigDecimal percent = new BigDecimal(entityList.get(2).getAttributeDetailValue());
                BigDecimal rate = BigDecimalUtils.toPercent(percent);
                if (detail.getPolicyBillHistoryAmount().compareTo(BigDecimal.ZERO) == 0) {
                    if (Integer.valueOf(key) < locationKey) {
                        result = result.add(BigDecimalUtils.getPositive((right.subtract(left)).multiply(rate)));
                        setValue(settleReason, right, left, percent);
                        residueactualAmount = residueactualAmount.subtract(right.subtract(left));
                    } else if (Integer.valueOf(key) == locationKey) {
                        result = result.add(BigDecimalUtils.getPositive(residueactualAmount).multiply(rate));
                        setValue(settleReason, right, left, percent);
                    }
                } else {
                    //
                    if (residueHistoryAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (Integer.valueOf(key) <= locationKey) {
                            residueHistoryAmount = residueHistoryAmount.subtract(right.subtract(left));
                            if (residueHistoryAmount.compareTo(BigDecimal.ZERO) < 0) {
                                if (residueactualAmount.compareTo(residueHistoryAmount.negate()) <= 0) {
                                    result = result.add(residueactualAmount.multiply(rate));
                                    residueactualAmount = BigDecimal.ZERO;
                                } else {
                                    ////residueHistoryAmount.negate()相反数，residueHistoryAmount为负数表示本级距的金额为抵扣完
                                    result = result.add(residueHistoryAmount.negate().multiply(rate));
                                    residueactualAmount = residueactualAmount.subtract(residueHistoryAmount.negate());
                                }

                            }
                        }
                    } else {
                        if (residueactualAmount.compareTo(BigDecimal.ZERO) > 0) {
                            result = result.add(residueactualAmount.multiply(rate));
                            setValue(settleReason, right, left, percent);
                        }
                    }


                }

            }
            detail.setTempSettleAmount(result);
            detail.setDetailSettleReason(settleReason.append("=").append(result.setScale(2, BigDecimal.ROUND_HALF_UP)));
        }
    }
    /**
     * 判断获取发票日所在合同月，及合同月已赔付次数
     * @param dtoList
     * @param detail
     */
    private void filterBillData(List<DutyAttributeDTO> dtoList, DutyDetailPayDTO detail) {
        if(Objects.isNull(detail.getConfigPayDays())){
            return ;
        }
        if(CollectionUtils.isEmpty(dtoList)){
            return ;
        }
        //设置每一张发票日期所在的合同月
        setMonthValue(dtoList,detail);
        //按合同月分组：合同月内的所有发票 的合同月起始时间一致
        Map<Integer,List<DutyAttributeDTO>>  mothListMap=dtoList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getMonth)).collect(Collectors.groupingBy(DutyAttributeDTO::getMonth,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Integer,List<DutyAttributeDTO>> entry : mothListMap.entrySet()) {
            List<DutyAttributeDTO>  dutyattList = entry.getValue();
            PolicyMonthDto policyMonthDto = dutyattList.get(0).getPolicyMonthDto();
            if(entry.getKey()==-1){
                //不在保单有效期内的发票
                continue;
            }
            //合同月内已赔付的所有发票日期记录
            List<DutyBillLimitDto> dutyBillLimitDtoLists = getDutyLimitMonth(policyMonthDto,detail);
            if(CollectionUtils.isEmpty(dutyBillLimitDtoLists)){
                //合同月内没有查询到赔付记录
                setBillValue(dutyattList,detail);//给发票日期打标 是否能参与本次理算
            }else {
                //合同月内查询到了记录
                dealMothData(dutyBillLimitDtoLists,dutyattList,detail);
            }

        }
    }

    /**
     * 合同月内已有赔付记录的情况
     * 判段是否已超每月赔付天数
     * @param payBillLimitDtoLists
     * @param dutyattList
     * @param detail
     */
    private void dealMothData(List<DutyBillLimitDto> payBillLimitDtoLists, List<DutyAttributeDTO> dutyattList, DutyDetailPayDTO detail) {
        //按发票日期排序分组
        Map<Date,List<DutyAttributeDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getBillDate)).collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        int count =payBillLimitDtoLists.size();//已赔付的天数
        for (Map.Entry<Date,List<DutyAttributeDTO>> entry : dateListMap.entrySet()) {
            List<DutyBillLimitDto> billLimitDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> !Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
            //如果不包含当前发票日期的条数都已经大于等于了每月赔付天数，那么当前发票日期的发票就不能参与计算了
            if(billLimitDtos.size()>=detail.getConfigPayDays()){
                setFlagY(entry.getValue());
            }else{
                //过滤看是否包含当前发票日期
                List<DutyBillLimitDto> currentDateDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(currentDateDtos)){
                    //包含当前发票日,当前发票日期可以参与计算
                    if(count<=detail.getConfigPayDays()){
                        setFlagN(entry.getValue());
                    }else{
                        setFlagY(entry.getValue());
                    }

                }else{
                    //不包含当前发票日期
                   if(count<detail.getConfigPayDays()){
                       setFlagN(entry.getValue());
                       count++;
                   }else{
                       setFlagY(entry.getValue());
                   }

                }


            }

        }


    }

    /**
     * 判断并给发票日期打标
     * @param dutyattList
     * @param detail
     */
    private void setBillValue(List<DutyAttributeDTO> dutyattList, DutyDetailPayDTO detail) {
        //得到每月赔付天数
        Integer configPayDays = detail.getConfigPayDays();
        Map<Date,List<DutyAttributeDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getBillDate)).collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        int count=0;
        for (Map.Entry<Date,List<DutyAttributeDTO>> entry : dateListMap.entrySet()) {
            if(count<configPayDays){
                setFlagN(entry.getValue());//发票日的数据可以参与计算
                count++;
            }else{
                setFlagY(entry.getValue());
            }
        }
    }

    /**
     * 不参与计算的发票标记为Y Y-表示 超每月赔付天数
     * @param list
     */
    private void setFlagY(List<DutyAttributeDTO> list) {
        for (DutyAttributeDTO  dto :list) {
            dto.setExceedMothPayDays("Y");
        }
    }

    /**
     * 参与计算的发票 标记为N
     * @param list
     */
    private void setFlagN(List<DutyAttributeDTO> list) {
        for (DutyAttributeDTO  dto :list) {
            dto.setExceedMothPayDays("N");
        }
    }
    private void setYearlyFlagN(List<DutyAttributeDTO> list) {
        for (DutyAttributeDTO  dto :list) {
            dto.setExceedYearlyPayDays("N");
        }
    }
    private void setYearlyFlagY(List<DutyAttributeDTO> list) {
        for (DutyAttributeDTO  dto :list) {
            dto.setExceedYearlyPayDays("Y");
        }
    }

    private void setMonthValue(List<DutyAttributeDTO> dtoList, DutyDetailPayDTO detail) {
        for (DutyAttributeDTO dto : dtoList) {
            PolicyMonthDto policyMonthDto =  getStartEndDate(dto.getBillDate(),detail.getMonthDtoList());
            if(Objects.isNull(policyMonthDto)){
                dto.setEffectiveFlag("N");
                dto.setMonth(-1);//不存在合同月、、不在保单有效期内
            }else {
                dto.setEffectiveFlag("Y");
                dto.setMonth(policyMonthDto.getMonth());
                dto.setPolicyMonthDto(policyMonthDto);
            }

        }

    }

    private List<DutyBillLimitDto> getDutyLimitMonth(PolicyMonthDto policyMonthDto,DutyDetailPayDTO detail) {
        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
        dutyLimitQueryVo.setPolicyNo(detail.getPolicyNo());
        dutyLimitQueryVo.setPlanCode(detail.getPlanCode());
        dutyLimitQueryVo.setDutyCode(detail.getDutyCode());
        dutyLimitQueryVo.setSatrtDate(policyMonthDto.getStartDate());
        dutyLimitQueryVo.setEndDate(policyMonthDto.getEndDate());
        LogUtil.info("报案号={}，责任查询参数={}",detail.getReportNo(),JsonUtils.toJsonString(dutyLimitQueryVo));
        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);
        if(CollectionUtils.isEmpty(dtos)){
            return dtos;
        }
        if(detail.getCaseTimes()>1){
           //重开案件去掉当前报案号对应的发票日期
            dtos= dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(detail.getReportNo(),dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
        }
        List<DutyBillLimitDto> returnList=new ArrayList<>();
        Map<Date,List<DutyBillLimitDto>>  mothListMap=dtos.stream().sorted(Comparator.comparing(DutyBillLimitDto::getBillDate)).collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyBillLimitDto>> entry : mothListMap.entrySet()){
            List<DutyBillLimitDto> evDayList = entry.getValue();
            List<String> caseNoList=getCaseNoList(evDayList);
            Integer count= dutyDetailPayMapper.getIndmenityInfo(caseNoList,detail.getDutyCode());
            if(count>=1){
                DutyBillLimitDto dto=new DutyBillLimitDto();
                dto.setBillDate(entry.getKey());
                returnList.add(dto);
            }
        }
        return returnList;
    }

    private List<String> getCaseNoList(List<DutyBillLimitDto> evDayList) {
        List<String> caseNolist=new ArrayList<>();
        for (DutyBillLimitDto dto : evDayList) {
            caseNolist.add(dto.getCaseNo());
        }
        return caseNolist;
    }

    private List<DutyAttributeDTO> setBillFlag(List<DutyAttributeDTO> dtoList) {
        for (DutyAttributeDTO dto : dtoList) {
            dto.setExceedMothPayDays("Y");
        }
        return dtoList;
    }

    /**
     * Match匹配发票日期在哪一个合同月
     * @param billDate
     * @param monthDtoList
     */
    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }

    /**
     * 年段赔付天数校验
     * @param dtoList
     * @param detail
     */
    private void checkYearlyPayDays(List<DutyAttributeDTO> dtoList, DutyDetailPayDTO detail) {
        if(Objects.isNull(detail.getConfiYearlyPayDays())||detail.getConfiYearlyPayDays()==0){
            return ;
        }
        if(CollectionUtils.isEmpty(dtoList)){
            return ;
        }
        //查询保单责任年段已经结案赔付的天数
        List<DutyBillLimitDto> dutyBillLimitDtoLists= getPolicyDutyYearlyPayDays(detail);
        detail.setYearlyPayDays(dutyBillLimitDtoLists.size());//年度已赔付的天数
        //按发票日期分组排序
        Map<Date,List<DutyAttributeDTO>>  listMap=dtoList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getBillDate)).collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyAttributeDTO>> entry :listMap.entrySet()){
            List<DutyAttributeDTO>  dutyattList = entry.getValue();
            if(CollectionUtils.isEmpty(dutyBillLimitDtoLists)){
                //保单年度内没有查询到赔付记录
                setYearlyBillValue(dutyattList,detail);//给发票日期打标 是否能参与本次理算
            }else {
                //保单年度内查询到了记录
                dealYeaylyData(dutyBillLimitDtoLists,dutyattList,detail);
            }
        }

    }

    /**
     * 查询已赔付
     * @param detail
     * @return
     */
    private List<DutyBillLimitDto> getPolicyDutyYearlyPayDays(DutyDetailPayDTO detail) {
        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
        dutyLimitQueryVo.setPolicyNo(detail.getPolicyNo());
        dutyLimitQueryVo.setPlanCode(detail.getPlanCode());
        dutyLimitQueryVo.setDutyCode(detail.getDutyCode());
        dutyLimitQueryVo.setSatrtDate(detail.getInsuranceBeginTime());
        dutyLimitQueryVo.setEndDate(detail.getInsuranceEndTime());
        LogUtil.info("年度已已赔付天数报案号={}，责任查询参数={}",detail.getReportNo(),JsonUtils.toJsonString(dutyLimitQueryVo));
        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);
        if(CollectionUtils.isEmpty(dtos)){
            return dtos;
        }
        if(detail.getCaseTimes()>1){
            //重开案件去掉当前报案号对应的发票日期
            dtos= dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(detail.getReportNo(),dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
        }
        List<DutyBillLimitDto> returnList=new ArrayList<>();
        Map<Date,List<DutyBillLimitDto>>  mothListMap=dtos.stream().sorted(Comparator.comparing(DutyBillLimitDto::getBillDate)).collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyBillLimitDto>> entry : mothListMap.entrySet()){
            List<DutyBillLimitDto> evDayList = entry.getValue();
            List<String> caseNoList=getCaseNoList(evDayList);
            Integer count= dutyDetailPayMapper.getIndmenityInfo(caseNoList,detail.getDutyCode());
            if(count>=1){
                DutyBillLimitDto dto=new DutyBillLimitDto();
                dto.setBillDate(entry.getKey());
                returnList.add(dto);
            }
        }
        return returnList;
    }
    /**
     * 判断并给发票日期打标
     *
     * @param dutyattList
     * @param detail
     * @param
     */
    private void setYearlyBillValue(List<DutyAttributeDTO> dutyattList, DutyDetailPayDTO detail) {
        //得到每月赔付天数
        Integer confYearlyPayDays = detail.getConfiYearlyPayDays();
        Map<Date,List<DutyAttributeDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getBillDate)).collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyAttributeDTO>> entry : dateListMap.entrySet()) {
            if(detail.getYearlyPayDays()<confYearlyPayDays){
                if(Objects.equals("Y",entry.getValue().get(0).getExceedMothPayDays())){
                    setYearlyFlagY(entry.getValue());
                }else{
                    setYearlyFlagN(entry.getValue());//发票日的数据可以参与计算
                    detail.setYearlyPayDays(detail.getYearlyPayDays()+1);
                }

            }else{
                setYearlyFlagY(entry.getValue());
            }
        }
    }
    /**
     * 合同月内已有赔付记录的情况
     * 判段是否已超每月赔付天数
     *
     * @param payBillLimitDtoLists
     * @param dutyattList
     * @param detail
     * @param
     */
    private void dealYeaylyData(List<DutyBillLimitDto> payBillLimitDtoLists, List<DutyAttributeDTO> dutyattList, DutyDetailPayDTO detail) {
        //按发票日期排序分组
        Map<Date,List<DutyAttributeDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getBillDate)).collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyAttributeDTO>> entry : dateListMap.entrySet()) {
            List<DutyBillLimitDto> billLimitDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> !Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
            //如果不包含当前发票日期的条数都已经大于等于了年度赔付天数，那么当前发票日期的发票就不能参与计算了
            if(billLimitDtos.size()>=detail.getConfiYearlyPayDays()){
                setYearlyFlagY(entry.getValue());
            }else{
                //过滤看是否包含当前发票日期
                List<DutyBillLimitDto> currentDateDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(currentDateDtos)){
                    //包含当前发票日,当前发票日期可以参与计算
                    if(detail.getYearlyPayDays()<=detail.getConfiYearlyPayDays()){
                        setYearlyFlagN(entry.getValue());
                    }else{
                        setYearlyFlagY(entry.getValue());
                    }

                }else{
                    //不包含当前发票日期
                    if(detail.getYearlyPayDays()<detail.getConfiYearlyPayDays()){
                        if(Objects.equals("Y",entry.getValue().get(0).getExceedMothPayDays())){
                            setYearlyFlagY(entry.getValue());
                        }else{
                            setYearlyFlagN(entry.getValue());
                            detail.setYearlyPayDays(detail.getYearlyPayDays()+1);
                        }

                    }else{
                        setYearlyFlagY(entry.getValue());
                    }

                }


            }

        }


    }

}
