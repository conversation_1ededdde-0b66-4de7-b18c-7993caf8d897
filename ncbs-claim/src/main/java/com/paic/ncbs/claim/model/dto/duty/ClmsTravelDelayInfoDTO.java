package com.paic.ncbs.claim.model.dto.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 延误信息
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsTravelDelayInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("延误类型 1 航空延误、2 旅行延误、3 其他延误")
    private String delayType;

    @ApiModelProperty("其他延误描述")
    private String otherTypeDescription;

    @ApiModelProperty("延误起期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date delayStartDate;

    @ApiModelProperty("延误止期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date delayEndDate;

    @ApiModelProperty("延误天数")
    private Integer delayDays;

    @ApiModelProperty("航空公司")
    private String airCompany;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("出发地")
    private List<String> departPlace;

    @ApiModelProperty("目的地")
    private List<String> arrivalPlace;

    @ApiModelProperty("预计起飞时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date expectTakeOffTime;

    @ApiModelProperty("预计到达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date expectArrivalTime;

    @ApiModelProperty("实际到达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date realArrivalTime;

    @ApiModelProperty("费用金额")
    private BigDecimal amount;

}

