package com.paic.ncbs.claim.service.other;

import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.*;
import com.paic.ncbs.claim.model.vo.duty.LossTypeVO;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.List;
import java.util.Map;

public interface CommonParameterService extends BaseService<CommonParameterEntity> {

    List<CommonParameterTinyDTO> getCommonParameterList(String[] collectionCodeList);

    Map<String, List<CommonParameterTinyDTO>> getDataDictList(String[] collectionCodeList);

    String getTotalSwitch();

    List<String> getCommonParameterByCode(String collectionCode);

    String getValueChineseName(String valueCode,String collectionCode)throws GlobalBusinessException;

    AdressSearchDto getDetailAdressFormCode(AdressSearchDto adressSearchDto);
    /**
     * 查询出险原因下拉数据
     */
    List<AccidentReasonTypeInfoDTO> getAccidentReason();
    /**
     * 查询财产损失原因下拉数据
     */
    List<PropertyLossReasonDTO> getPropertyLossReason();

    /**
     * 查询救援类型
     * @return
     */
    List<SuccourTypeDTO> getSusccourTypeList();

    /**
     * 根据证件号、保单号、被保人姓名查询出险原因
     * @param certificateNo
     * @param policyList
     * @param insuredName
     * @return
     */
    List<AccidentReasonTypeInfoDTO> getAccidentReason(String certificateNo, String policyList, String insuredName);

    /**
     * 查询重点项目
     * @return
     */
    List<KeyProjectsDTO> getKeyProjectsList();
    /**
     * 查询险种对应的损失分类
     * @param certificateNo
     * @param policyList
     * @param insuredName
     * @return
     */
    List<LossTypeVO> getLossClass(String certificateNo, String policyList, String insuredName);
    /**
     * 查询全部损失分类
     * @return
     */
    List<LossTypeVO> getLossClass();
}
