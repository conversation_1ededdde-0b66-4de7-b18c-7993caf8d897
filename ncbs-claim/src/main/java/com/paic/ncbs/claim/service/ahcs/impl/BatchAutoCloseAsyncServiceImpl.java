package com.paic.ncbs.claim.service.ahcs.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentDuty;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentPlan;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.HospitalInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.BatchAutoCloseDTO;
import com.paic.ncbs.claim.model.dto.ahcs.BatchAutoFinishDTO;
import com.paic.ncbs.claim.model.dto.ahcs.BatchAutoReportDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.dto.endcase.*;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchAutoClose;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchAutoReportDTO;
import com.paic.ncbs.claim.model.vo.batch.OnlinePaymentInfo;
import com.paic.ncbs.claim.model.vo.batch.OnlineZeroCancelInfo;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.duty.DutyEndorsementVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyEnendorsementVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyInfoVO;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimService;
import com.paic.ncbs.claim.mq.producer.MqProducerEndcaseService;
import com.paic.ncbs.claim.mq.producer.MqProducerRegistService;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseAsyncService;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseTransationService;
import com.paic.ncbs.claim.service.checkloss.HospitalInfoService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import com.paic.ncbs.claim.service.settle.SettleValidateService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.ConfigConstValues.AUDIT_NOTNEED;

@Service("batchAutoCloseAsyncService")
@RefreshScope
public class BatchAutoCloseAsyncServiceImpl implements BatchAutoCloseAsyncService {

    @Autowired
    private CommonService commonService;
    @Autowired
    private BatchAutoCloseTransationService batchAutoCloseTransationService;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Autowired
    private MqProducerRegistService mqProducerRegistService;
    @Autowired
    private MqProducerClaimService mqProducerClaimService;
    @Autowired
    private MqProducerEndcaseService mqProducerEndcaseService;
    @Autowired
    private HospitalInfoMapper hospitalInfoMapper;

    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Value("${save.batch.enabled:true}")
    private boolean saveBatchEnabled;

    // 立案人
    @Value("${return.insurance.batch.register:SYSTEM}")
    private String register;
    //报案人
    @Value("${return.insurance.batch.resporter:SYSTEM}")
    private String resporter;
    //核赔人编码
    @Value("${return.insurance.batch.assigner:SYSTEM}")
    private String assigner;
    //核赔人名称
    @Value("${return.insurance.batch.assigneeName:SYSTEM}")
    private String assigneeName;
    // 核赔机构
//    @Value("${return.insurance.batch.verifyDepartmentCode:1}")
//    private String verifyDepartmentCode;

    @Autowired
    private HospitalInfoService hospitalInfoService;
    @Autowired
    private PrintService printService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Autowired
    private PaymentInfoService paymentInfoService;

    @Autowired
    private ClmsPolicySurrenderInfoService clmsPolicySurrenderInfoService;

    @Autowired
    private CaseZeroCancelMapper caseZeroCancelDao;
    @Autowired
    private SettleValidateService settleValidateService;
    @Autowired
    private PaymentItemService paymentItemService;


    @Override
    @Async("asyncPool")
    public void asyncBatchClose(String batchNo,List<BatchAutoReportDTO> autoReportDTOList) {
        LogUtil.audit("批量结案开始自动理赔batchNo={}",batchNo);
        for (BatchAutoReportDTO reportDTO : autoReportDTOList) {
            String reportNo = reportDTO.getAhcsDomainDTO().getReportNo();
            Integer caseTimes = 1;
            BatchAutoCloseDTO batchAutoCloseDTO = reportDTO.getBatchAutoCloseDTO();
            BatchAutoFinishDTO finishDTO = new BatchAutoFinishDTO();

            buildCaseProcess(reportNo,caseTimes,reportDTO.getAhcsDomainDTO(),finishDTO);
            buildCaseClass(reportNo,caseTimes, batchAutoCloseDTO.getCaseClass(),finishDTO,false);
            String policyPlanDutyCode = batchAutoCloseDTO.getPolicyNo()+batchAutoCloseDTO.getPlanCode()+batchAutoCloseDTO.getDutyCode();
            buidPolicyClaimCase(reportNo,caseTimes,finishDTO,policyPlanDutyCode,batchAutoCloseDTO.getPayAmount());
            String policyPlanDutyDetailCode = batchAutoCloseDTO.getPolicyNo()+batchAutoCloseDTO.getPlanCode()+batchAutoCloseDTO.getDutyCode()+batchAutoCloseDTO.getDutyDetailCode();
            buildPolicyPay(reportNo,caseTimes, policyPlanDutyDetailCode,finishDTO,batchAutoCloseDTO.getPayAmount());
            buildPayment(reportNo,caseTimes,reportDTO,finishDTO);
            bulidClmsEmtimateRecord(reportNo,caseTimes,finishDTO);
            buildWholeCase(reportNo,caseTimes,finishDTO,false);
            buildChannelProcess(reportNo,caseTimes,finishDTO);
            buildPersonAccident(reportNo,caseTimes, batchAutoCloseDTO,finishDTO);
            buildPersonDiagnose(reportNo,caseTimes, batchAutoCloseDTO,finishDTO);
            buildPersonHospital(reportNo,caseTimes, batchAutoCloseDTO,finishDTO);
            if(saveBatchEnabled){
                batchAutoCloseTransationService.autoCloseBatch(finishDTO);
            }else{
                batchAutoCloseTransationService.autoClose(finishDTO);
            }
            LogUtil.audit(JSON.toJSONString(finishDTO));
            // 送收付
            payInfoNoticeThirdPartyCoreSAO.noticePayment(reportNo,caseTimes, null, true, false);
//            asyncBatchCloseSendMessage(reportDTO);
            printService.sendPrintCore(reportNo,caseTimes);
        }
        LogUtil.audit("批量结案完成自动理赔batchNo={}",batchNo);
    }

    private void bulidClmsEmtimateRecord(String reportNo, Integer caseTimes, BatchAutoFinishDTO finishDTO) {
        ClmsEstimateRecord record = new ClmsEstimateRecord();
        Date date = new Date();
        record.setReportNo(reportNo);
        record.setCaseTimes(caseTimes);
        record.setIdClmsEstimateRecord(UuidUtil.getUUID());
        record.setEstimateType(EstimateTypeEnum.REGISTER_PENDING.getType());
        record.setEstimateTypeName(EstimateTypeEnum.REGISTER_PENDING.getName());
        record.setCreatedBy(ConstValues.SYSTEM);
        record.setUpdatedBy(ConstValues.SYSTEM);
        record.setCreatedDate(date);
        record.setUpdatedDate(date);
        // 为了时间一致
        record.setEffectiveTime(finishDTO.getEstimateDutyRecordList().get(0).getArchiveTime());
        record.setRecordUserId(ConstValues.SYSTEM);
        record.setRecordUserName(ConstValues.SYSTEM);
        List<EstimatePolicyDTO> estimatePolicyDTOList = finishDTO.getPolicyList();
        BigDecimal estimateAmount=BigDecimal.ZERO;
        for (EstimatePolicyDTO dto : estimatePolicyDTOList) {
            estimateAmount=estimateAmount.add(dto.getEstimateAmount());
        }
        record.setEstimateAmount(estimateAmount);
        finishDTO.setClmsEstimateRecord(record);
    }

    @Override
    public void asyncBatchCloseOnline(String batchNo,List<OnlineBatchAutoReportDTO> autoReportDTOList) {
        LogUtil.audit("批量结案开始自动理赔batchNo={}",batchNo);
        Integer caseTimes = 1;
        for (OnlineBatchAutoReportDTO reportDTO : autoReportDTOList) {
            String reportNo = reportDTO.getAhcsDomainDTO().getReportNo();
            OnlineBatchAutoClose onlineBatchAutoClose = reportDTO.getOnlineBatchAutoClose();
            BatchAutoFinishDTO finishDTO = new BatchAutoFinishDTO();
            buildCaseProcess(reportNo,caseTimes,reportDTO.getAhcsDomainDTO(),finishDTO);

            //  出险类型
            String caseClass = ReportConstant.THREE_SOURCE_DIANPING.equals(onlineBatchAutoClose.getThreeSource())
                    ? onlineBatchAutoClose.getCaseClass() :InsuredApplyTypeEnum.OTHER_ALLOWANCE.getType();
            buildCaseClass(reportNo,caseTimes,caseClass,finishDTO,false);
            // 退运险/美团点评上游传不了责任明细编码 此处默认只有单险种 单责任 单责任明细 否则有问题, 大众点评说只有单个责任
            BigDecimal payAmount = onlineBatchAutoClose.getPaymentInfo().getPayAmount();
            LogUtil.audit("批量结案- 自动理赔- payAmount={}",payAmount);
            String policyPlanDutyCode = onlineBatchAutoClose.getPolicyNo()+onlineBatchAutoClose.getPlanCode()+onlineBatchAutoClose.getDutyCode();
            buidPolicyClaimCase(reportNo,caseTimes,finishDTO,policyPlanDutyCode, payAmount);//DutyRecord的ArchiveTime为空，History表的生效时间也为空
            String policyPlanDutyDetailCode = onlineBatchAutoClose.getPolicyNo()+onlineBatchAutoClose.getPlanCode()+onlineBatchAutoClose.getDutyCode()+onlineBatchAutoClose.getDutyDetailCode();
            buildPolicyPay(reportNo,caseTimes,policyPlanDutyDetailCode,finishDTO, payAmount);
            buildPaymentOnline(reportNo,caseTimes,reportDTO,finishDTO);
            bulidClmsEmtimateRecord(reportNo,caseTimes,finishDTO);
            buildWholeCase(reportNo,caseTimes,finishDTO,false);
            bulidPaymentPlanAndDuty(finishDTO);
            //  因监管需要 还需补充 自动立案记录 和 核赔审批记录
            bulidCaseRegisterApplyDTO(reportNo,caseTimes,finishDTO, payAmount);
            bulidVerifyTask(reportDTO.getAhcsDomainDTO(),finishDTO,false);
            if(saveBatchEnabled){
                batchAutoCloseTransationService.autoCloseBatch(finishDTO);
            }else{
                batchAutoCloseTransationService.autoClose(finishDTO);
            }
            LogUtil.audit(JSON.toJSONString(finishDTO));

        }
        for (OnlineBatchAutoReportDTO reportDTO : autoReportDTOList) {
            payInfoNoticeThirdPartyCoreSAO.noticePayment(reportDTO.getAhcsDomainDTO().getReportNo(),caseTimes, null, true, false);
        }

        // 退运险送再保不影响主流程 单独调用
        sendReinsurance(autoReportDTOList);

        LogUtil.audit("批量结案完成自动理赔batchNo={}",batchNo);
    }

    /**
     *
     * @param batchNo
     * @param autoReportDTOList
     * @param requestAttributes  部分代码需要使用上下文信息不统一处理了，线程问题单独传值。
     */
    @Override
    public void asyncOntTimeZeroCancelOnline(String batchNo,List<OnlineBatchAutoReportDTO> autoReportDTOList,
                                             RequestAttributes requestAttributes) {
        LogUtil.audit("一步零结开始自动理赔batchNo={}",batchNo);
        Integer caseTimes = 1;
        // 单独SET
        RequestContextHolder.setRequestAttributes(requestAttributes);
        for (OnlineBatchAutoReportDTO reportDTO : autoReportDTOList) {
            BatchAutoFinishDTO finishDTO = new BatchAutoFinishDTO();
            String reportNo = reportDTO.getAhcsDomainDTO().getReportNo();
            OnlineBatchAutoClose onlineBatchAutoClose = reportDTO.getOnlineBatchAutoClose();
            //  出险类型
            String caseClass = ReportConstant.THREE_SOURCE_DIANPING.equals(onlineBatchAutoClose.getThreeSource())
                    ? onlineBatchAutoClose.getCaseClass() :InsuredApplyTypeEnum.OTHER_ALLOWANCE.getType();
            buildCaseClass(reportNo,caseTimes,caseClass,finishDTO,true);// TODO taskId更改
            buildCaseProcess(reportNo,caseTimes,reportDTO.getAhcsDomainDTO(),finishDTO);
            //美团点评上游传不了责任明细编码 此处默认只有单险种 单责任 单责任明细 否则有问题, 大众点评说只有单个责任
            BigDecimal payAmount = onlineBatchAutoClose.getPaymentInfo().getPayAmount();
            LogUtil.audit("一步零结-自动理赔");
            String policyPlanDutyCode = onlineBatchAutoClose.getPolicyNo()+onlineBatchAutoClose.getPlanCode()+onlineBatchAutoClose.getDutyCode();
            buidPolicyClaimCase(reportNo,caseTimes,finishDTO,policyPlanDutyCode, payAmount);//未决表 立案用到
            bulidClmsEmtimateRecord(reportNo,caseTimes,finishDTO);
            String policyPlanDutyDetailCode = onlineBatchAutoClose.getPolicyNo()+onlineBatchAutoClose.getPlanCode()+onlineBatchAutoClose.getDutyCode()+onlineBatchAutoClose.getDutyDetailCode();
            buildPolicyPay(reportNo,caseTimes,policyPlanDutyDetailCode,finishDTO, payAmount); //零注只要BatchDTO和Endorsement
            buildWholeCase(reportNo,caseTimes,finishDTO,true);
            //因监管需要 还需补充 自动立案记录 和 核赔审批记录
            bulidCaseRegisterApplyDTO(reportNo,caseTimes,finishDTO, payAmount);
            bulidVerifyTask(reportDTO.getAhcsDomainDTO(),finishDTO, true);
            //校验责任明细和责任明细基本保额不能为空
            settleValidateService.validDutyDetailNull(reportNo, caseTimes);
            //校验是否有在途解约
            clmsPolicySurrenderInfoService.checkSurrenderIsOtherCase(reportNo,caseTimes);
            clmsPolicySurrenderInfoService.applyPolicySurrender(reportNo,caseTimes);
            //组装零注信息
            CaseZeroCancelDTO caseZeroCancelDTO = buildCaseZeroCancelDTO(reportNo,onlineBatchAutoClose.getZeroCancelInfo());//组装零结信息
            caseZeroCancelDao.addCaseZeroCancelApply(caseZeroCancelDTO);
            caseZeroCancelDTO.setVerifyUm(caseZeroCancelDTO.getApplyUm());
            caseZeroCancelDTO.setVerifyDate(new Date());
            caseZeroCancelDTO.setVerifyOptions(EndCaseConstValues.AUDIT_AGREE_CODE);
            caseZeroCancelDTO.setVerifyRemark(caseZeroCancelDTO.getApplyReasonDetails());
            caseZeroCancelDTO.setStatus(EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_APPROVED);
            caseZeroCancelDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
            caseZeroCancelDTO.setUpdatedDate(new Date());
            caseZeroCancelDao.saveCaseZeroCancelAudit(caseZeroCancelDTO);
            //批量插入上面组装的数据
            batchAutoCloseTransationService.autoZeroCancelClose(finishDTO);

            // printService.sendPrintCore(reportNo,caseTimes);
            LogUtil.audit("asyncOntTimeZeroCancelOnline组装后的参数: {}",JSON.toJSONString(finishDTO));
        }
        for (OnlineBatchAutoReportDTO reportDTO : autoReportDTOList) {
            // 零结和拒赔 判断是否有费用需要支付
            paymentItemService.checkAndPayFee(reportDTO.getAhcsDomainDTO().getReportNo(),caseTimes);
        }

        // 一步零结送再保不影响主流程 单独调用
        sendReinsurance(autoReportDTOList);

        LogUtil.audit("一步零结完成自动理赔batchNo={}",batchNo);
    }

    /**
     * 同步调用
     *
     * @param batchNo
     * @param autoReportList
     */
    @Override
    public void batchCloseOnline(String batchNo, List<OnlineBatchAutoReportDTO> autoReportList) {
        LogUtil.audit("同步结案开始自动理赔batchNo={}",batchNo);
        // 自己内容调用使 @Async("asyncPool") 失效
        this.asyncBatchCloseOnline(batchNo,autoReportList);
        LogUtil.audit("同步结案开始自动理赔batchNo={}",batchNo);
    }

    private CaseZeroCancelDTO buildCaseZeroCancelDTO(String reportNo, OnlineZeroCancelInfo onlineZeroCancelInfo) {
        CaseZeroCancelDTO caseZeroCancelDTO = new CaseZeroCancelDTO();
        Date currentDate = new Date();
        caseZeroCancelDTO.setIdAhcsZeroCancelApply(UuidUtil.getUUID());
        caseZeroCancelDTO.setReportNo(reportNo);
        caseZeroCancelDTO.setCaseTimes(1);
        caseZeroCancelDTO.setApplyTimes(1);
        caseZeroCancelDTO.setApplyType(onlineZeroCancelInfo.getApplyType());
        caseZeroCancelDTO.setReasonCode(onlineZeroCancelInfo.getReasonCode());
        caseZeroCancelDTO.setApplyReasonDetails(onlineZeroCancelInfo.getApplyReasonDetails());
        caseZeroCancelDTO.setApplyUm("美团默认");// TODO
        caseZeroCancelDTO.setApplyDate(currentDate);
        caseZeroCancelDTO.setVerifyUm("美团默认");// TODO
        caseZeroCancelDTO.setVerifyDate(currentDate);
        caseZeroCancelDTO.setVerifyOptions(EndCaseConstValues.AUDIT_AGREE_CODE);
        caseZeroCancelDTO.setVerifyRemark(onlineZeroCancelInfo.getApplyReasonDetails());
        caseZeroCancelDTO.setStatus(EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING);
        caseZeroCancelDTO.setCreatedBy(ConstValues.SYSTEM_UM);
        caseZeroCancelDTO.setCreatedDate(currentDate);
        caseZeroCancelDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
        caseZeroCancelDTO.setUpdatedDate(currentDate);
        return caseZeroCancelDTO;
    }

    public void sendReinsurance(List<OnlineBatchAutoReportDTO> autoReportDTOList) {
        LogUtil.info("退运险-sendReinsurance-start");
        autoReportDTOList.forEach(reportDTO->{
            String reportNo = reportDTO.getAhcsDomainDTO().getReportNo();
            RepayCalDTO repayCalDTO = new RepayCalDTO();
            repayCalDTO.setReportNo(reportNo);
            repayCalDTO.setCaseTimes(1);
            repayCalDTO.setCaseScene("01");
            repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.REGISTER);
            reinsuranceService.sendReinsurance(repayCalDTO);
        });
        try {
            // 确保createDate不一样 延时两秒调用
            Thread.sleep(2000L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        autoReportDTOList.forEach(reportDTO->{
            String reportNo = reportDTO.getAhcsDomainDTO().getReportNo();
            RepayCalDTO repayCalDTO = new RepayCalDTO();
            repayCalDTO.setReportNo(reportNo);
            repayCalDTO.setCaseTimes(1);
            repayCalDTO.setCaseScene("01");
            repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
            repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
            reinsuranceService.sendReinsurance(repayCalDTO);
        });
        LogUtil.info("退运险-sendReinsurance-end");
    }

    /**
      *
      * @Description 构建核赔审批记录
      * <AUTHOR>
      * @Date 2023/7/19 14:11
      **/
    private void bulidVerifyTask(AhcsDomainDTO ahcsDomainDTO, BatchAutoFinishDTO finishDTO, boolean isZeroCancel) {
        TaskInfoDTO startTask = new TaskInfoDTO();
        startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
        startTask.setTaskId(UuidUtil.getUUID());
        startTask.setReportNo(ahcsDomainDTO.getReportNo());
        startTask.setCaseTimes(1);
        startTask.setTaskDefinitionBpmKey(isZeroCancel? BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT :BpmConstants.OC_SETTLE_REVIEW);
        startTask.setAssigneeTime(new Date());
        startTask.setCreatedBy(ConstValues.SYSTEM);
        startTask.setUpdatedBy(ConstValues.SYSTEM);
        startTask.setApplyer(ConstValues.SYSTEM);
        startTask.setApplyerName(ConstValues.SYSTEM);
        startTask.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
        startTask.setTaskGrade(4);
        startTask.setAuditGrade(1);
        String departmentCode = ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode();
        startTask.setDepartmentCode(departmentCode);
        // 核赔处理人
        startTask.setAssigner(assigner);
        startTask.setAssigneeName(assigneeName);
        finishDTO.setTaskInfoDTO(startTask);
    }

    /**
      *
      * @Description 构建立案审批记录
      * <AUTHOR>
      * @Date 2023/7/19 13:51
      **/
    private void bulidCaseRegisterApplyDTO(String reportNo,Integer caseTimes,BatchAutoFinishDTO finishDTO,BigDecimal payAmount) {
        CaseRegisterApplyDTO caseRegisterApplyDTO = new CaseRegisterApplyDTO();
        caseRegisterApplyDTO.setCreatedBy(ConstValues.SYSTEM);
        caseRegisterApplyDTO.setUpdatedBy(ConstValues.SYSTEM);
        String idAhcsRegisterApply = UuidUtil.getUUID();
        caseRegisterApplyDTO.setIdAhcsCaseRegisterApply(idAhcsRegisterApply);
        caseRegisterApplyDTO.setReportNo(reportNo);
        caseRegisterApplyDTO.setCaseTimes(caseTimes);
        caseRegisterApplyDTO.setApplyUm(resporter);
        caseRegisterApplyDTO.setAuditUm(register);
        caseRegisterApplyDTO.setApplyTimes(1);
        caseRegisterApplyDTO.setAuditDate(new Date());
        caseRegisterApplyDTO.setStatus(AUDIT_NOTNEED);
        caseRegisterApplyDTO.setAuditOpinion(ConstValues.AUDIT_AGREE_CODE);
        caseRegisterApplyDTO.setRegisterAmount(payAmount);
        finishDTO.setCaseRegisterApplyDTO(caseRegisterApplyDTO);
        WholeCaseBaseDTO wholeCaseBase = finishDTO.getWholeCaseBase();
        wholeCaseBase.setIsRegister(ConstValues.YES);
        wholeCaseBase.setRegisterDate(new Date());
        wholeCaseBase.setRegisterUm(register);
        finishDTO.setWholeCaseBase(wholeCaseBase);
    }

    // 构建险种、责任拆分 送收付
    private void bulidPaymentPlanAndDuty( BatchAutoFinishDTO finishDTO) {
        PaymentItemDTO dto = finishDTO.getPaymentitemList().get(0);
        PlanPayDTO planPayDTO = finishDTO.getPlanPayList().get(0);
        DutyPayDTO dutyPayDTO = finishDTO.getDutyPayList().get(0);
        // 根据保单号查询产品编码和产品大类
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(dto.getPolicyNo());
        String productCode = MapUtils.getString(productMap, "productCode");
        String productClass = MapUtils.getString(productMap, "productClass");
        String idClmPaymentItem = dto.getIdClmPaymentItem();
        BigDecimal paymentAmount = dto.getPaymentAmount();
        //查询险种大类
        String kindCode = ocasMapper.getPlyPlanInfo(dto.getPolicyNo(), planPayDTO.getPlanCode());
        String planId = UuidUtil.getUUID();
        ClmsPaymentPlan clmsPaymentPlan = new ClmsPaymentPlan();
        clmsPaymentPlan.setIdClmsPaymentPlan(planId);
        clmsPaymentPlan.setIdClmPaymentItem(idClmPaymentItem);
        clmsPaymentPlan.setPlanCode(planPayDTO.getPlanCode());
        clmsPaymentPlan.setKindCode(kindCode);
        clmsPaymentPlan.setProductCode(productCode);
        clmsPaymentPlan.setProductLineCode(productClass);
        clmsPaymentPlan.setPlanPayAmount(paymentAmount);
        clmsPaymentPlan.setTaxAmount(BigDecimal.ZERO);
        clmsPaymentPlan.setNoTaxAmount(paymentAmount);
        clmsPaymentPlan.setCreatedBy(BaseConstant.SYSTEM);
        clmsPaymentPlan.setUpdatedBy(BaseConstant.SYSTEM);
        clmsPaymentPlan.setCreatedDate(new Date());
        clmsPaymentPlan.setUpdatedDate(new Date());
        List<ClmsPaymentPlan> clmsPaymentPlanList = new ArrayList<>();
        clmsPaymentPlanList.add(clmsPaymentPlan);
        finishDTO.setClmsPaymentPlanList(clmsPaymentPlanList);
        List<ClmsPaymentDuty> clmsPaymentDutyList = new ArrayList<>();
        ClmsPaymentDuty clmsPaymentDuty = new ClmsPaymentDuty();
        clmsPaymentDuty.setIdClmsPaymentPlan(planId);
        clmsPaymentDuty.setIdClmsPaymentDuty(UuidUtil.getUUID());
        clmsPaymentDuty.setDutyCode(dutyPayDTO.getDutyCode());
        clmsPaymentDuty.setDutyPayAmount(paymentAmount);
        clmsPaymentDuty.setTaxAmount(BigDecimal.ZERO);
        clmsPaymentDuty.setNoTaxAmount(paymentAmount);
        clmsPaymentDuty.setCreatedBy(BaseConstant.SYSTEM);
        clmsPaymentDuty.setUpdatedBy(BaseConstant.SYSTEM);
        clmsPaymentDuty.setCreatedDate(new Date());
        clmsPaymentDuty.setUpdatedDate(new Date());
        clmsPaymentDutyList.add(clmsPaymentDuty);
        finishDTO.setClmsPaymentDutyList(clmsPaymentDutyList);
    }

    private void asyncBatchCloseSendMessage(BatchAutoReportDTO reportDTO) {
        LogUtil.audit("asyncBatchCloseSendMessage--syncRegistLink={}",reportDTO.getAhcsDomainDTO().getReportNo());
        mqProducerRegistService.syncRegistLink(reportDTO.getAhcsDomainDTO().getReportNo(),1);
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        LogUtil.audit("asyncBatchCloseSendMessage--syncClaimLink={}",reportDTO.getAhcsDomainDTO().getReportNo());
        mqProducerClaimService.syncClaimLink(reportDTO.getAhcsDomainDTO().getReportNo(),1);
        LogUtil.audit("asyncBatchCloseSendMessage--syncProducerBatchCompensateLink={}",reportDTO.getAhcsDomainDTO().getReportNo());
//        mqProducerBatchCompensateService.syncProducerBatchCompensateLink(reportDTO.getAhcsDomainDTO().getReportNo(),1);
        LogUtil.audit("asyncBatchCloseSendMessage--syncProducerEndcaseLink={}",reportDTO.getAhcsDomainDTO().getReportNo());
        mqProducerEndcaseService.syncProducerEndcaseLink(reportDTO.getAhcsDomainDTO().getReportNo(),1);
    }

    private void buildCaseProcess(String reportNo,Integer caseTimes,AhcsDomainDTO dto,BatchAutoFinishDTO finishDTO){
        String deptCode = dto.getAcceptDepartmentCode();
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(reportNo);
        caseProcessDTO.setCaseTimes(caseTimes);
        caseProcessDTO.setProcessStatus(CaseProcessStatus.CASE_CLOSED.getCode());
        caseProcessDTO.setIsNewProcess(ConstValues.YES);
        caseProcessDTO.setArchiveTime(new Date());
        caseProcessDTO.setCompanyCode(deptCode);
        caseProcessDTO.setCommissionCompany(deptCode);
        finishDTO.setCaseProcessDTO(caseProcessDTO);
    }

    private void buildCaseClass(String reportNo,Integer caseTimes,String caseClass,BatchAutoFinishDTO finishDTO,boolean isZeroCancel){
        List<CaseClassDTO> caseSubClassList = new ArrayList<>();
        CaseClassDTO caseClassDTO2 = new CaseClassDTO();
        caseClassDTO2.setCreatedBy(ConstValues.SYSTEM);
        caseClassDTO2.setUpdatedBy(ConstValues.SYSTEM);
        caseClassDTO2.setTaskId(isZeroCancel ? BpmConstants.REPORT_TRACK : BpmConstants.CHECK_DUTY);
        caseClassDTO2.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
        caseClassDTO2.setReportNo(reportNo);
        caseClassDTO2.setCaseTimes(caseTimes);
        caseClassDTO2.setCaseSubClass(caseClass);
        caseSubClassList.add(caseClassDTO2);
        finishDTO.setCaseSubClassList(caseSubClassList);
    }

    private void buidPolicyClaimCase(String reportNo,Integer caseTimes,BatchAutoFinishDTO finishDTO,String policyPlanDutyCode,BigDecimal estimateAmount){
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimateService.getPolicyCopy(reportNo, caseTimes);
        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            LogUtil.audit("#initpolicyClaimCase抄单的数据为空,reportNo={}",reportNo);
            throw new GlobalBusinessException("抄单数据为空reportNo={}",reportNo);
        }
        List<EstimateDutyRecordDTO> estimateDutyRecordList = new ArrayList<>();
        List<EstimatePolicyDTO> policyList = new ArrayList<>();
        List<EstimatePlanDTO> planList = new ArrayList<>();
        List<EstimateDutyDTO> dutyList = new ArrayList<>();
        List<PolicyClaimCaseDTO> policyClaimCaseList = new ArrayList<>();
        String idFlagHistoryChange = UuidUtil.getUUID();
        Map<String, BigDecimal> policyPaySumMap = new HashMap<>();
        Map<String, BigDecimal> policyFeeSumMap = new HashMap<>();
        List<EstimateChangeDTO> estimateChangeList = new ArrayList<>();
        for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyDTOList) {
            PolicyClaimCaseDTO policyClaimCaseDTO = new PolicyClaimCaseDTO();
            policyClaimCaseDTO.setCaseNo(estimatePolicyDTO.getCaseNo());
            policyClaimCaseDTO.setDepartmentCode(estimatePolicyDTO.getDepartmentCode());
            policyClaimCaseDTO.setDepartmentName(estimatePolicyDTO.getDepartmentName());
            policyClaimCaseDTO.setPartyNo("1");
            policyClaimCaseDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
            policyClaimCaseDTO.setReportNo(reportNo);
            policyClaimCaseDTO.setSubpolicyNo(estimatePolicyDTO.getSubpolicyNo());
            policyClaimCaseDTO.setInsuredCode(estimatePolicyDTO.getInsuredCode());
            policyClaimCaseDTO.setName(estimatePolicyDTO.getName());
            policyClaimCaseDTO.setCreatedBy(ConstValues.SYSTEM);
            policyClaimCaseDTO.setUpdatedBy(ConstValues.SYSTEM);
            policyClaimCaseList.add(policyClaimCaseDTO);
            String policyId = UuidUtil.getUUID();
            Date archiveTime = new Date();
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                String planId = UuidUtil.getUUID();
                String planCode = estimatePlanDTO.getPlanCode();
                List<EstimateDutyDTO> estimateDutyDTOList = estimatePlanDTO.getEstimateDutyList();
                for (EstimateDutyDTO estimateDutyDTO : estimateDutyDTOList) {
                    estimateDutyDTO.setIdAhcsEstimatePlan(planId);
                    estimateDutyDTO.setPlanCode(planCode);
                    estimateDutyDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
                    estimateDutyDTO.setCaseNo(estimatePolicyDTO.getCaseNo());
                    estimateDutyDTO.setCaseTimes(caseTimes);
                    estimateDutyDTO.setCreatedBy(ConstValues.SYSTEM);
                    estimateDutyDTO.setUpdatedBy(ConstValues.SYSTEM);
                    if (StringUtils.isEmptyStr(policyPlanDutyCode) ||
                            policyPlanDutyCode.equals(estimateDutyDTO.getPolicyNo()+estimateDutyDTO.getPlanCode()+estimateDutyDTO.getDutyCode())){
                        estimateDutyDTO.setEstimateAmount(estimateAmount);
                        estimatePlanDTO.setEstimateAmount(estimateAmount);
                        estimatePolicyDTO.setEstimateAmount(estimateAmount);
                    }
                    estimateDutyDTO.setDutyDetails(new ArrayList<>());
                    dutyList.add(estimateDutyDTO);

                    EstimateDutyRecordDTO estimateDutyRecordDTO = buildEsitmateDutyRecord(estimateDutyDTO,archiveTime);
                    estimateDutyRecordList.add(buildEsitmateDutyRecord(estimateDutyDTO,archiveTime));
                    EstimateUtil.calPolicyPayAndFeeSumAmount(policyPaySumMap, policyFeeSumMap, estimateDutyRecordDTO);
                }

                estimatePlanDTO.setCreatedBy(ConstValues.SYSTEM_UM);
                estimatePlanDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
                estimatePlanDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
                estimatePlanDTO.setCaseTimes(caseTimes);
                estimatePlanDTO.setCaseNo(estimatePolicyDTO.getCaseNo());
                estimatePlanDTO.setIdAhcsEstimatePlan(planId);
                estimatePlanDTO.setIdAhcsEstimatePolicy(policyId);
                estimatePlanDTO.setEstimateDutyList(new ArrayList<>());
                planList.add(estimatePlanDTO);
            }
            estimatePolicyDTO.setCreatedBy(ConstValues.SYSTEM_UM);
            estimatePolicyDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
            estimatePolicyDTO.setIdAhcsEstimatePolicy(policyId);
            estimatePolicyDTO.setEstimatePlanList(new ArrayList<>());
            policyList.add(estimatePolicyDTO);

            EstimateChangeDTO estimateChangeDTO = new EstimateChangeDTO();
            estimateChangeDTO.setReportNo(reportNo);
            estimateChangeDTO.setCaseTimes(estimatePolicyDTO.getCaseTimes());
            estimateChangeDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
            //首次为空
            estimateChangeDTO.setReason(null);
            estimateChangeDTO.setMaxPayAmount(null);
            estimateChangeDTO.setSumDutyPayAmount(policyPaySumMap.get(estimatePolicyDTO.getPolicyNo()));
            estimateChangeDTO.setSumDutyFeeAmount(policyFeeSumMap.get(estimatePolicyDTO.getPolicyNo()));
            BigDecimal changeAmount = policyFeeSumMap.get(estimatePolicyDTO.getPolicyNo())==null
                    ?BigDecimal.ZERO: policyFeeSumMap.get(estimatePolicyDTO.getPolicyNo());
            String uuid = UuidUtil.getUUID();
            estimateChangeDTO.setChangeAmount(policyPaySumMap.get(estimatePolicyDTO.getPolicyNo()).add(changeAmount));
            estimateChangeDTO.setIdclmsEstimateChange(uuid);
            estimateChangeDTO.setIdFlagHistoryChange(idFlagHistoryChange);
            estimateChangeDTO.setCreatedBy(ConstValues.SYSTEM_UM);
            estimateChangeDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
            estimateChangeDTO.setChangeDate(new Date());
            estimateChangeDTO.setUserId(ConstValues.SYSTEM_UM);
            estimateChangeList.add(estimateChangeDTO);
        }

        List<EstimateDutyHistoryDTO> historyDTOList = new ArrayList<>();
        estimateDutyRecordList.forEach(recordDTO->{
            String uuid = UuidUtil.getUUID();
            EstimateDutyHistoryDTO historyDTO = new EstimateDutyHistoryDTO();
            BeanUtils.copyProperties(recordDTO,historyDTO);
            historyDTO.setDutyEstimateAmount(recordDTO.getEstimateAmount());
            historyDTO.setEffectiveTime(recordDTO.getArchiveTime());
            //duty_record归档时间作为生效时间
            historyDTO.setIdAhcsEstimatDutyHistory(uuid);
            historyDTO.setIdFlagHistoryChange(idFlagHistoryChange);
            historyDTOList.add(historyDTO);
        });

        finishDTO.setPolicyClaimCaseList(policyClaimCaseList);
        finishDTO.setEstimateDutyRecordList(estimateDutyRecordList);
        finishDTO.setPolicyList(policyList);
        finishDTO.setPlanList(planList);
        finishDTO.setDutyList(dutyList);
        finishDTO.setEstimateDutyHistoryDTOList(historyDTOList);
        finishDTO.setEstimateChangeDTOList(estimateChangeList);

    }

    private void buildPolicyPay(String reportNo,Integer caseTimes,String policyPlanDutyDetailCode,BatchAutoFinishDTO finishDTO,BigDecimal payAmount) {
        List<PolicyPayDTO> policyList = policyPayMapper.selectFromPolicyCopy(reportNo, caseTimes);
        if(ListUtils.isEmptyList(policyList)){
            LogUtil.audit("理算保单为空reportNo={}",reportNo);
            throw new GlobalBusinessException("理算保单为空");
        }

        BatchDTO batch =new BatchDTO();
        batch.setIdAhcsBatch(UuidUtil.getUUID());
        batch.setReportNo(reportNo);
        batch.setCaseTimes(caseTimes);
        batch.setSettleStatus(SettleConst.SETTLE_STATUS_DONE);
        batch.setBatchSettleType(SettleConst.BATCH_SETTLE_TYPE_WHOLE_CASE);
        batch.setMigrateFrom(SettleConst.MIGRATE_FROM_NH);
        batch.setSettleTime(new Date());
        batch.setSettleUserUm(ConstValues.SYSTEM);
        batch.setCreatedBy(ConstValues.SYSTEM);
        batch.setUpdatedBy(ConstValues.SYSTEM);
        batch.setArchiveDate(new Date());
        finishDTO.setBatchDTO(batch);
        List<PolicyPayDTO> policyPayList = new ArrayList<>();
        List<PlanPayDTO> planPayList = new ArrayList<>();
        List<DutyPayDTO> dutyPayList = new ArrayList<>();
        List<DutyDetailPayDTO> detailPayList = new ArrayList<>();

        List<PolicyEnendorsementVO> policyEnendorsements = new ArrayList<>();
        for (PolicyPayDTO policy : policyList) {
            List<DutyEndorsementVO> dutyEndorsements = new ArrayList<>();
            policy.setCreatedBy(ConstValues.SYSTEM);
            policy.setUpdatedBy(ConstValues.SYSTEM);
            if(StringUtils.isEmptyStr(policy.getIdClmPolicyPay())){
                policy.setIdClmPolicyPay(UuidUtil.getUUID());
            }
            for (PlanPayDTO plan : policy.getPlanPayArr()) {
                plan.setCreatedBy(ConstValues.SYSTEM);
                plan.setUpdatedBy(ConstValues.SYSTEM);
                plan.setCaseNo(policy.getCaseNo());
                plan.setCaseTimes(1);
                plan.setIdAhcsBatch(batch.getIdAhcsBatch());
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    duty.setCreatedBy(ConstValues.SYSTEM);
                    duty.setUpdatedBy(ConstValues.SYSTEM);
                    duty.setCaseNo(policy.getCaseNo());
                    duty.setCaseTimes(1);
                    duty.setPlanCode(plan.getPlanCode());
                    duty.setIdAhcsBatch(batch.getIdAhcsBatch());
                    for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                        detail.setCreatedBy(ConstValues.SYSTEM);
                        detail.setUpdatedBy(ConstValues.SYSTEM);
                        detail.setCaseNo(policy.getCaseNo());
                        detail.setCaseTimes(1);
                        detail.setDutyCode(duty.getDutyCode());
                        detail.setPlanCode(plan.getPlanCode());
                        detail.setPolicyNo(policy.getPolicyNo());
                        detail.setIdAhcsBatch(batch.getIdAhcsBatch());
                        String key = policy.getPolicyNo()+plan.getPlanCode()+duty.getDutyCode()+detail.getDutyDetailCode();
                        LogUtil.audit("matchKey={}",key);
                        if( StringUtils.isEmptyStr(policyPlanDutyDetailCode) || key.equals(policyPlanDutyDetailCode)) {
                            detail.setAutoSettleAmount(payAmount);
                            duty.setSettleAmount(payAmount);
                            duty.setSettleReason("按照保险责任赔付" + payAmount + "元");
                            plan.setSettleAmount(payAmount);
                            policy.setPolicyPay(payAmount);
                        }
                        detailPayList.add(detail);
                    }

                    if(duty.getSettleAmount() != null && BigDecimalUtils.compareBigDecimalPlus(duty.getSettleAmount(),BigDecimal.ZERO)){
                        DutyEndorsementVO dutyEndorsement = new DutyEndorsementVO();
                        dutyEndorsement.setDutyAmount(duty.getSettleAmount());
                        dutyEndorsement.setDutyCode(duty.getDutyCode());
                        dutyEndorsement.setDutyName(duty.getDutyName());
                        dutyEndorsement.setAdjustmentTextArea(duty.getSettleReason());
                        dutyEndorsements.add(dutyEndorsement);
                    }
                    duty.setDutyDetailPayArr(new ArrayList<>());
                    dutyPayList.add(duty);

                }
                plan.setDutyPayArr(new ArrayList<>());
                planPayList.add(plan);
            }
            policy.setPolicySumPay(policy.getPolicyPay());
            policy.setSettleAmount(policy.getPolicyPay());
            if (policy.getPolicyPay() != null && BigDecimalUtils.compareBigDecimalPlus(policy.getPolicyPay(),BigDecimal.ZERO)){
                PolicyEnendorsementVO policyEnendorsement = new PolicyEnendorsementVO();
                policyEnendorsement.setPolicyNo(policy.getPolicyNo());
                policyEnendorsement.setDetailArr(dutyEndorsements);
                policyEnendorsements.add(policyEnendorsement);
            }
            policy.setPlanPayArr(new ArrayList<>());
            policyPayList.add(policy);
        }
        finishDTO.setPolicyPayList(policyPayList);
        finishDTO.setPlanPayList(planPayList);
        finishDTO.setDutyPayList(dutyPayList);
        finishDTO.setDetailPayList(detailPayList);
        EndorsementDTO endorsement = new EndorsementDTO();
        endorsement.setIdAhcsEndorsement(UuidUtil.getUUID());
        endorsement.setCreatedBy(ConstValues.SYSTEM);
        endorsement.setUpdatedBy(ConstValues.SYSTEM);
        endorsement.setCaseTimes(caseTimes);
        endorsement.setReportNo(reportNo);
        endorsement.setEndorsementRemark(JSON.toJSONString(policyEnendorsements));
        finishDTO.setEndorsement(endorsement);

    }

    private void buildPayment(String reportNo,Integer caseTimes,BatchAutoReportDTO reportDTO,BatchAutoFinishDTO finishDTO){
        BatchAutoCloseDTO dto = reportDTO.getBatchAutoCloseDTO();
        AhcsDomainDTO domainDTO = reportDTO.getAhcsDomainDTO();
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setCreatedBy(ConstValues.SYSTEM);
        paymentInfoDTO.setUpdatedBy(ConstValues.SYSTEM);
        paymentInfoDTO.setReportNo(reportNo);
        paymentInfoDTO.setCaseTimes(caseTimes);
        paymentInfoDTO.setIdClmPaymentInfo(UuidUtil.getUUID());
        paymentInfoDTO.setDataSource(Constants.DATA_SOURCE_DEFAULT);
        paymentInfoDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        paymentInfoDTO.setPaymentInfoStatus(Constants.PAYMENT_INFO_STATUS_0);
        paymentInfoDTO.setCollectPayApproach("02");
        paymentInfoDTO.setPaymentUsage(PaymentInfoTypeEnum.INDEMNITY.getType());
        paymentInfoDTO.setPaymentInfoType("00");
        paymentInfoDTO.setBankAccountAttribute("1");
        paymentInfoDTO.setProvinceName(dto.getProvinceCode());
        paymentInfoDTO.setClientType("01");
        paymentInfoDTO.setCityName(dto.getCityCode());
        paymentInfoDTO.setRegionCode(dto.getRegionCode());
        paymentInfoDTO.setClientBankCode(dto.getClientBankCode());
        paymentInfoDTO.setClientBankName(dto.getClientBankName());
        paymentInfoDTO.setClientBankAccount(dto.getClientBankAccount());
        paymentInfoDTO.setClientName(dto.getClientName());
        paymentInfoDTO.setClientCertificateNo(dto.getClientCertificateNo());
        finishDTO.setPaymentInfo(paymentInfoDTO);

        PaymentItemDTO itemDTO = new PaymentItemDTO();
        BeanUtils.copyProperties(paymentInfoDTO,itemDTO);
        itemDTO.setCreatedBy(ConstValues.SYSTEM);
        itemDTO.setUpdatedBy(ConstValues.SYSTEM);
        itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
        itemDTO.setPolicyNo(dto.getPolicyNo());
        itemDTO.setCaseNo(domainDTO.getCaseBases().get(0).getCaseNo());
        itemDTO.setReportNo(reportNo);
        itemDTO.setCaseTimes(caseTimes);
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
        itemDTO.setIdClmPaymentInfo(paymentInfoDTO.getIdClmPaymentInfo());
        itemDTO.setCollectPaySign(SettleConst.COLLECTION);
        itemDTO.setPaymentAmount(dto.getPayAmount());
        itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
        itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
        itemDTO.setMergeSign(SettleConst.NOT_MERGE);
        itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
        itemDTO.setIsCoinsure(ConstValues.NO);
        itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        itemDTO.setCompensateNo(commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,domainDTO.getAcceptDepartmentCode()));
        List<PaymentItemDTO> itemDTOList = new ArrayList<>();
        itemDTOList.add(itemDTO);
        finishDTO.setPaymentitemList(itemDTOList);
    }

    /**
      *
      * @Description 退运险的 赔付 信息表
      * <AUTHOR>
      * @Date 2023/7/20 21:14
      **/
    private void buildPaymentOnline(String reportNo,Integer caseTimes,OnlineBatchAutoReportDTO reportDTO,BatchAutoFinishDTO finishDTO){
        OnlinePaymentInfo paymentInfo = reportDTO.getOnlineBatchAutoClose().getPaymentInfo();
        AhcsDomainDTO domainDTO = reportDTO.getAhcsDomainDTO();
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        BeanUtils.copyProperties(paymentInfo,paymentInfoDTO);
        paymentInfoDTO.setCreatedBy(ConstValues.SYSTEM);
        paymentInfoDTO.setUpdatedBy(ConstValues.SYSTEM);
        paymentInfoDTO.setReportNo(reportNo);
        paymentInfoDTO.setCaseTimes(caseTimes);
        paymentInfoDTO.setIdClmPaymentInfo(UuidUtil.getUUID());
        paymentInfoDTO.setDataSource(Constants.DATA_SOURCE_DEFAULT);
        paymentInfoDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        paymentInfoDTO.setPaymentInfoStatus(Constants.PAYMENT_INFO_STATUS_0);
//        paymentInfoDTO.setCollectPayApproach("02");
        paymentInfoDTO.setPaymentUsage(PaymentInfoTypeEnum.INDEMNITY.getType());
        paymentInfoDTO.setPaymentInfoType("00");
//        paymentInfoDTO.setBankAccountAttribute("0");
        paymentInfoDTO.setClientBankCode(StringUtils.cancelNull(paymentInfoDTO.getClientBankCode()));
        paymentInfoDTO.setClientBankName(StringUtils.cancelNull(paymentInfoDTO.getClientBankName()));
        paymentInfoDTO.setClientBankAccount(StringUtils.cancelNull(paymentInfoDTO.getClientBankAccount()));
        try {
            paymentInfoService.getCustomerNo(paymentInfoDTO);
        } catch (Exception e) {
            LogUtil.info("退运险生成客户号异常");
        }
        finishDTO.setPaymentInfo(paymentInfoDTO);

        PaymentItemDTO itemDTO = new PaymentItemDTO();
        BeanUtils.copyProperties(paymentInfoDTO,itemDTO);
        itemDTO.setCreatedBy(ConstValues.SYSTEM);
        itemDTO.setUpdatedBy(ConstValues.SYSTEM);
        // 因为这个唯一键需要返回给渠道，所以预先生成
        itemDTO.setIdClmPaymentItem(paymentInfo.getPaySerialNo());
        itemDTO.setPolicyNo(reportDTO.getOnlineBatchAutoClose().getPolicyNo());
        itemDTO.setCaseNo(domainDTO.getCaseBases().get(0).getCaseNo());
        itemDTO.setReportNo(reportNo);
        itemDTO.setCaseTimes(caseTimes);
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
        itemDTO.setIdClmPaymentInfo(paymentInfoDTO.getIdClmPaymentInfo());
        itemDTO.setCollectPaySign(SettleConst.COLLECTION);
        itemDTO.setPaymentAmount(paymentInfo.getPayAmount());
        itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
        itemDTO.setMergeSign(SettleConst.NOT_MERGE);
        itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
        itemDTO.setIsCoinsure(ConstValues.NO);
        itemDTO.setCompensateNo(commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,domainDTO.getAcceptDepartmentCode()));
        List<PaymentItemDTO> itemDTOList = new ArrayList<>();
        itemDTOList.add(itemDTO);
        finishDTO.setPaymentitemList(itemDTOList);
    }

    private void buildWholeCase(String reportNo,Integer caseTimes,BatchAutoFinishDTO finishDTO, boolean isZeroCancel){
        WholeCaseBaseDTO wholeCaseBaseDTO = new WholeCaseBaseDTO();
        wholeCaseBaseDTO.setReportNo(reportNo);
        wholeCaseBaseDTO.setCaseTimes(caseTimes);
        wholeCaseBaseDTO.setUpdatedBy(ConstValues.SYSTEM);
        wholeCaseBaseDTO.setCaseFinisherUm(ConstValues.SYSTEM);
        wholeCaseBaseDTO.setWholeCaseStatus(EndCaseConstValues.WHOLE_CASE_END_STATUS);
        Date endCaseDate = new Date();
        //未决record和History表记录是当前时间，本次结案时间在当前时间基础上加20s
        wholeCaseBaseDTO.setEndCaseDate(DateUtil.offsetSecond(endCaseDate,20));
        wholeCaseBaseDTO.setIndemnityConclusion(isZeroCancel?ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED : SettleConst.INDEMNITY_MODE_PAY);
        String registNo =commonService.generateNoOnline( NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO, finishDTO.getCaseProcessDTO().getCompanyCode());
        String endCaseNo =commonService.generateNoOnline( NoConstants.END_CASE_NO, VoucherTypeEnum.END_CASE_NO, finishDTO.getCaseProcessDTO().getCompanyCode());
        wholeCaseBaseDTO.setRegistNo(registNo);
        wholeCaseBaseDTO.setEndCaseNo(endCaseNo);
        finishDTO.setWholeCaseBase(wholeCaseBaseDTO);
    }
    
    private EstimateDutyRecordDTO buildEsitmateDutyRecord(EstimateDutyDTO duty, Date archiveDate){
        EstimateDutyRecordDTO recordDTO = new EstimateDutyRecordDTO();
        recordDTO.setCreatedBy(duty.getCreatedBy());
        recordDTO.setUpdatedBy(duty.getCreatedBy());
        recordDTO.setIdAhcsEstimateDutyRecord(duty.getIdAhcsEstimateDuty());
        recordDTO.setTaskId(TacheConstants.ESTIMATE);
        recordDTO.setPolicyNo(duty.getPolicyNo());
        recordDTO.setCaseNo(duty.getCaseNo());
        recordDTO.setCaseTimes(duty.getCaseTimes());
        recordDTO.setPlanCode(duty.getPlanCode());
        recordDTO.setDutyCode(duty.getDutyCode());
        recordDTO.setDutyName(duty.getDutyName());
        recordDTO.setBaseAmountPay(duty.getBaseAmountPay());
        recordDTO.setEstimateAmount(duty.getEstimateAmount());
        recordDTO.setEstimateType(EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
        recordDTO.setChgPayValue(duty.getEstimateAmount());
        recordDTO.setArchiveTime(archiveDate);
        return recordDTO;
    }

    private void buildChannelProcess(String reportNo,Integer caseTimes,BatchAutoFinishDTO finishDTO){
        String channelProcessId = UuidUtil.getUUID();
        ChannelProcessDTO channelProcess = new ChannelProcessDTO();
        channelProcess.setCreatedBy(ConstValues.SYSTEM);
        channelProcess.setUpdatedBy(ConstValues.SYSTEM);
        channelProcess.setIdAhcsChannelProcess(channelProcessId);
        channelProcess.setReportNo(reportNo);
        channelProcess.setCaseTimes(caseTimes);
        channelProcess.setChannelType(BaseConstant.STRING_1);
        channelProcess.setAssessUm(ConstValues.SYSTEM);
        channelProcess.setLossObjectNo(TimeMillisSequence.getTimeMillisSequence());
        finishDTO.setChannelProcess(channelProcess);
    }

    private void buildPersonAccident(String reportNo,Integer caseTimes,BatchAutoCloseDTO dto,BatchAutoFinishDTO finishDTO){
        PersonAccidentDTO personAccidentDTO = new PersonAccidentDTO();
        personAccidentDTO.setReportNo(reportNo);
        personAccidentDTO.setCaseTimes(caseTimes);
        personAccidentDTO.setCreatedBy(ConstValues.SYSTEM);
        personAccidentDTO.setUpdatedBy(ConstValues.SYSTEM);
        personAccidentDTO.setIdAhcsChannelProcess(finishDTO.getChannelProcess().getIdAhcsChannelProcess());
        personAccidentDTO.setTaskId(BpmConstants.CHECK_DUTY);
        personAccidentDTO.setOverseasOccur(BaseConstant.STRING_0);
        personAccidentDTO.setAccidentCityCode(dto.getAccidentCity());
        personAccidentDTO.setInsuredApplyStatus(InsuredApplyStatusEnum.END_OF_TREATMENT.getType());
        personAccidentDTO.setAccidentType(dto.getAccidentType());
        personAccidentDTO.setProvinceCode(dto.getAccidentProvince());
        personAccidentDTO.setAccidentCountyCode(dto.getAccidentCounty());
        personAccidentDTO.setAccidentPlace("");
        personAccidentDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
        personAccidentDTO.setIsHugeAccident(ConstValues.NO);
        personAccidentDTO.setAccidentTime(dto.getAccidentDate());
        personAccidentDTO.setPersonAccidentId(UuidUtil.getUUID());
        personAccidentDTO.setInjuryMechanism("IM_2912");
        finishDTO.setPersonAccident(personAccidentDTO);
    }

    private void buildPersonDiagnose(String reportNo,Integer caseTimes,BatchAutoCloseDTO dto,BatchAutoFinishDTO finishDTO){
        PersonDiagnoseDTO personDiagnoseDTO = new PersonDiagnoseDTO();
        personDiagnoseDTO.setIdAhcsChannelProcess(finishDTO.getChannelProcess().getIdAhcsChannelProcess());
        personDiagnoseDTO.setTaskId(BpmConstants.CHECK_DUTY);
        personDiagnoseDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
        personDiagnoseDTO.setReportNo(reportNo);
        personDiagnoseDTO.setCaseTimes(caseTimes);
        personDiagnoseDTO.setCreatedBy(ConstValues.SYSTEM);
        personDiagnoseDTO.setUpdatedBy(ConstValues.SYSTEM);
        personDiagnoseDTO.setDiagnoseCode(dto.getDiagnoseCode());
        personDiagnoseDTO.setIsSurgical(ConstValues.NO);
        personDiagnoseDTO.setDiagnosticTypologyCode(DiagnosticTypologyEnum.DiagnosticTypology05.getType());
        finishDTO.setPersonDiagnose(personDiagnoseDTO);

    }

    private void buildPersonHospital(String reportNo,Integer caseTimes,BatchAutoCloseDTO dto,BatchAutoFinishDTO finishDTO){
        List<PersonHospitalDTO> hospitalList = new ArrayList<>();
        PersonHospitalDTO hospital = new PersonHospitalDTO();
        hospital.setCreatedBy(ConstValues.SYSTEM);
        hospital.setUpdatedBy(ConstValues.SYSTEM);
        hospital.setIdAhcsPersonHospital(UuidUtil.getUUID());
        hospital.setReportNo(reportNo);
        hospital.setCaseTimes(caseTimes);
        hospital.setIdAhcsChannelProcess(finishDTO.getChannelProcess().getIdAhcsChannelProcess());
        hospital.setTaskId(BpmConstants.CHECK_DUTY);
        hospital.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
        hospital.setTherapyType(ChecklossConst.AHCS_THERAPY_ONE);
        hospital.setMedicalStatus(TreatCondition.TREATMEN_ED.getType());
        if(StringUtils.isNotEmpty(dto.getHospital())){
            //根据报案号查询机构编码
            //根据机构编码确认机构是全国、北京(211)还是上海(231)
            String orgType = hospitalInfoService.getOrgTypeByReportNo(reportNo, NcbsConstant.HOSPITAL);
            HospitalInfoVO hosVo = hospitalInfoMapper.getHospitalCodeByName(dto.getHospital(),orgType);
            if(hosVo != null && StringUtils.isNotEmpty(hosVo.getHospitalCode())){
                hospital.setHospitalCode(hosVo.getHospitalCode());
                hospital.setHospitalName(dto.getHospital());
            }
        }else{
            hospital.setHospitalCode("9999999");
            hospital.setHospitalName("其他医院");
        }

        hospitalList.add(hospital);
        finishDTO.setPersonHospitalList(hospitalList);
    }
}
