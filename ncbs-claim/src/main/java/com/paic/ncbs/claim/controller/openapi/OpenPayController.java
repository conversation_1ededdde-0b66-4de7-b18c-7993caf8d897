package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.openapi.FeeInvoiceBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.MergePaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentDTO;
import com.paic.ncbs.claim.service.openapi.OpenPayService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/public/pay")
public class OpenPayController extends BaseController {

    @Autowired
    private OpenPayService openPayService;

    /**
     * 收付费回调支付结果
     * @param dtos
     * @return
     */
    @ApiOperation("接收支付结果")
    @PostMapping(value = "/paymentBackResult")
    public ResponseResult<Object> handlePayBack(@RequestBody List<PaymentBackResultDTO> dtos) {
        openPayService.handlePayBack(dtos);
        return ResponseResult.success();
    }

    @ApiOperation("支付信息修改")
    @PostMapping(value = "/updatePayment")
    public ResponseResult<Object> updatePayment(@RequestBody @Validated PaymentDTO dto) {
        openPayService.updatePayment(dto);
        return ResponseResult.success();
    }

    @ApiOperation("进项税费用发票退回")
    @PostMapping(value = "/feeInvoiceBackResult")
    public ResponseResult<Object> feeInvoiceBackResult(@RequestBody @Validated FeeInvoiceBackResultDTO dto) {
        openPayService.feeInvoiceBackResult(dto);
        return ResponseResult.success();
    }

    @ApiOperation("接收退运险合并支付结果")
    @PostMapping(value = "/mergePaymentBackResult")
    public ResponseResult<Object> mergePaymentBackResult(@RequestBody MergePaymentBackResultDTO dto) {
        openPayService.mergePaymentBackResult(dto);
        return ResponseResult.success();
    }
}
