package com.paic.ncbs.claim.service.taskdeal;

import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface TaskInfoService {

    void addTaskInfo(TaskInfoDTO taskInfoDTO);

    void modifyTaskInfo(TaskInfoDTO taskInfoDTO);

    void modifyTaskInfoAssigner(TaskInfoDTO taskInfoDTO);
    
    void updateTaskAssigner(TaskInfoDTO taskInfoDTO);

    String getNoFinishScoreTask(String reportNo, Integer caseTimes);

    String getMajoyProcessDepartmentByReport(String reportNo, Integer caseTimes);

     TaskInfoDTO findLatestByReportNoAndBpmKey(@Param("reportNo") String reportNo,
                                                     @Param("caseTimes") Integer caseTimes,
                                                     @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    void modifyTaskInfoByDefKey(TaskInfoDTO taskInfoDTO);

    void suspendOrActiveTask(TaskInfoDTO taskInfoDTO);

    String getConclusion(String reportNo, Integer caseTimes);

    TaskInfoDTO getTaskInfoForInvestigate(String reportNo, Integer caseTimes);

    boolean hasNotFinishTaskByTaskKey(String reportNo, Integer caseTimes,String taskKey,String taskId);

    TaskInfoDTO ownNewSuspendProcess(String reportNo, Integer caseTimes, List<String> caseProcess,String status);

    int getSuspendTaskCount(TaskInfoDTO taskInfoDTO);

    void suspendNotUpdateDate(TaskInfoDTO dto);

    TaskInfoDTO getTaskDtoByTaskId(String taskId);

    /**
     * 保费是否交齐,false=交齐
     * @param reportNo
     * @return
     */
    boolean checkPolicyActualPremium(String reportNo);

    TaskInfoDTO checkWorkflow(String reportNo, int caseTimes, String bpmConstants, String status);

    String getTaskId(String reportNo, Integer caseTimes,String taskDefinitionBpmKey);

    void deleteTaskInfo(TaskInfoDTO taskInfoDTO);

    String getLatestTaskId(String reportNo, Integer caseTimes,String taskKey);

    TaskInfoDTO getTaskAssignerName(String reportNo, Integer caseTimes, String taskKey);
}
