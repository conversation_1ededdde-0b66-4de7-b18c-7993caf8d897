package com.paic.ncbs.claim.dao.mapper.riskppt;

import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.PlyRiskGroupQueryDTO;
import com.paic.ncbs.claim.model.dto.riskppt.ReportRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyQueryDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface RiskPropertyMapper {

    List<CaseRiskPropertyDTO> getPlyRiskPropertyList(PlyRiskGroupQueryDTO queryDTO);

    String getCaseRiskPropertyLastTaskId(CaseRiskPropertyDTO caseDTO);

    List<CaseRiskPropertyDTO> getCaseRiskPropertyList(CaseRiskPropertyDTO caseDTO);

    RiskPropertyQueryDTO getRiskPropertyListByReportNo(@Param("reportNo")String reportNo, @Param("caseTimes")int caseTimes);

    void saveCaseRiskPropertyList(@Param("caseRiskPropertyList") List<CaseRiskPropertyDTO> caseRiskPropertyList);

    void removeCaseRiskProperty(CaseRiskPropertyDTO caseDTO);

    List<CaseRiskPropertyDTO> getReportRiskPropertyList(CaseRiskPropertyDTO caseDTO);

    void saveReportRiskPropertyList(@Param("reportRiskPropertyList") List<ReportRiskPropertyDTO> reportRiskPropertyList);

    void removeReportRiskProperty(CaseRiskPropertyDTO caseDTO);

    List<CaseRiskPropertyDTO> getLastTaskIdCaseRiskPropertyList(CaseRiskPropertyDTO caseDTO);
    
    void deleteCaseRiskProperty(@Param("reportNo")String reportNo, @Param("caseTimes")int caseTimes, @Param("taskId")String taskId);

    String getPlyRiskGroupType(@Param("policyNo")String policyNo, @Param("riskGroupNo")String riskGroupNo);

    void updateReportRiskPropertyTaskId(CaseRiskPropertyDTO caseDTO);

}