package com.paic.ncbs.claim.service.pet.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.PetTreatmentItemEnum;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity;
import com.paic.ncbs.claim.dao.mapper.pet.PetInjureExMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetInjureMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetTreatmentDetailMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pet.PetInjureDTO;
import com.paic.ncbs.claim.model.dto.pet.PetInjureExDTO;
import com.paic.ncbs.claim.model.dto.pet.PetTreatmentDetailDTO;
import com.paic.ncbs.claim.model.dto.settle.PetDetailDTO;
import com.paic.ncbs.claim.model.vo.pet.PetInjureExVO;
import com.paic.ncbs.claim.model.vo.pet.PetInjureVO;
import com.paic.ncbs.claim.model.vo.pet.PetTreatmentDetailVO;
import com.paic.ncbs.claim.model.vo.pet.TypeAndAmountVO;
import com.paic.ncbs.claim.service.pet.PetInjureService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service("petInjureService")
public class PetInjureServiceImpl implements PetInjureService {
    @Autowired
    private PetInjureMapper petInjureMapper;
    @Autowired
    private PetInjureExMapper petInjureExMapper;
    @Autowired
    private PetTreatmentDetailMapper petTreatmentDetailMapper;
    @Autowired
    private ReportAccidentExService reportAccidentExService;

    private static List<String> PET_TASK_TEMP_LIST = new ArrayList<>();
    static {
        PET_TASK_TEMP_LIST.add(BpmConstants.REPORT_TRACK);
        PET_TASK_TEMP_LIST.add(BpmConstants.CHECK_DUTY);
    }
    private static List<String> PET_TASK_SUBMIT_LIST = new ArrayList<>();
    static {
        PET_TASK_SUBMIT_LIST.add("report1");
        PET_TASK_SUBMIT_LIST.addAll(PET_TASK_TEMP_LIST);
    }

    @Override
    @Transactional
    public void addPetInjure(PetInjureVO petInjureVO) {
        String taskCode = petInjureVO.getTaskCode();
        if(!PET_TASK_SUBMIT_LIST.contains(taskCode)){
            LogUtil.audit("宠物险环节号异常"+taskCode);
            throw new GlobalBusinessException("宠物险环节号异常");
        }

        String userId = petInjureVO.getUserId();
        if(StringUtils.isEmptyStr(userId)){
            userId = WebServletContext.getUserId();
        }
        String petInjureId = petInjureVO.getIdClmsPetInjure();
        if(StringUtils.isEmptyStr(petInjureId)){
            PetInjureDTO queryPetInjure = new PetInjureDTO();
            queryPetInjure.setReportNo(petInjureVO.getReportNo());
            queryPetInjure.setCaseTimes(petInjureVO.getCaseTimes());
            queryPetInjure.setTaskCode(taskCode);
            queryPetInjure.setStatus(BaseConstant.STRING_1);
            PetInjureDTO petInjureDTO = petInjureMapper.getPetInjure(queryPetInjure);
//            Optional.ofNullable(petInjureDTO).orElse(new PetInjureDTO()).getIdClmsPetInjure();
            petInjureId = Optional.ofNullable(petInjureDTO).map(PetInjureDTO::getIdClmsPetInjure).orElse(null);
        }

        //前端传参有IdClmsPetInjure,或者根据案件号查询本环节下有数据,则需要删除
        final boolean deleteFlag = StringUtils.isNotEmpty(petInjureId);

        petInjureId = UuidUtil.getUUID();
        PetInjureDTO addPetInjureDTO = new PetInjureDTO();
        BeanUtils.copyProperties(petInjureVO,addPetInjureDTO);
        addPetInjureDTO.setCreatedBy(userId);
        addPetInjureDTO.setIdClmsPetInjure(petInjureId);
        addPetInjureDTO.setTypeAndAmount(JSON.toJSONString(petInjureVO.getTypeAndAmountList()));

        List<PetInjureExVO> petInjureExVOList = petInjureVO.getPetInjureExList();
        List<PetInjureExDTO> addPetInjureExList = new ArrayList<>();
        if(ListUtils.isNotEmpty(petInjureExVOList)){
            for (PetInjureExVO vo : petInjureExVOList) {
                PetInjureExDTO addPetInjureExDTO = new PetInjureExDTO();
                BeanUtils.copyProperties(vo,addPetInjureExDTO);
                addPetInjureExDTO.setCreatedBy(userId);
                addPetInjureExDTO.setIdClmsPetInjure(petInjureId);
                addPetInjureExDTO.setIdClmsPetInjureEx(UuidUtil.getUUID());
                addPetInjureExList.add(addPetInjureExDTO);
            }
        }

        List<PetTreatmentDetailVO> petTreatmentDetailVOList = petInjureVO.getPetTreatmentDetailList();
        List<PetTreatmentDetailDTO> addPetTreatmentList = new ArrayList<>();
        if(ListUtils.isNotEmpty(petTreatmentDetailVOList)){
            for (PetTreatmentDetailVO vo : petTreatmentDetailVOList) {
                if(vo.getUnitPrice() == null || vo.getQuantity() == null){
                    throw new GlobalBusinessException("单价及数量不能为空");
                }
                PetTreatmentDetailDTO addPetTreatmentDTO = new PetTreatmentDetailDTO();
                BeanUtils.copyProperties(vo,addPetTreatmentDTO);
                addPetTreatmentDTO.setCreatedBy(userId);
                addPetTreatmentDTO.setIdClmsPetInjure(petInjureId);
                addPetTreatmentDTO.setIdClmsPetTreatmentDetail(UuidUtil.getUUID());
                addPetTreatmentDTO.setTotalPrice(vo.getUnitPrice().multiply(vo.getQuantity()));
                addPetTreatmentList.add(addPetTreatmentDTO);
            }
        }

        if(deleteFlag) {
            //删除
            petInjureMapper.delPetInjureById(petInjureId, userId);
            petInjureExMapper.delPetInjureExList(petInjureId, userId);
            petTreatmentDetailMapper.delPetTreatmentDetailList(petInjureId, userId);
        }

        //新增
        petInjureMapper.addPetInjure(addPetInjureDTO);
        if(addPetInjureExList.size() > 0){
            petInjureExMapper.addPetInjureExList(addPetInjureExList);
        }
        if(addPetTreatmentList.size() > 0){
            petTreatmentDetailMapper.addPetTreatmentDetailList(addPetTreatmentList);
        }
    }

    @Override
    public PetInjureVO getPetInjure(PetInjureVO petInjureVO) {
        String reportNo = petInjureVO.getReportNo();
        Integer caseTimes = petInjureVO.getCaseTimes();
        String taskCode = petInjureVO.getTaskCode();
        PetInjureDTO queryPetInjure = new PetInjureDTO();
        queryPetInjure.setReportNo(reportNo);
        queryPetInjure.setCaseTimes(caseTimes);
        queryPetInjure.setTaskCode(taskCode);
        PetInjureDTO petInjureDTO = null;
        if(PET_TASK_TEMP_LIST.contains(taskCode)){
            queryPetInjure.setStatus(BaseConstant.STRING_0);
            queryPetInjure.setTaskCode(taskCode);
            petInjureDTO = petInjureMapper.getPetInjure(queryPetInjure);
        }
        if(petInjureDTO == null){
            queryPetInjure.setStatus(BaseConstant.STRING_1);
            queryPetInjure.setTaskCode(null);
            petInjureDTO = petInjureMapper.getPetInjure(queryPetInjure);
        }
        if(petInjureDTO == null || petInjureDTO.getIdClmsPetInjure() == null){
            return null;
        }

        List<PetInjureExDTO> petInjureExDTOList = petInjureExMapper.getPetInjureExList(petInjureDTO.getIdClmsPetInjure());
        if(ListUtils.isNotEmpty(petInjureExDTOList)){
            List<PetInjureExVO> petInjureExVOList = new ArrayList<>();
            PetInjureExVO vo = new PetInjureExVO();
            for (PetInjureExDTO dto : petInjureExDTOList) {
                BeanUtils.copyProperties(dto,vo);
                petInjureExVOList.add(vo);
            }
            petInjureVO.setPetInjureExList(petInjureExVOList);
        }else{
            petInjureVO.setPetInjureExList(new ArrayList<>());
        }

        List<PetTreatmentDetailDTO> petTreatmentDTOListList = petTreatmentDetailMapper.getPetTreatmentDetailList(petInjureDTO.getIdClmsPetInjure());
        if(ListUtils.isNotEmpty(petTreatmentDTOListList)){
            List<PetTreatmentDetailVO> petTreatmentVOList = new ArrayList<>();
            PetTreatmentDetailVO vo = new PetTreatmentDetailVO();
            for (PetTreatmentDetailDTO dto : petTreatmentDTOListList) {
                BeanUtils.copyProperties(dto,vo);
                vo.setTreatmentItemName(PetTreatmentItemEnum.getName(vo.getTreatmentItem()));
                petTreatmentVOList.add(vo);
                sumAmount(petInjureVO,vo);
            }
            petInjureVO.setPetTreatmentDetailList(petTreatmentVOList);
        }
        BeanUtils.copyProperties(petInjureDTO,petInjureVO);
        if(StringUtils.isNotEmpty(petInjureDTO.getTypeAndAmount())){
            petInjureVO.setTypeAndAmountList(JSON.parseArray(petInjureDTO.getTypeAndAmount(), TypeAndAmountVO.class));
        }

        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        String extend = Optional.ofNullable(reportAccidentEx).map(ReportAccidentExEntity::getAccidentExtendInfo).orElse(null);
        if(extend != null){
            petInjureVO.setPetDetail(JSON.parseObject(extend, PetDetailDTO.class));
        }
        return petInjureVO;
    }

    private void sumAmount(PetInjureVO petInjureVO,PetTreatmentDetailVO vo){
        BigDecimal sumQuantity = Optional.ofNullable(petInjureVO.getSumQuantity()).orElse(BigDecimal.ZERO);
        BigDecimal sumUnitPrice = Optional.ofNullable(petInjureVO.getSumUnitPrice()).orElse(BigDecimal.ZERO);
        BigDecimal sumTotalPrice = Optional.ofNullable(petInjureVO.getSumTotalPrice()).orElse(BigDecimal.ZERO);
        BigDecimal sumCustomerAmount = Optional.ofNullable(petInjureVO.getSumCustomerAmount()).orElse(BigDecimal.ZERO);
        BigDecimal sumPayAmount = Optional.ofNullable(petInjureVO.getSumPayAmount()).orElse(BigDecimal.ZERO);
        petInjureVO.setSumQuantity(sumQuantity.add(Optional.ofNullable(vo.getQuantity()).orElse(BigDecimal.ZERO)));
        petInjureVO.setSumUnitPrice(sumUnitPrice.add(Optional.ofNullable(vo.getUnitPrice()).orElse(BigDecimal.ZERO)));
        petInjureVO.setSumTotalPrice(sumTotalPrice.add(Optional.ofNullable(vo.getTotalPrice()).orElse(BigDecimal.ZERO)));
        petInjureVO.setSumCustomerAmount(sumCustomerAmount.add(Optional.ofNullable(vo.getCustomerAmount()).orElse(BigDecimal.ZERO)));
        petInjureVO.setSumPayAmount(sumPayAmount.add(Optional.ofNullable(vo.getPayAmount()).orElse(BigDecimal.ZERO)));
    }


}
