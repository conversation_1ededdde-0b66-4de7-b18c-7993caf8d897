package com.paic.ncbs.claim.model.dto.fee;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("发票信息")
public class InvoiceInfoDTO extends EntityDTO {
	
	private static final long serialVersionUID = 4130688646265835155L;

	private String idAhcsInvoiceInfo;
    @ApiModelProperty("理赔费用表主键")
	private String idAhcsFeePay;
	@ApiModelProperty("发票代码")
	private String invoiceCode;
	@ApiModelProperty("发票号码")
	private String invoiceNo;
	@ApiModelProperty("费用发票类型 01-增值税专用发票 02-普通发票")
	private String invoiceType;
	@ApiModelProperty("发票日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
	private Date invoiceDate;
	@ApiModelProperty("购买方")
	private String buyCompany;
	@ApiModelProperty("购买方纳税识别号")
	private String taxbuyerNo;
	@ApiModelProperty("销货单位")
	private String sellCompany;
	@ApiModelProperty("纳税识别号")
	private String sellTaxpayerNo;
	@ApiModelProperty("不计税金额")
	private BigDecimal noTaxAmount;
	@ApiModelProperty("税额")
	private BigDecimal taxAmount;
	@ApiModelProperty("价税合计")
	private BigDecimal totalAmount;
	@ApiModelProperty("税率")
	private Integer taxRate;
	@ApiModelProperty("文件id")
	private String fileId;
	@ApiModelProperty("文件名")
	private String fileName;
	@ApiModelProperty("文件链接")
	private String fileUrl;
	@ApiModelProperty("文件格式")
	private String documentFormat;

	/**
	 * 费用发票修改标记 0:未修改 1:待修改 2:修改完成 3:临时
	 */
	@ApiModelProperty("费用发票修改标记")
	private String isModifiedFlag;
	@ApiModelProperty("退回原因")
	private String returnReason;
	@ApiModelProperty("退回类型,默认:发票退回")
	private String returnType;
	@ApiModelProperty("退回日期")
	private String returnDate;




}
