package com.paic.ncbs.claim.model.dto.problem;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description =  "问题件答复入参")
public class ProblemCaseRequestDTO {
    /**
     * 样例：
     * {
     *     "requestType": "CTA-002",
     *     "requestId": "7fa407461b2f4f8a9325a36c88980974",
     *     "requestTime": "1702605997473",
     *     "companyId": "42300010",
     *     "requestData": {
     *         "registNo": "2023728",
     *         "agrmNo": "",
     *         "claimNo": "",
     *         "comCode": "",
     *         "prdCode": "",
     *         "objectCode": "",
     *         "problemNo": "2023000728001",
     *         "problemType": "01",
     *         "caseConclusion": "2",
     *         "caseConclusionDetail": "",
     *         "replyTime": "20231215100559",
     *         "remark": "已核销"
     *     }
     * }
     *
     *
     */


    private String requestType = "CTA-002";

    private String requestId = UUID.randomUUID().toString();

    private String requestTime = String.valueOf(System.currentTimeMillis());

    private String companyId;

    private RequestData requestData;

}
