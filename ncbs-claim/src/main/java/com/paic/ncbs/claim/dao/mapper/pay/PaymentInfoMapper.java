package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.model.dto.pay.PaymentInfo;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PaymentInfoMapper {

    void addPaymentInfo(PaymentInfoDTO paymentInfoDTO);

    List<PaymentInfo> getPaymentInfoByReportNo(@Param("reportNo") String reportNo);

    List<PaymentInfoDTO> getPaymentInfo(PaymentInfoDTO paymentInfoDTO);

    PaymentInfoDTO getPaymentInfoById(String idClmPaymentInfo);

    void updatePaymentInfo(PaymentInfoDTO paymentInfoDTO);

    /**
     * 根据id更新状态为作废
     * @param paymentInfoDTO
     */
    void updateById(PaymentInfoDTO paymentInfoDTO);

    Integer getPaymentInfoForOnly(PaymentInfoDTO paymentInfoDTO);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    Integer getPaymentInfoForComOnly(PaymentInfoDTO paymentInfoDTO);

    /**
     * 查询没有客户号的数据进行补充
     * @return
     */
    List<PaymentInfoDTO> queryCustomerNoToSupplementedFromInfo();

    /**
     * 批量更新
     * @param paymentInfoList
     */
    void batchUpdate(@Param("paymentInfoList") List<PaymentInfoDTO> paymentInfoList);

    /**
     * 根据姓名，证件类型，证件号查询公司银行账号信息
     * @param paymentInfoVO
     * @return
     */
    PaymentInfoDTO getCompanyPaymentInfoByNameAndTypeAndCardNo(PaymentInfoVO paymentInfoVO);

    PaymentInfoDTO getIdvPaymentInfoByNameAndTypeAndCardNo(PaymentInfoVO paymentInfoVO);

    /**
     * 查询有效的被保人信息
     * @return
     */
    Integer getPaymentEffInfoByReportNo(String reportNo);
}
