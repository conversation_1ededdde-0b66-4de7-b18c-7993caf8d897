package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializeWriter;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsPersonalInjuryDeathInfo;
import com.paic.ncbs.claim.dao.entity.duty.ReportDutyDetailVo;
import com.paic.ncbs.claim.dao.entity.duty.ReportDutyVo;
import com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyDutyDetailMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyDutyMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.pet.ReportAccidentPetMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.dto.report.ReportInfoDTO;
import com.paic.ncbs.claim.model.dto.report.ReportStatDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.ReportRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO;
import com.paic.ncbs.claim.model.vo.report.ReportQueryVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.ahcs.AhcsSavePolicyinfoService;
import com.paic.ncbs.claim.service.ahcs.AhcsStartReportBpmService;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseExService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsPersonalInjuryDeathInfoService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.report.*;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PlanPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private LinkManService linkManService;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private WholeCaseBaseExService wholeCaseBaseExService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private ReportExcService reportExcService;
    @Autowired
    private DepartmentDefineService departmentDefineService;
    @Autowired
    private ReportAccidentBaggageService reportAccidentBaggageService;
    @Autowired
    private ReportAccidentExamService reportAccidentExamService;
    @Autowired
    private ReportAccidentFlightService reportAccidentFlightService;
    @Autowired
    private ReportAccidentOtherService reportAccidentOtherService;
    @Autowired
    private ReportAccidentLossService reportAccidentLossService;
    @Autowired
    private ReportAccidentTrafficService reportAccidentTrafficService;
    @Autowired
    private ReportAccidentTravelService reportAccidentTravelService;
    @Autowired
    private AhcsSavePolicyinfoService ahcsSavePolicyinfoService;
    @Autowired
    private AhcsStartReportBpmService ahcsStartReportBpmService;
    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;
    @Autowired
    private ReportAccidentPetMapper reportAccidentPetMapper;
    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private RiskPropertyMapper riskPropertyMapper;
    @Autowired
    private ClmsPersonalInjuryDeathInfoService clmsPersonalInjuryDeathInfoService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private AhcsPolicyPlanMapper policyPlanMapper;
    @Autowired
    private AhcsPolicyInfoMapper policyInfoMapper;
    @Autowired
    private AhcsPolicyDutyMapper policyDutyMapper;
    @Autowired
    private AhcsPolicyDutyDetailMapper policyDutyDetailMapper;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private PolicyPayMapper policyPayDao;
    @Autowired
    private PlanPayService planPayService;
    @Autowired
    private MaxPayService maxPayService;
    @Autowired
    private ResidueAmountService residueAmountService;
    @Autowired
    private EstimatePolicyMapper estimatePolicyDAO;
    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;

    @Override
    public AhcsDomainDTO getReportInfo(String reportNo) {
        AhcsDomainDTO ahcsDomainDTO = new AhcsDomainDTO();
        try {
            ahcsDomainDTO.setReportInfo(this.getReportInfoByReportNo(reportNo));

            ahcsDomainDTO.setReportAccident(this.getReportAccident(reportNo));

            ReportAccidentExEntity exEntity = this.getReportAccidentEx(reportNo);

            ahcsDomainDTO.setReportAccidentEx(exEntity);

            if (Objects.nonNull(exEntity) && RapeCheckUtil.isNotBlank(exEntity.getAccidentExtendInfo())) {
                JSONObject extendInfo = JSON.parseObject(exEntity.getAccidentExtendInfo());
                exEntity.setDrivingTestExtendDto(extendInfo);
                LogUtil.info("获取意健险事故表扩展数据, reportNo:{}, data:{}", reportNo, extendInfo);
                JSONArray selectedPolicyNos = extendInfo.getJSONArray("selectedPolicyNos");
                if (Objects.nonNull(selectedPolicyNos)) {
                    ahcsDomainDTO.setSelectedPolicyNos(selectedPolicyNos.toJavaList(String.class));
                }
            }

            ahcsDomainDTO.setWholeCaseBase(this.getWholeCaseBase(reportNo));

            ahcsDomainDTO.setWholeCaseBaseEx(this.getWholeCaseBaseEx(reportNo));

            ahcsDomainDTO.setReportCustomerInfo(this.getReportCustomerInfoByReportNo(reportNo));

            ahcsDomainDTO.setLinkMans(this.getLinkMans(reportNo,null));

            ahcsDomainDTO.setReportInfoExs(this.getReportInfoEx(reportNo));

            ahcsDomainDTO.setReportExc(this.getReportExcByReportNo(reportNo));
            //非人伤的数据，暂时不需要
            ahcsDomainDTO.setReportAccidentBaggage(this.getReportAccidentBaggageByReportNo(reportNo));

            ahcsDomainDTO.setReportAccidentExam(this.getReportAccidentExamByReportNo(reportNo));

            ahcsDomainDTO.setReportAccidentTraffic(this.getReportAccidentTrafficByReportNo(reportNo));

            ahcsDomainDTO.setReportAccidentTravel(this.getReportAccidentTravelByReportNo(reportNo));

            ahcsDomainDTO.setReportAccidentOther(this.getReportAccidentOtherByReportNo(reportNo));

            ahcsDomainDTO.setReportAccidentFlight(this.getReportAccidentFlightByReportNo(reportNo));

            ahcsDomainDTO.setReportAccidentLoss(this.getReportAccidentLossByReportNo(reportNo));

        } catch (Exception e) {
            LogUtil.info("获取案件全量信息异常, reportNo:{}", reportNo, e);
            return null;
        }
        return ahcsDomainDTO;
    }

    @Override
    @Transactional
    public AhcsDomainDTO saveReportInfo(ReportInfoDTO reportInfo, AhcsDomainDTO ahcsDomainDTO, boolean isTranCertificate) {
        long start = System.currentTimeMillis();

        this.transforAhcsDomainDTO(reportInfo, ahcsDomainDTO);

        //保存报案信息 + 标的信息 等
        AhcsDomainDTO dto = this.saveBaseReportDomain(ahcsDomainDTO, reportInfo, isTranCertificate);
        long end = System.currentTimeMillis();
        LogUtil.info("保存的报案数据耗时：{}", end - start);
        return dto;
    }

    private void transforAhcsDomainDTO(ReportInfoDTO reportInfoDto, AhcsDomainDTO ahcsDomainDTO) {

        this.buildReportAccident(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentEx(ahcsDomainDTO, reportInfoDto);

        this.buildReportInfo(ahcsDomainDTO, reportInfoDto);

        this.buildReportInfoEx(ahcsDomainDTO, reportInfoDto);

        this.buildCaseBase(ahcsDomainDTO, reportInfoDto);

        this.buildReportLinkManInfo(ahcsDomainDTO, reportInfoDto);

        this.buildWholeCaseBase(ahcsDomainDTO, reportInfoDto);

        this.buildWholeCaseBaseEx(ahcsDomainDTO, reportInfoDto);

        this.buildReportExc(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentBaggage(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentExam(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentFlight(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentLoss(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentOther(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentTraffic(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentTravel(ahcsDomainDTO, reportInfoDto);

        this.buildReportAccidentPet(ahcsDomainDTO);

        buildReportRiskProperty(ahcsDomainDTO);

    }

    private void buildReportRiskProperty(AhcsDomainDTO ahcsDomainDTO){
        List<ReportRiskPropertyDTO> reportRiskList = ahcsDomainDTO.getReportRiskPropertyList();
        if(ListUtils.isNotEmpty(reportRiskList)){
            for (ReportRiskPropertyDTO riskPropertyDTO : reportRiskList) {
                riskPropertyDTO.setReportNo(ahcsDomainDTO.getReportNo());
                riskPropertyDTO.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
                riskPropertyDTO.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
                riskPropertyDTO.setIdReportRiskProperty(UuidUtil.getUUID());
            }
        }
    }

    private void buildReportAccidentPet(AhcsDomainDTO ahcsDomainDTO) {
        ReportAccidentPetEntity reportAccidentPet = ahcsDomainDTO.getReportAccidentPet();
        if (reportAccidentPet != null) {
            reportAccidentPet.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentPet.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentPet.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentPet.setCreatedDate(new Date());
            reportAccidentPet.setUpdatedDate(new Date());
            reportAccidentPet.setIdAhcsReportAccidentPet(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentPet(reportAccidentPet);
        }
    }

    private void buildReportAccidentTravel(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentTravelEntity reportAccidentTravel = ahcsDomainDTO.getReportAccidentTravel();
        if (reportAccidentTravel != null) {
            reportAccidentTravel.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentTravel.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentTravel.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentTravel.setCreatedDate(new Date());
            reportAccidentTravel.setUpdatedDate(new Date());
            reportAccidentTravel.setIdAhcsReportAccidentTravel(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentTravel(reportAccidentTravel);
        }
    }

    private void buildReportAccidentTraffic(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentTrafficEntity reportAccidentTraffic = ahcsDomainDTO.getReportAccidentTraffic();
        if (reportAccidentTraffic != null) {
            reportAccidentTraffic.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentTraffic.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentTraffic.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentTraffic.setCreatedDate(new Date());
            reportAccidentTraffic.setUpdatedDate(new Date());
            reportAccidentTraffic.setIdAhcsReportAccidentTra(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentTraffic(reportAccidentTraffic);
        }
    }

    private void buildReportAccidentOther(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentOtherEntity reportAccidentOther = ahcsDomainDTO.getReportAccidentOther();
        if (reportAccidentOther != null) {
            reportAccidentOther.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentOther.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentOther.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentOther.setCreatedDate(new Date());
            reportAccidentOther.setUpdatedDate(new Date());
            reportAccidentOther.setIdAhcsReportAccidentOther(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentOther(reportAccidentOther);
        }
    }

    private void buildReportAccidentLoss(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentLossEntity reportAccidentLoss = ahcsDomainDTO.getReportAccidentLoss();
        if (reportAccidentLoss != null) {
            reportAccidentLoss.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentLoss.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentLoss.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentLoss.setCreatedDate(new Date());
            reportAccidentLoss.setUpdatedDate(new Date());
            reportAccidentLoss.setIdAhcsReportAccidentLoss(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentLoss(reportAccidentLoss);
        }
    }

    private void buildReportAccidentFlight(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentFlightEntity reportAccidentFlight = ahcsDomainDTO.getReportAccidentFlight();
        if (reportAccidentFlight != null) {
            reportAccidentFlight.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentFlight.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentFlight.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentFlight.setCreatedDate(new Date());
            reportAccidentFlight.setUpdatedDate(new Date());
            reportAccidentFlight.setIdAhcsReportAccidentFlight(UuidUtil.getUUID());
            String isFlightDelay = reportAccidentFlight.getIsFlightDelay();
            if(isFlightDelay == null || isFlightDelay.trim().length() == 0){
                reportAccidentFlight.setIsFlightDelay(ConstValues.YES);
            }
            ahcsDomainDTO.setReportAccidentFlight(reportAccidentFlight);
        }
    }

    private void buildReportAccidentExam(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentExamEntity reportAccidentExam = ahcsDomainDTO.getReportAccidentExam();
        if (reportAccidentExam != null) {
            reportAccidentExam.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentExam.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentExam.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentExam.setCreatedDate(new Date());
            reportAccidentExam.setUpdatedDate(new Date());
            reportAccidentExam.setIdAhcsReportAccidentExam(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentExam(reportAccidentExam);
        }

    }

    private void buildReportAccidentBaggage(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentBaggageEntity reportAccidentBaggage = ahcsDomainDTO.getReportAccidentBaggage();
        if (reportAccidentBaggage != null) {
            reportAccidentBaggage.setReportNo(ahcsDomainDTO.getReportNo());
            reportAccidentBaggage.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentBaggage.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportAccidentBaggage.setCreatedDate(new Date());
            reportAccidentBaggage.setUpdatedDate(new Date());
            reportAccidentBaggage.setIdAhcsReportAccidentBag(UuidUtil.getUUID());
            ahcsDomainDTO.setReportAccidentBaggage(reportAccidentBaggage);
        }
    }

    @Transactional
    @Override
    public AhcsDomainDTO saveBaseReportDomain(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto, boolean isTranCertificate) {

        AhcsDomainDTO dto = this.saveReportMainModel(ahcsDomainDTO, reportInfoDto);

        this.saveReportCustomerInfoModel(ahcsDomainDTO, isTranCertificate);
        this.saveReportPolicyModel(ahcsDomainDTO);
        return dto;
    }


    @Transactional
    public AhcsDomainDTO saveReportMainModel(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        //插入CLM_REPORT_INFO报案信息表一条新记录
        reportInfoService.insert(ahcsDomainDTO.getReportInfo());

        //批量插入AhcsReportInfoEx报案信息扩展表记录
        for (ReportInfoExEntity reportInfoEx : ahcsDomainDTO.getReportInfoExs()) {
            reportInfoExService.insert(reportInfoEx);
        }
        //插入clmReportAccident事故信息表记录
        reportAccidentService.insert(ahcsDomainDTO.getReportAccident());
        //插入AhcsReportAccidentEx意键险报案信息扩展表
        reportAccidentExService.insert(ahcsDomainDTO.getReportAccidentEx());
        //插入AhcsReportAccidentBaggage意键险行李延误信息表
        if (ahcsDomainDTO.getReportAccidentBaggage() != null) {
            reportAccidentBaggageService.insert(ahcsDomainDTO.getReportAccidentBaggage());
        }
        //插入ahcs_report_accident_exam意键险考试不通过表
        if (ahcsDomainDTO.getReportAccidentExam() != null) {
            reportAccidentExamService.insert(ahcsDomainDTO.getReportAccidentExam());
        }
        //插入ahcs_report_accident_flight意键险航班延误
        if (ahcsDomainDTO.getReportAccidentFlight() != null) {
            reportAccidentFlightService.insert(ahcsDomainDTO.getReportAccidentFlight());
        }
        //插入ahcs_report_accident_other意键险其他非人伤信息表
        if (ahcsDomainDTO.getReportAccidentOther() != null) {
            reportAccidentOtherService.insert(ahcsDomainDTO.getReportAccidentOther());
        }
        //插入ahcs_report_accident_loss意键险财产损失信息表
        if (ahcsDomainDTO.getReportAccidentLoss() != null) {
            reportAccidentLossService.insert(ahcsDomainDTO.getReportAccidentLoss());
        }
        //插入ahcs_report_accident_traffic意键险其他交通工具延误信息
        if (ahcsDomainDTO.getReportAccidentTraffic() != null) {
            reportAccidentTrafficService.insert(ahcsDomainDTO.getReportAccidentTraffic());
        }
        //插入ahcs_report_accident_travel意健险旅行变更信息表
        if (ahcsDomainDTO.getReportAccidentTravel() != null) {
            reportAccidentTravelService.insert(ahcsDomainDTO.getReportAccidentTravel());
        }

        if (ahcsDomainDTO.getReportAccidentPet() != null) {
            reportAccidentPetMapper.insert(ahcsDomainDTO.getReportAccidentPet());
        }

        //根据ReportNo和Casetimes获取clm_case_base保单案件信息表
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportInfoDto.getReportNo(), "1");
        if (CollectionUtils.isEmpty(caseBaseInfoList)) {

            for (CaseBaseEntity caseBase : ahcsDomainDTO.getCaseBases()) {
                caseBaseService.insert(caseBase);
            }
        } else {
            List<String> casePolicyNos = caseBaseInfoList.stream().map(CaseBaseEntity::getPolicyNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<CaseBaseEntity> emptyPolicyCaseList = caseBaseInfoList.stream().filter(c -> StringUtils.isBlank(c.getPolicyNo())).collect(Collectors.toList());
            List<CaseBaseEntity> nonCasePolicyList = ahcsDomainDTO.getCaseBases().stream().filter(base -> !casePolicyNos.contains(base.getPolicyNo())).collect(Collectors.toList());
            for (int i = 0; i < nonCasePolicyList.size() && i < emptyPolicyCaseList.size(); i++) {
                CaseBaseEntity caseBase = emptyPolicyCaseList.get(i);
                CaseBaseEntity nonCasePolicy = nonCasePolicyList.get(i);
                caseBase.setPolicyNo(nonCasePolicy.getPolicyNo());
                caseBase.setDepartmentCode(nonCasePolicy.getDepartmentCode());
                caseBase.setUpdatedDate(nonCasePolicy.getUpdatedDate());
                caseBase.setUpdatedBy(nonCasePolicy.getUpdatedBy());
                caseBaseService.updateByPrimaryKeySelective(caseBase);
            }
        }
        //插入ahcs_link_man联系人表
        for (LinkManEntity linkMan : ahcsDomainDTO.getLinkMans()) {
            linkManService.insert(linkMan);
        }
        //插入clm_whole_case_base整案信息表
        for (WholeCaseBaseEntity wholeCaseBase : ahcsDomainDTO.getWholeCaseBase()) {
            wholeCaseBase.setInjuryReasonCode(reportInfoDto.getInjuryReasonCode());
            wholeCaseBaseService.insert(wholeCaseBase);
        }
        //插入clm_whole_case_base_ex整案扩展表
        for (WholeCaseBaseExEntity wholeCaseBaseEx : ahcsDomainDTO.getWholeCaseBaseEx()) {
            wholeCaseBaseExService.insert(wholeCaseBaseEx);
        }

        // 前端目前未存放该对象信息，逻辑上不保存该表数据
        if(ListUtils.isNotEmpty(ahcsDomainDTO.getReportRiskPropertyList())){
            // 报案标的表 CLMS_RISK_PROPERTY_REPORT
            riskPropertyMapper.saveReportRiskPropertyList(ahcsDomainDTO.getReportRiskPropertyList());
        }

        // 保存理赔标的信息
        if(ListUtils.isNotEmpty(ahcsDomainDTO.getCaseRiskPropertyList())){
            // 理赔标的信息表 CLMS_RISK_PROPERTY_CASE
            riskPropertyMapper.saveCaseRiskPropertyList(ahcsDomainDTO.getCaseRiskPropertyList());
        }

        // 保存人伤信息 clms_personal_injury_death_info
        if(ahcsDomainDTO.getClmsPersonalInjuryDeathInfoDTO() != null
                && StringUtils.isNotEmpty(ahcsDomainDTO.getClmsPersonalInjuryDeathInfoDTO().getInjuredName())) {
        	this.saveReportRiskSubProp(ahcsDomainDTO,reportInfoDto);
        }

        return ahcsDomainDTO;
    }


    private void saveReportCustomerInfoModel(AhcsDomainDTO ahcsDomainDTO, boolean isTranCertificate) {
        ReportCustomerInfoEntity reportCustomerInfo = ahcsDomainDTO.getReportCustomerInfo();
        LogUtil.audit("保存的客户数据reportCustomerInfo={}", JSON.toJSONString(reportCustomerInfo));
        if (StringUtils.isNotBlank(reportCustomerInfo.getClientNo())) {
            reportCustomerInfo.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportCustomerInfo.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportCustomerInfo.setCreatedDate(new Date());
            reportCustomerInfo.setUpdatedDate(new Date());
            if (isTranCertificate) {

                if (!BaseConstant.UPPER_CASE_Y.equals(reportCustomerInfo.getIsOrganization())) {
                    reportCustomerInfo.setCertificateType(reportCustomerInfo.getCertificateType());
                }
            }
            reportCustomerInfo.setIdAhcsReportCustomer(UuidUtil.getUUID());
            reportCustomerInfo.setReportNo(ahcsDomainDTO.getReportNo());
            //根据报案号查询ahcs_report_customer报案客户信息表；如果能查出，则删除该条记录；
            // 最后添加一条新记录
            ReportCustomerInfoEntity customer = reportCustomerInfoService.getReportCustomerInfoByReportNo(ahcsDomainDTO.getReportNo());
            if (customer != null) {
                reportCustomerInfoService.delete(customer.getIdAhcsReportCustomer());
            }
            RatingQueryVO queryVO = new RatingQueryVO(reportCustomerInfo.getName());
            queryVO.setPhoneNumber(reportCustomerInfo.getMobileTelephone());
            if(reportCustomerInfo.getCertificateType()!= null && CertificateTypeEnum.ID_CARD.getType().equals(reportCustomerInfo.getCertificateType())){
                queryVO.setIdentificationNumber(reportCustomerInfo.getCertificateNo());
            }
            reportCustomerInfo.setClientType(customerInfoStoreSAO.queryCustomerRating(queryVO));
            reportCustomerInfoService.insert(reportCustomerInfo);
        }
    }

    public void buildReportInfo(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {

        ReportInfoEntity reportInfo = ahcsDomainDTO.getReportInfo();
        if (reportInfo == null) {
            reportInfo = new ReportInfoEntity();
        }
        List<LinkManEntity> linkManList = reportInfoDto.getLinkManList();

        if (RapeCheckUtil.isListNotEmpty(linkManList)) {
            LinkManEntity linkMan = linkManList.get(0);
            // 应该取报案人
            if (StringUtils.isEmpty(linkMan.getApplicantPerson())) {
                reportInfo.setReporterName(linkMan.getLinkManName());
            } else {
                reportInfo.setReporterName(linkMan.getApplicantPerson());
            }
            if (StringUtils.isEmpty(reportInfo.getReporterCallNo())) {
                reportInfo.setReporterCallNo(linkMan.getLinkManTelephone());
            }
            if (StringUtils.isEmpty(reportInfo.getReporterRegisterTel())) {
                reportInfo.setReporterRegisterTel(linkMan.getLinkManTelephone());
            }
        }
        reportInfo.setReportNo(ahcsDomainDTO.getReportNo());

        reportInfo.setIdClmReportInfo(UuidUtil.getUUID());
        reportInfo.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setCreatedDate(new Date());
        reportInfo.setUpdatedDate(new Date());
        if (reportInfo.getReportDate() == null) {
            reportInfo.setReportDate(new Date());
        }
        reportInfo.setReportRegisterUm(ahcsDomainDTO.getReportAcceptUm());

        reportInfo.setRemark(reportInfoDto.getRemark());
        reportInfo.setAcceptDepartmentCode(ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode());
        if (StringUtils.isNotEmpty(reportInfoDto.getMigrateFrom())) {
            reportInfo.setMigrateFrom(reportInfoDto.getMigrateFrom());
        } else {
            reportInfo.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        }
        ahcsDomainDTO.setReportInfo(reportInfo);
    }

    public void buildReportInfoEx(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        List<ReportInfoExEntity> reportInfoExList = new ArrayList<ReportInfoExEntity>();
        List<LinkManEntity> linkManList = reportInfoDto.getLinkManList();
        ReportExcEntity exc = ahcsDomainDTO.getReportExc();
        LogUtil.audit("构建案件报案信息拓展信息ahcsDomainDTO.getReportInfoExs()={}", JSON.toJSONString(ahcsDomainDTO.getReportInfoExs()));
        ReportInfoExEntity reportInfoEx = new ReportInfoExEntity();
        if (RapeCheckUtil.isListNotEmpty(ahcsDomainDTO.getReportInfoExs())) {
            reportInfoEx = ahcsDomainDTO.getReportInfoExs().get(0);
        }
        reportInfoEx.setCaseType(reportInfoDto.getCaseType());
        if (StringUtils.isEmpty(reportInfoEx.getSuccorService())) {
            reportInfoEx.setSuccorService(CommonConstant.NO);
        }
        reportInfoEx.setSuccorCompany(reportInfoDto.getSuccorCompany());
        reportInfoEx.setSuccorCompanyName(reportInfoDto.getSuccorCompanyName());
        reportInfoEx.setSuccorServiceCode(reportInfoDto.getSuccorServiceCode());
        reportInfoEx.setSuccorServiceName(reportInfoDto.getSuccorServiceName());
        reportInfoEx.setSuccorServiceLevel(reportInfoDto.getSuccorServiceLevel());

        this.buildRemark(ahcsDomainDTO, reportInfoDto, exc, reportInfoEx);
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());
        reportInfoEx.setIsSpecialReport(reportInfoDto.getIsSpecialReport());
        reportInfoEx.setReportNo(ahcsDomainDTO.getReportNo());
        reportInfoEx.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfoEx.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfoEx.setPartnerCode(reportInfoDto.getPartnerCode());
        reportInfoEx.setCreatedDate(new Date());
        reportInfoEx.setUpdatedDate(new Date());
        reportInfoEx.setCaseClass(reportInfoDto.getLossClass());
        reportInfoEx.setDocumentGroupId(reportInfoDto.getDocumentGroupId());

        if (RapeCheckUtil.isNotEmpty(ahcsDomainDTO.getAhcsPolicyDomainDTOs())) {
            for (AhcsPolicyDomainDTO policyDomainDTO : ahcsDomainDTO.getAhcsPolicyDomainDTOs()) {
                if ("02".equals(policyDomainDTO.getProductClass())) {
                    reportInfoEx.setCaseSign(BaseConstant.STRING_1);
                    break;
                }
            }
        }
        if (StringUtils.isNotEmpty(reportInfoDto.getCostEstimate())) {
            try {
                reportInfoEx.setCostEstimate(new BigDecimal(reportInfoDto.getCostEstimate()));
            } catch (Exception e) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("输入费用预估数据不合法"));
            }
        }
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());

        if (RapeCheckUtil.isListNotEmpty(linkManList)) {
            LinkManEntity linkMan = reportInfoDto.getLinkManList().get(0);
            reportInfoEx.setLinkManName(linkMan.getLinkManName());
            if (linkMan.getLinkManRelation() == null) {
                linkMan.setLinkManRelation("98");
            }
            reportInfoEx.setLinkManRelation(linkMan.getLinkManRelation());
            reportInfoEx.setRelationWithReporter(linkMan.getApplicantType());
            reportInfoEx.setSendMessage(linkMan.getSendMessage());
        }

        if (reportInfoDto.getSuccorService() != null && !reportInfoDto.getSuccorService().equalsIgnoreCase(BaseConstant.UPPER_CASE_N)) {
            if (StringUtils.isNotEmpty(reportInfoDto.getLinkmanUm()) || StringUtils.isNotEmpty(reportInfoDto.getLinkmanTel())) {
                Map<String, String> map = new HashMap<>();
                map.put("linkmanUm", reportInfoDto.getLinkmanUm());
                map.put("linkmanTel", reportInfoDto.getLinkmanTel());
                reportInfoEx.setReportExtend(JSONObject.toJSONString(map));
            }
        }

        reportInfoExList.add(reportInfoEx);
        LogUtil.audit("构建案件报案信息拓展信息reportInfoExList={}", JSON.toJSONString(reportInfoExList));
        ahcsDomainDTO.setReportInfoExs(reportInfoExList);
    }

    private void buildRemark(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto, ReportExcEntity exc,
                             ReportInfoExEntity reportInfoEx) {
        LogUtil.info("开始生成系统备注，案件类别:caseClass={}", reportInfoDto.getCaseClass());
        WholeCaseBaseEntity wholeCaseBase = null;
        if (RapeCheckUtil.isListNotEmpty(ahcsDomainDTO.getWholeCaseBase())) {
            wholeCaseBase = ahcsDomainDTO.getWholeCaseBase().get(CommonConstant.ZERO);
        }
        String remark = "";
        if (StringUtils.isEmpty(reportInfoEx.getPartnerCode())) {
            if (RapeCheckUtil.isNotEmpty(reportInfoDto.getPolicyNos())) {
                remark = "客户选择的保单号:" + reportInfoDto.getPolicyNos().toString();
            }
            if (RapeCheckUtil.isNotEmpty(reportInfoDto.getInputPolicyNos())) {
                remark = remark + ";客户输入的保单号:" + reportInfoDto.getInputPolicyNos().toString();
            }
        }

        if (exc != null) {
            if (StringUtils.isNotEmpty(exc.getPolicyNo())) {
                remark = remark + "来电登记的保单号:" + exc.getPolicyNo();
            }
            if (StringUtils.isNotEmpty(exc.getElecSubPolicyNo())) {
                remark = remark + "来电登记的电子保单号:" + exc.getElecSubPolicyNo();
            }
            if (StringUtils.isNotEmpty(exc.getDepartmentCode())) {
                DepartmentDefineEntity department = departmentDefineService.getDepartmentInfo(exc.getDepartmentCode());
                remark = remark + ";首次登记的承保机构:" + exc.getDepartmentCode() + "-" + department.getDepartmentAbbrName();
            }
            if (StringUtils.isNotEmpty(exc.getAccidentName())) {
                remark = remark + ";事故者姓名:" + exc.getAccidentName();
            }
            if (exc.getBirthday() != null) {
                remark = remark + ";事故者出生日期:" + RapeDateUtil.dateFormat(exc.getBirthday(), RapeDateUtil.SIMPLE_DATE_STR);
            }
            if (StringUtils.isNotEmpty(exc.getSex())) {
                remark = remark + ";事故者性别:" + SexEnum.getName(exc.getSex());
            }
            if (StringUtils.isNotEmpty(exc.getIsVirtual())) {
                remark = remark + ";是否为虚拟保单:" + exc.getIsVirtual();
            }
            LogUtil.info("生成异常案件系统备注完成：remark={}", remark);
        }

        if (ReportConstant.NOT_HURT.equals(reportInfoDto.getCaseClass())) {
            if (RapeCheckUtil.isNotEmpty(reportInfoDto.getLinkManList())) {
                remark = remark + "  联系电话:" + reportInfoDto.getLinkManList().get(CommonConstant.ZERO).getLinkManTelephone();
            }
            ReportAccidentTravelEntity reportAccidentTravel = ahcsDomainDTO.getReportAccidentTravel();
            ReportAccidentFlightEntity reportAccidentFlight = ahcsDomainDTO.getReportAccidentFlight();
            ReportAccidentBaggageEntity reportAccidentBaggage = ahcsDomainDTO.getReportAccidentBaggage();
            ReportAccidentTrafficEntity reportAccidentTraffic = ahcsDomainDTO.getReportAccidentTraffic();
            ReportAccidentLossEntity reportAccidentLoss = ahcsDomainDTO.getReportAccidentLoss();
            ReportAccidentOtherEntity reportAccidentOther = ahcsDomainDTO.getReportAccidentOther();
            ReportAccidentExamEntity reportAccidentExam = ahcsDomainDTO.getReportAccidentExam();
            if (reportAccidentFlight != null) {
                remark = remark + "  航班延误:{";
                if (StringUtils.isNotEmpty(reportAccidentFlight.getFlightNo())) {
                    remark = remark + "航班号：" + reportAccidentFlight.getFlightNo();
                }
                if (StringUtils.isNotEmpty(reportAccidentFlight.getEleTicketNo())) {
                    remark = remark + ",电子客票:" + reportAccidentFlight.getEleTicketNo();
                }
                if (StringUtils.isNotEmpty(reportAccidentFlight.getReplaceFlight())) {
                    remark = remark + ",代替航班:" + reportAccidentFlight.getReplaceFlight();
                }
                if (reportAccidentFlight.getTakeoffDate() != null) {
                    if (StringUtils.isNotEmpty(remark)) {
                        remark = remark + ",";
                    }
                    remark = remark + "起飞时间:"
                            + RapeDateUtil.parseToFormatString(reportAccidentFlight.getTakeoffDate(), RapeDateUtil.FULL_DATE_STR);
                }
                if (reportAccidentFlight.getDelayTime() != CommonConstant.ZERO) {
                    remark = remark + ",延误时长:" + reportAccidentFlight.getDelayTime() + "分钟";
                }
                if (StringUtils.isNotEmpty(reportAccidentFlight.getDeparturePlace())) {
                    remark = remark + ",起飞机场:" + reportAccidentFlight.getDeparturePlace();
                }
                if (StringUtils.isNotEmpty(reportAccidentFlight.getDestination())) {
                    remark = remark + ",到达机场:" + reportAccidentFlight.getDestination();
                }
                remark = remark + "}";


            }
            if (reportAccidentBaggage != null) {
                if (StringUtils.isNotEmpty(reportAccidentBaggage.getFlightNo())) {
                    remark = remark + ";行李延误:{航班号:" + reportAccidentBaggage.getFlightNo();
                }
                if (StringUtils.isNotEmpty(reportAccidentBaggage.getDestination())) {
                    remark = remark + ",目的地:" + reportAccidentBaggage.getBaggageDelayType();
                }
                if (reportAccidentBaggage.getPlanArrivalDate() != null) {
                    remark = remark + ",计划到到达时间:"
                            + RapeDateUtil.parseToFormatString(reportAccidentBaggage.getPlanArrivalDate(), RapeDateUtil.FULL_DATE_STR);
                }
                if (reportAccidentBaggage.getSignDate() != null) {
                    remark = remark + ",签收时间:"
                            + RapeDateUtil.parseToFormatString(reportAccidentBaggage.getSignDate(), RapeDateUtil.FULL_DATE_STR);
                }
                if (reportAccidentBaggage.getCostEstimate() != null) {
                    remark = remark + "费用预估:" + reportAccidentBaggage.getCostEstimate();
                }
                remark = remark + "}";
            }
            if (reportAccidentTraffic != null) {

                if (StringUtils.isNotEmpty(reportAccidentTraffic.getIsTrafficDelay())) {
                    remark = remark + ";其他公共交通工具延误:{是否延误:" + reportAccidentTraffic.getIsTrafficDelay() + ",";
                } else {
                    remark = remark + ";其他公共交通工具延误:{";
                }
                if (StringUtils.isNotEmpty(reportAccidentTraffic.getDeparturePlace())) {
                    remark = remark + "出发地" + reportAccidentTraffic.getDeparturePlace();
                }
                if (StringUtils.isNotEmpty(reportAccidentTraffic.getDestination())) {
                    remark = remark + ",目的地" + reportAccidentTraffic.getDestination();
                }
                if (reportAccidentTraffic.getOriginalDepartureDate() != null) {
                    remark = remark + ",原定时间:"
                            + RapeDateUtil.parseToFormatString(reportAccidentTraffic.getOriginalDepartureDate(),
                            RapeDateUtil.FULL_DATE_STR);
                    if (reportAccidentTraffic.getOriginalArrivalDate() != null) {
                        remark = remark + "-" + RapeDateUtil.parseToFormatString(reportAccidentTraffic.getOriginalArrivalDate(),
                                RapeDateUtil.FULL_DATE_STR);
                    }
                }
                if (reportAccidentTraffic.getActualDepartureDate() != null) {
                    remark = remark + ",实际时间:"
                            + RapeDateUtil.parseToFormatString(reportAccidentTraffic.getActualDepartureDate(),
                            RapeDateUtil.FULL_DATE_STR)
                            + "-" + RapeDateUtil.parseToFormatString(reportAccidentTraffic.getActualArrivalDate(),
                            RapeDateUtil.FULL_DATE_STR);
                }
                if (StringUtils.isNotEmpty(reportAccidentTraffic.getChangedPort())) {
                    remark = remark + ",港口信息:" + reportAccidentTraffic.getChangedPort();
                }
                if (StringUtils.isNotEmpty(reportAccidentTraffic.getSteamerDelayCase())) {
                    remark = remark + ",轮船延误情况:" + reportAccidentTraffic.getSteamerDelayCase();
                }
                if (reportAccidentTraffic.getCostEstimate() != null) {
                    remark = remark + ",费用预估:" + reportAccidentTraffic.getCostEstimate();
                }
                remark = remark + "}";
            }
            if (reportAccidentTravel != null) {
                if (StringUtils.isNotEmpty(reportAccidentTravel.getChangeReason())) {
                    remark = remark + ";旅行变更:{取消原因:" + ChangeReasonEnum.getName(reportAccidentTravel.getChangeReason());
                }
                if (!StringUtils.isNotEmpty(remark)) {
                    if (reportAccidentTravel.getCostEstimate() != null) {
                        remark = remark + "旅行变更:{费用预估:" + reportAccidentTravel.getCostEstimate();
                    }
                    remark = remark + "}";
                } else {
                    if (reportAccidentTravel.getCostEstimate() != null) {
                        remark = remark + ",费用预估:" + reportAccidentTravel.getCostEstimate();
                    }
                    remark = remark + "}";
                }

            }
            if (reportAccidentLoss != null) {
                if (reportAccidentLoss.getCostEstimate() != null) {
                    remark = remark + ";财产损失:{费用预估:" + reportAccidentLoss.getCostEstimate();
                }
                remark = remark + "}";
            }
            if (reportAccidentExam != null) {
                if (reportAccidentExam.getCostEstimate() != null) {
                    remark = remark + ";考试不通过:{费用预估:" + reportAccidentExam.getCostEstimate();
                }
                remark = remark + "}";
            }
            if (reportAccidentOther != null) {
                if (reportAccidentOther.getCostEstimate() != null) {
                    remark = remark + ";其他非人伤:{费用预估:" + reportAccidentOther.getCostEstimate();
                }
                remark = remark + "}";
            }
        }
        if (ReportConstant.HURT.equals(reportInfoDto.getCaseClass())) {
            ReportCustomerInfoEntity customerInfo = ahcsDomainDTO.getReportCustomerInfo();
            ReportAccidentExEntity accidentEx = ahcsDomainDTO.getReportAccidentEx();
            ReportAccidentEntity accident = ahcsDomainDTO.getReportAccident();
            ReportInfoEntity reportInfo = ahcsDomainDTO.getReportInfo();
            WholeCaseBaseExEntity wholeCaseBaseEx = null;
            if (RapeCheckUtil.isListNotEmpty(ahcsDomainDTO.getWholeCaseBaseEx())) {
                wholeCaseBaseEx = ahcsDomainDTO.getWholeCaseBaseEx().get(CommonConstant.ZERO);
            }
            if (customerInfo != null && StringUtils.isNotEmpty(customerInfo.getClientNo())) {
                remark = remark + "客户号:" + customerInfo.getClientNo();
            }
            if (StringUtils.isNotEmpty(ahcsDomainDTO.getReportAcceptUm())) {
                remark = remark + ";用户ID:" + ahcsDomainDTO.getReportAcceptUm();
            }
            if (accident != null && accident.getInjuredNumber() != null) {
                remark = remark + ";人伤数量:" + accident.getInjuredNumber();
            }
            if (accidentEx != null && StringUtils.isNotEmpty(accidentEx.getThisCarlicense())) {
                remark = remark + ";本车车牌:" + accidentEx.getThisCarlicense();
            }
            if (reportInfo != null && StringUtils.isNotEmpty(reportInfo.getDriverName())) {
                remark = remark + ";驾驶员姓名" + reportInfo.getDriverName();
            }
            if (reportInfo != null && StringUtils.isNotEmpty(reportInfo.getDriveCardId())) {
                remark = remark + ";驾驶员身份证件号码" + reportInfo.getDriveCardId();
            }
            if (reportInfo != null && StringUtils.isNotEmpty(SexEnum.getName(reportInfo.getDriveSex()))) {
                remark = remark + ";驾驶员性别" + SexEnum.getName(reportInfo.getDriveSex());
            }
            if (accident != null && StringUtils.isNotEmpty(accident.getOverseasOccur())) {
                remark = remark + ";是否境外出险:" + WhetherStatusEnum.getName(accident.getOverseasOccur());
            }
            if (accidentEx != null && StringUtils.isNotEmpty(accidentEx.getTrafficAccidentType())) {
                remark = remark + ";交通事故类型:" + TrafficAccidentStatusEnum.getName(accidentEx.getTrafficAccidentType());
            }
            if (wholeCaseBaseEx != null && (StringUtils.isNotEmpty(wholeCaseBaseEx.getIsSelfHelp()))) {
                remark = remark + ";是否自助理赔:" + WhetherStatusEnum.getName(wholeCaseBaseEx.getIsSelfHelp());
            }
            if (accidentEx != null && accidentEx.getDiedDate() != null) {
                remark = remark + "; 身故日期:" + RapeDateUtil.dateFormat(accidentEx.getDiedDate(), RapeDateUtil.FULL_DATE_STR);
            }
            if (accidentEx != null && StringUtils.isNotEmpty(accidentEx.getDiedCause())) {
                remark = remark + ";死亡原因:" + DiedCauseEnum.getName(accidentEx.getDiedCause());
            }
            if (accidentEx != null && StringUtils.isNotEmpty(accidentEx.getDiedStatus())) {
                remark = remark + ";身故者状态:" + DiedStatusEnum.getName(accidentEx.getDiedStatus());
            }
            if (reportInfoEx.getCostEstimate() != null) {
                remark = remark + ";费用预估:" + reportInfoEx.getCostEstimate();
            }
            if (accidentEx != null && StringUtils.isNotEmpty(accidentEx.getHospitalName())) {
                remark = remark + ";医院名称:" + accidentEx.getHospitalName();
            }
            if (StringUtils.isNotEmpty((reportInfoEx.getSuccorService()))) {
                if (reportInfoEx.getSuccorService().equals(BaseConstant.UPPER_CASE_Y) || reportInfoEx.getSuccorService().equals(BaseConstant.UPPER_CASE_I)) {
                    remark = remark + ";是否需要救援: " + BaseConstant.UPPER_CASE_Y;
                } else {
                    remark = remark + ";是否需要救援: " + reportInfoEx.getSuccorService();
                }
            }
            if (StringUtils.isNotEmpty(reportInfoEx.getSuccorServiceCode())) {
                String rescueTypeName = "";
                String[] array = reportInfoEx.getSuccorServiceCode().split("\\|");
                rescueTypeName = RescueTypeEnum.getName(array[0]);
                LogUtil.audit("rescueTypeName = {}", rescueTypeName);
                for (int i = 1; i < array.length; i++) {
                    rescueTypeName = rescueTypeName + "," + RescueTypeEnum.getName(array[i]);
                }
                remark = remark + ";申请救援项目:" + rescueTypeName;
            }
            if (accidentEx != null && StringUtils.isNotEmpty(accidentEx.getInsuredApplyStatus())) {
                remark = remark + ";事故类型：" + AccidentTypeEnum.getName(accidentEx.getAccidentType());
            }
            if (StringUtils.isNotEmpty(reportInfoDto.getReportTravelName())) {
                remark = remark + ";旅行社名称：" + reportInfoDto.getReportTravelName();
            }
            LogUtil.info("生成人伤系统备注完成：remark={}", remark);
        }

        LogUtil.audit("生成系统备注完成：remark={}", remark);
        reportInfoEx.setReportRemark(remark);

    }

    public void buildReportAccident(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentEntity reportAccident = Optional.ofNullable(ahcsDomainDTO.getReportAccident()).orElse(new ReportAccidentEntity());
        reportAccident.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccident.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccident.setCreatedDate(new Date());
        reportAccident.setUpdatedDate(new Date());
        reportAccident.setAccidentDate(reportInfoDto.getAccidentDate());
        reportAccident.setIdClmReportAccident(UuidUtil.getUUID());
        reportAccident.setReportNo(ahcsDomainDTO.getReportNo());

        String deptCode =ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode();
        if(deptCode!= null && deptCode.startsWith("211")){
            reportAccident.setAccidentCityCode("110100");
            reportAccident.setProvinceCode("110000");
            reportAccident.setAccidentCountyCode("110101");
        }

        //reportAccident.setAccidentCauseLevel1("意健险");
        if (StringUtils.isNotEmpty(reportInfoDto.getMigrateFrom())) {
            reportAccident.setMigrateFrom(reportInfoDto.getMigrateFrom());
        } else {
            reportAccident.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        }
        ahcsDomainDTO.setReportAccident(reportAccident);
    }

    public void buildReportAccidentEx(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportAccidentExEntity reportAccidentEx = ahcsDomainDTO.getReportAccidentEx();
        if (reportAccidentEx == null) {
            reportAccidentEx = new ReportAccidentExEntity();
        }
        reportAccidentEx.setCreatedBy(reportInfoDto.getReportAcceptUm());
        reportAccidentEx.setUpdatedBy(reportInfoDto.getReportAcceptUm());
        reportAccidentEx.setCreatedDate(new Date());
        reportAccidentEx.setUpdatedDate(new Date());
        reportAccidentEx.setAccidentType(reportInfoDto.getAccidentType());
        reportAccidentEx.setIdAhcsReportAccidentEx(UuidUtil.getUUID());
        reportAccidentEx.setReportNo(ahcsDomainDTO.getReportNo());
        reportAccidentEx.setInsuredApplyType(reportInfoDto.getInsuredApplyType());
        reportAccidentEx.setInsuredApplyStatus(InsuredApplyStatusEnum.END_OF_TREATMENT.getType());
        ahcsDomainDTO.setReportAccidentEx(reportAccidentEx);
    }

    public void buildReportLinkManInfo(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        List<LinkManEntity> linkManList = new ArrayList<LinkManEntity>();
        short i = 1;
        for (LinkManEntity linkMan : reportInfoDto.getLinkManList()) {
            linkMan.setIdAhcsLinkMan(UuidUtil.getUUID());
            linkMan.setIsReport(CommonConstant.YES);
            linkMan.setReportNo(ahcsDomainDTO.getReportNo());
            linkMan.setCreatedDate(new Date());
            linkMan.setUpdatedDate(new Date());
            linkMan.setCreatedBy(reportInfoDto.getReportAcceptUm());
            linkMan.setUpdatedBy(reportInfoDto.getReportAcceptUm());

            linkMan.setLinkManNo(i++);
            linkMan.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
            if (linkMan.getLinkManRelation() == null) {
                linkMan.setLinkManRelation("98");
            }
            linkManList.add(linkMan);
        }
        ahcsDomainDTO.setLinkMans(linkManList);
    }

    public void buildWholeCaseBase(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        List<WholeCaseBaseEntity> wholeCaseBaseList = ahcsDomainDTO.getWholeCaseBase();
        WholeCaseBaseEntity wholeCaseBase = new WholeCaseBaseEntity();
        if (RapeCheckUtil.isListNotEmpty(wholeCaseBaseList)) {
            wholeCaseBase = wholeCaseBaseList.get(CommonConstant.ZERO);
        } else {
            wholeCaseBaseList = new ArrayList<WholeCaseBaseEntity>();
            wholeCaseBaseList.add(wholeCaseBase);
        }
        wholeCaseBase.setCreatedBy(reportInfoDto.getReportAcceptUm());
        wholeCaseBase.setUpdatedBy(reportInfoDto.getReportAcceptUm());
        wholeCaseBase.setCreatedDate(new Date());
        wholeCaseBase.setUpdatedDate(new Date());
        wholeCaseBase.setIdClmWholeCaseBase(UuidUtil.getUUID());
        if (StringUtils.isNotEmpty(reportInfoDto.getMigrateFrom())) {
            wholeCaseBase.setMigrateFrom(reportInfoDto.getMigrateFrom());
        } else {
            wholeCaseBase.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        }
        wholeCaseBase.setAllowQuickFinish(CommonConstant.NO);
        wholeCaseBase.setReportNo(ahcsDomainDTO.getReportNo());
        wholeCaseBase.setReceiveVoucherUm(reportInfoDto.getReportAcceptUm());
        wholeCaseBase.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        wholeCaseBase.setWholeCaseStatus(ReportConstant.REPROTED);
        wholeCaseBase.setCaseIdentification("01");
        if("Y".equals(reportInfoDto.getIsSuffice())){
            wholeCaseBase.setDocumentFullDate(new Date());
        }
        ahcsDomainDTO.setWholeCaseBase(wholeCaseBaseList);
    }

    public void buildWholeCaseBaseEx(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        List<WholeCaseBaseExEntity> wholeCaseBaseExList = ahcsDomainDTO.getWholeCaseBaseEx();
        WholeCaseBaseExEntity wholeCaseBaseEx = new WholeCaseBaseExEntity();
        if (RapeCheckUtil.isListNotEmpty(wholeCaseBaseExList)) {
            wholeCaseBaseEx = wholeCaseBaseExList.get(CommonConstant.ZERO);
        } else {
            wholeCaseBaseExList = new ArrayList<WholeCaseBaseExEntity>();
            wholeCaseBaseExList.add(wholeCaseBaseEx);
        }
        wholeCaseBaseEx.setCreatedBy(reportInfoDto.getReportAcceptUm());
        wholeCaseBaseEx.setUpdatedBy(reportInfoDto.getReportAcceptUm());
        wholeCaseBaseEx.setCreatedDate(new Date());
        wholeCaseBaseEx.setUpdatedDate(new Date());
        wholeCaseBaseEx.setIdClmWholeCaseBase(ahcsDomainDTO.getWholeCaseBase().get(CommonConstant.ZERO).getIdClmWholeCaseBase());
        wholeCaseBaseEx.setIdClmWholeCaseBaseEx(UuidUtil.getUUID());
        wholeCaseBaseEx.setReportNo(ahcsDomainDTO.getReportNo());
        wholeCaseBaseEx.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        ahcsDomainDTO.setWholeCaseBaseEx(wholeCaseBaseExList);
    }

    public void buildCaseBase(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        List<CaseBaseEntity> caseBaseList = ahcsDomainDTO.getCaseBases();
        if (caseBaseList == null) {
            caseBaseList = new ArrayList<>();
        }
        for (AhcsPolicyDomainDTO policyDomainDto : ahcsDomainDTO.getAhcsPolicyDomainDTOs()) {
            String departmentCode = policyDomainDto.getAhcsPolicyInfo().getDepartmentCode();
            CaseBaseEntity caseBase = new CaseBaseEntity();
            caseBase.setCreatedDate(new Date());
            caseBase.setUpdatedDate(new Date());
            caseBase.setReportNo(ahcsDomainDTO.getReportNo());
            String caseNo = commonService.generateNo( NoConstants.CASE_NO, VoucherTypeEnum.CLAIM_NO,departmentCode);
            String registNo =commonService.generateNo( NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO, departmentCode);
            caseBase.setRegistNo(registNo);
            caseBase.setCaseNo(caseNo);
            policyDomainDto.getAhcsPolicyInfo().setCaseNo(caseNo);
            caseBase.setIdClmCaseBase(UuidUtil.getUUID());
            caseBase.setPolicyNo(policyDomainDto.getAhcsPolicyInfo().getPolicyNo());
            caseBase.setDepartmentCode(departmentCode);
            caseBase.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            caseBase.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            caseBase.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
            caseBase.setCaseStatus(ReportConstant.INIT_CASE_STATUS);
            if (StringUtils.isNotEmpty(reportInfoDto.getMigrateFrom())) {
                caseBase.setMigrateFrom(reportInfoDto.getMigrateFrom());
            } else {
                caseBase.setMigrateFrom(CommonConstant.MIGRATE_FROM);
            }

            caseBase.setRiskGroupNo(reportInfoDto.getRiskGroupNo());
            caseBase.setRiskGroupName(reportInfoDto.getRiskGroupName());

            caseBaseList.add(caseBase);
        }
        LogUtil.audit("构建赔案信息caseBaseList:{}", JSON.toJSONString(caseBaseList));

        reportInfoDto.setCaseTimes(ReportConstant.INIT_CASE_TIMES);
        ahcsDomainDTO.setCaseTimes(ReportConstant.INIT_CASE_TIMES);
        ahcsDomainDTO.setCaseBases(caseBaseList);
    }

    public void buildReportExc(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ReportExcEntity reportExc = ahcsDomainDTO.getReportExc();
        if (reportExc != null) {
            reportExc.setCreatedBy(reportInfoDto.getReportAcceptUm());
            reportExc.setUpdatedBy(reportInfoDto.getReportAcceptUm());
            reportExc.setCreatedDate(new Date());
            reportExc.setUpdatedDate(new Date());
            reportExc.setIdReportExc(UuidUtil.getUUID());
        }

    }

    private void saveReportPolicyModel(final AhcsDomainDTO ahcsDomainDTO) {
        long startTime = System.currentTimeMillis();
        List<AhcsPolicyDomainDTO> policyDomainDTOs = ahcsDomainDTO.getAhcsPolicyDomainDTOs();
        for (AhcsPolicyDomainDTO policyDomainDto : policyDomainDTOs) {
            ahcsSavePolicyinfoService.savePolicyInfo(ahcsDomainDTO, policyDomainDto);
        }

        long end = System.currentTimeMillis();
        LogUtil.info("保存保单数据耗时:{},报案号={}", end - startTime, ahcsDomainDTO.getReportNo());
    }

    @Override
    public AhcsDomainDTO requestReportDomainInfoNoCache(String reportNo) throws GlobalBusinessException{
        LogUtil.info("根据报案号查询全量信息入参reportNo:{}", reportNo);
        RapeCheckUtil.checkParamEmpty(reportNo, "报案号");
        AhcsDomainDTO ahcsDomainDTO = this.getReportInfo(reportNo);
        LogUtil.info("根据报案号查询全量信息出参AhcsDomainDTO:{}", JSON.toJSONString(ahcsDomainDTO));
        return ahcsDomainDTO;
    }

    //根据reportNo获取意健险全量数据
    @Override
    public JSONObject requestReportDomainInfo(String reportNo) {
        AhcsDomainDTO ahcsDomainDTO = this.getReportInfo(reportNo);
        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.put(Date.class, new ObjectSerializer() {
            @Override
            public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
                SerializeWriter writer = serializer.getWriter();
                if (object == null) {
                    serializer.getWriter().writeNull();
                    return;
                }
                try {
                    writer.write("\"" + DateUtils.parseToFormatString((Date)object,DateUtils.FULL_DATE_STR) + "\"");
                } catch (ParseException e) {
                    LogUtil.error("error", e);
                }
            }
        });
        return (JSONObject) JSON.toJSON(ahcsDomainDTO,serializeConfig);
    }

    @Override
    public List<HistoryCaseDTO> getAccidentDateReport(Date accidentDate, String certificateNo, String name, String certificateType) {
        String date = RapeDateUtil.parseToFormatString(accidentDate, RapeDateUtil.SIMPLE_DATE_STR);
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";
        return reportInfoService.getHistoryCaseBetweenTime(certificateNo,name,certificateType, beginTime, endTime);
    }

    @Override
    public AhcsDomainDTO getReportInfo(String reportNo, String caseTimes) {
        LogUtil.audit("查询报案信息入参：reportNo={}, caseTimes={}", reportNo, caseTimes);
        RapeCheckUtil.checkParamEmpty(reportNo, "reportNo");
        AhcsDomainDTO ahcsDomainDTO = this.getReportInfo(reportNo);
        ahcsDomainDTO.setReportNo(reportNo);
        ahcsDomainDTO.setCaseTimes(caseTimes);
        List<WholeCaseBaseEntity> wholeCaseBaseEntityList = ahcsDomainDTO.getWholeCaseBase();
        if (RapeCheckUtil.isListNotEmpty(wholeCaseBaseEntityList)) {
            wholeCaseBaseEntityList = wholeCaseBaseEntityList.stream()
                    .filter(entity -> Objects.equals(entity.getCaseTimes() + "", caseTimes))
                    .collect(Collectors.toList());
            ahcsDomainDTO.setWholeCaseBase(wholeCaseBaseEntityList);
        }
        List<WholeCaseBaseExEntity> wholeCaseBaseExList = ahcsDomainDTO.getWholeCaseBaseEx();
        if (RapeCheckUtil.isListNotEmpty(wholeCaseBaseExList)) {
            wholeCaseBaseExList = wholeCaseBaseExList.stream()
                    .filter(base -> Objects.equals(base.getCaseTimes() + "", caseTimes))
                    .collect(Collectors.toList());
            ahcsDomainDTO.setWholeCaseBaseEx(wholeCaseBaseExList);
        }
        return ahcsDomainDTO;
    }

    @Override
    public int updateByPrimaryKeySelective(ReportInfoExEntity reportInfoEx) {
        return reportInfoExService.updateByPrimaryKeySelective(reportInfoEx);
    }

    @Override
    public ReportInfoEntity getReportInfoByReportNo(String reportNo){
        return reportInfoService.getReportInfo(reportNo);
    }
    @Override
    public ReportAccidentEntity getReportAccident(String reportNo){
        return reportAccidentService.getReportAccident(reportNo);
    }
    @Override
    public ReportAccidentExEntity getReportAccidentEx(String reportNo){
        return reportAccidentExService.getReportAccidentEx(reportNo);
    }
    @Override
    public List<WholeCaseBaseEntity> getWholeCaseBase(String reportNo){
        return wholeCaseBaseService.getWholeCaseBase(reportNo);
    }
    @Override
    public List<WholeCaseBaseExEntity> getWholeCaseBaseEx(String reportNo){
        return wholeCaseBaseExService.getWholeCaseBaseEx(reportNo);
    }
    @Override
    public ReportCustomerInfoEntity getReportCustomerInfoByReportNo(String reportNo){
        return reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
    }
    @Override
    public List<LinkManEntity> getLinkMans(String reportNo,Integer caseTimes){
        return linkManService.getLinkMans(reportNo,caseTimes);
    }
    @Override
    public List<ReportInfoExEntity> getReportInfoEx(String reportNo){
        return reportInfoExService.getReportInfoEx(reportNo);
    }
    @Override
    public ReportExcEntity getReportExcByReportNo(String reportNo){
        return reportExcService.getReportExcByReportNo(reportNo);
    }
    /*-------------------------------------非人伤--------------------------------------------------------------*/
    @Override
    public ReportAccidentBaggageEntity getReportAccidentBaggageByReportNo(String reportNo){
        return reportAccidentBaggageService.getReportAccidentBaggageByReportNo(reportNo);
    }
    @Override
    public ReportAccidentExamEntity getReportAccidentExamByReportNo(String reportNo){
        return reportAccidentExamService.getReportAccidentExamByReportNo(reportNo);
    }
    @Override
    public ReportAccidentTrafficEntity getReportAccidentTrafficByReportNo(String reportNo){
        return reportAccidentTrafficService.getReportAccidentTrafficByReportNo(reportNo);
    }
    @Override
    public ReportAccidentTravelEntity getReportAccidentTravelByReportNo(String reportNo){
        return reportAccidentTravelService.getReportAccidentTravelByReportNo(reportNo);
    }
    @Override
    public ReportAccidentOtherEntity getReportAccidentOtherByReportNo(String reportNo){
        return reportAccidentOtherService.getReportAccidentOtherByReportNo(reportNo);
    }
    @Override
    public ReportAccidentFlightEntity getReportAccidentFlightByReportNo(String reportNo){
        return reportAccidentFlightService.getReportAccidentFlightByReportNo(reportNo);
    }
    @Override
    public ReportAccidentLossEntity getReportAccidentLossByReportNo(String reportNo){
        return reportAccidentLossService.getReportAccidentLossByReportNo(reportNo);
    }

    @Override
    public ReportAccidentPetEntity getReportAccidentPetByReportNo(String reportNo) {
        return reportAccidentPetMapper.getReportAccidentPetByReportNo(reportNo);
    }

    @Override
    @Transactional
    public void createReport(ReportInfoDTO reportInfo, AhcsDomainDTO ahcsDomainDTO) {
        // 保存案件信息
        AhcsDomainDTO dto = this.saveReportInfo(reportInfo, ahcsDomainDTO, true);
        initCaseClass(reportInfo);
        // 新建报案启动工作流 改造
        ahcsStartReportBpmService.reportStartReportBpmProcess(dto);
    }

    private void initCaseClass(ReportInfoDTO reportInfo){
        List<String> subCaseClass = reportInfo.getSubCaseClass();
        if(ChecklossConst.CASECLASS_NO_PEOPLE_HURT_SX.equals(reportInfo.getCaseClass())){
            if(ListUtils.isEmptyList(subCaseClass)){
                throw new GlobalBusinessException("非人伤报案,请勾选案件类别");
            }
            String userId = reportInfo.getReportAcceptUm();
            List<CaseClassDTO> caseSubClassDTO = new ArrayList<>();
            for (String caseSubClassStr : subCaseClass) {
                CaseClassDTO caseClassDTO = new CaseClassDTO();
                caseClassDTO.setCreatedBy(userId);
                caseClassDTO.setUpdatedBy(userId);
                caseClassDTO.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
                caseClassDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
                caseClassDTO.setReportNo(reportInfo.getReportNo());
                caseClassDTO.setCaseTimes(1);
                caseClassDTO.setCaseSubClass(caseSubClassStr);
                caseClassDTO.setArchiveTime(new Date());
                caseSubClassDTO.add(caseClassDTO);
            }
            caseClassDao.addCaseClassList(caseSubClassDTO, 1, userId);
        }

    }

    // 保存责任险报案标的及人伤信息
    private void saveReportRiskSubProp(AhcsDomainDTO ahcsDomainDTO, ReportInfoDTO reportInfoDto) {
        ClmsPersonalInjuryDeathInfo reportPersonalInjuryDeathInfo = ClmsPersonalInjuryDeathInfo.builder().build();
        BeanUtils.copyProperties(ahcsDomainDTO.getClmsPersonalInjuryDeathInfoDTO(), reportPersonalInjuryDeathInfo);
        reportPersonalInjuryDeathInfo.setReportNo(ahcsDomainDTO.getReportNo());
        reportPersonalInjuryDeathInfo.setCaseTimes(1);
        reportPersonalInjuryDeathInfo.setTaskId("report1");
        clmsPersonalInjuryDeathInfoService.insert(reportPersonalInjuryDeathInfo); // clms_personal_injury_death_info 人身伤亡信息表

    }

    @Override
    public ReportStatDTO getAccidentDateReportStat(ReportQueryVO queryVO) {
        return reportInfoService.getReportStat(queryVO);
    }

    @Override
    public List<ReportPlanDutyVo> getReportPlanDuty(String reportNo,Integer caseTimes) {
        List<ReportPlanDutyVo> reportPlanDutyVoList = new ArrayList<>();
        //责任信息查询
        List<ReportPlanDutyVo> reportPlanDutyVos = policyPlanMapper.getPlanList(reportNo);
        if(reportPlanDutyVos!=null && reportPlanDutyVos.size()>0){
            for(ReportPlanDutyVo planDTO:reportPlanDutyVos){
                ReportPlanDutyVo reportPlanDutyVo = new ReportPlanDutyVo();
                BeanUtils.copyProperties(planDTO,reportPlanDutyVo);
                List<ReportDutyVo> reportDutyVoList = policyDutyMapper.getDutyList(planDTO.getIdAhcsPolicyPlan());
                List<ReportDutyVo> reportDutyVoArrayList = new ArrayList<>();
                if(reportDutyVoList!=null && reportDutyVoList.size()>0){
                    for(ReportDutyVo policyDuty:reportDutyVoList){
                        ReportDutyVo ahcsPolicyDutyDTO = new ReportDutyVo();
                        BeanUtils.copyProperties(policyDuty,ahcsPolicyDutyDTO);
                        List<AhcsPolicyDutyDetailEntity> PolicyDutyDetailList = policyDutyMapper.getDutyDetailList(policyDuty.getIdAhcsPolicyDuty());
                        ahcsPolicyDutyDTO.setAhcsPolicyDutyDetail(PolicyDutyDetailList);
                        reportDutyVoArrayList.add(ahcsPolicyDutyDTO);
                    }
                }
                reportDutyVoArrayList.stream().forEach(dutyVo -> dutyVo.setDeductible(dutyAttributeMapper.getDeductibleToDuty(reportNo,dutyVo.getDutyCode())));
                reportPlanDutyVo.setReportDutyVoList(reportDutyVoArrayList);
                reportPlanDutyVoList.add(reportPlanDutyVo);
            }

        }
        maxPayService.initPoliciesPayMaxPayToPersonTrace(reportPlanDutyVoList,null);
        return reportPlanDutyVoList;
    }
    @Override
    public List<ReportPlanDutyVo> getReportPlanDuty(String reportNo) {
        List<ReportPlanDutyVo> reportPlanDutyVoList = new ArrayList<>();
        //责任明细层级信息查询
        List<ReportDutyDetailVo> dutydetailVos = policyInfoMapper.getDutyDetails(reportNo);
        //根据plancode分组
        Map<String, List<ReportDutyDetailVo>> planMaps = dutydetailVos.stream()
                .collect(Collectors.groupingBy(ReportDutyDetailVo::getPlanCode));
        //循环每个plan
        for (Map.Entry<String, List<ReportDutyDetailVo>> entry : planMaps.entrySet()) {
            ReportPlanDutyVo planDutyVo = new ReportPlanDutyVo();
            List<ReportDutyDetailVo> planDetails = entry.getValue();
            //组装planVo数据
            ReportDutyDetailVo planDetail = planDetails.get(0);
            planDutyVo.setReportNo(reportNo);
            planDutyVo.setPolicyNo(planDetail.getPolicyNo());
            planDutyVo.setPlanCode(planDetail.getPlanCode());
            planDutyVo.setPlanName(planDetail.getPlanName());
            //根据责任代码分组
            Map<String, List<ReportDutyDetailVo>> dutyMaps = planDetails.stream()
                    .collect(Collectors.groupingBy(ReportDutyDetailVo::getDutyCode));
            List<ReportDutyVo> reportDutyVoArrayList = new ArrayList<>();
            //循环每条责任
            for (Map.Entry<String, List<ReportDutyDetailVo>> dutyMap : dutyMaps.entrySet()) {
                List<ReportDutyDetailVo> dutyDetails = dutyMap.getValue();
                List<AhcsPolicyDutyDetailEntity> policyDutyDetails = new ArrayList<>();
                ReportDutyVo policyDutyDTO = new ReportDutyVo();
                //循环每条责任明细
                for(ReportDutyDetailVo dutyDetail:dutyDetails){
                    AhcsPolicyDutyDetailEntity policyDutyDetail = new AhcsPolicyDutyDetailEntity();
                    policyDutyDetail.setDutyDetailCode(dutyDetail.getDutyDetailCode());
                    policyDutyDetail.setDutyDetailName(dutyDetail.getDutyDetailName());
                    policyDutyDetails.add(policyDutyDetail);
                }
                policyDutyDTO.setAhcsPolicyDutyDetail(policyDutyDetails);
                //组装责任信息
                ReportDutyDetailVo dutyDetail = dutyDetails.get(0);
                policyDutyDTO.setDutyCode(dutyDetail.getDutyCode());
                policyDutyDTO.setDutyName(dutyDetail.getDutyName());
                policyDutyDTO.setDutyAmount(dutyDetail.getDutyAmount());
                policyDutyDTO.setIsDutySharedAmount(dutyDetail.getIsDutySharedAmount());
                policyDutyDTO.setDutySharedAmountMerge(dutyDetail.getDutySharedAmountMerge());
                //获取责任层级剩余保额
                BigDecimal remainAmount = getDutyRemainAmount(planDutyVo,policyDutyDTO);
                policyDutyDTO.setDutyAmount(remainAmount);
                //免赔额
                policyDutyDTO.setDeductible(dutyDetail.getDeductible());
                reportDutyVoArrayList.add(policyDutyDTO);
            }
            planDutyVo.setReportDutyVoList(reportDutyVoArrayList);
            reportPlanDutyVoList.add(planDutyVo);
        }
        return reportPlanDutyVoList;
    }
    private BigDecimal getDutyRemainAmount(ReportPlanDutyVo plan,ReportDutyVo duty) {
        String reportNo = plan.getReportNo();
        String policyNo = plan.getPolicyNo();
        String planCode = plan.getPlanCode();
        Boolean isShareAmount = !StringUtils.isEmpty(duty.getDutySharedAmountMerge());
        duty.setDutyShareAmount(isShareAmount);

        String dutyCode = duty.getDutyCode();
        //查询责任历史赔付金额
        BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(null, reportNo, policyNo, planCode,
                dutyCode, duty.getDutyShareAmount(), duty.getDutySharedAmountMerge(), isShareAmount);
        HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
        historyPayInfo.setPolicyNo(policyNo);
        historyPayInfo.setPlanCode(planCode);
        historyPayInfo.setDutyCode(dutyCode);
        historyPayInfo.setDutyBaseAmount(duty.getDutyAmount());
        historyPayInfo.setDutyHistoryPay(dutyHistoryPay);

        BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
        return dutyMaxPay;


//                for (AhcsPolicyDutyDetailEntity dutyDetail : duty.getAhcsPolicyDutyDetail()) {
//                    String dutyDetailCode = dutyDetail.getDutyDetailCode();
//
//                    //查询责任明细历史赔付金额
//                    BigDecimal dutyDetailHistoryPay = residueAmountService.getDutyDetailHistoryPay(scene, reportNo, policyNo,
//                            planCode, dutyCode, dutyDetailCode, isShareAmount);
//                    historyPayInfo.setDutyDetailCode(dutyDetailCode);
//                    historyPayInfo.setDutyDetailBaseAmount(dutyDetail.getDutyAmount());
//                    historyPayInfo.setDutyDetailHistoryPay(dutyDetailHistoryPay);
//                    BigDecimal dutyDetailMaxPay =  residueAmountService.getDutyDetailMaxPay(historyPayInfo);
//                    dutyDetail.setDutyAmount(dutyDetailMaxPay);
//
//                    //责任明细剩余理赔金额不能大于责任剩余理赔金额
//                    if (dutyMaxPay.compareTo(dutyDetailMaxPay) < 0) {
//                        dutyDetail.setDutyAmount(dutyMaxPay);
//                    } else {
//                        dutyDetail.setDutyAmount(dutyDetailMaxPay);
//                    }
//
//                }
    }


}
