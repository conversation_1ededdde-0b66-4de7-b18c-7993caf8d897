package com.paic.ncbs.claim.model.vo.communicate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "沟通主信息VO")
public class CommunicateBaseVO extends EntityDTO {


	private static final long serialVersionUID = 6061672719669992072L;

    @ApiModelProperty(value = "ahcs_communicate_base表主键")
	private String idAhcsCommunicateBase;

	@ApiModelProperty(value = "案件号")
	private String reportNo;

	@ApiModelProperty(value = "赔付次数")
	private Integer caseTimes;

	@ApiModelProperty(value = "沟通主题")
	private String communicateTitle;

	@ApiModelProperty(value = "沟通环节")
	private String communicateLink;

	@ApiModelProperty(value = "发起人(UM)")
	private String initiatorUm;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	@ApiModelProperty(value = "发起时间")
	private Date initiatDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	@ApiModelProperty(value = "完成时间")
	private Date finishDate;

	@ApiModelProperty(value = "沟通状态(0-待处理，1-处理中，2-完成)")
	private String communicateStatus;

	@ApiModelProperty(value = "沟通内容")
	private String communicateContent;

	@ApiModelProperty(value = "沟通环节任务实例ID")
	private String taskInstId;

	@ApiModelProperty(value = "发起沟通环节代码")
	private String communicateFrom;

	@ApiModelProperty(value = "是否挂起(1:挂起 0:不挂起)")
	private String isHangup;

    @ApiModelProperty(value = "沟通主信息列表")
	private List<CommunicateBaseVO> historyCommunicateBaseVOList;

    @ApiModelProperty(value = "沟通明细信息列表")
	private List<CommunicateDetailVO> communicateDetailVOList;

    @ApiModelProperty(value = "处理人列表")
	private List<String> assignerList;


	private Map<String, String> communicateLinkMap;


	private Map<String, String> communicateTitleMap;


	public Map<String, String> documenCommunicateTypeMap;


	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	@ApiModelProperty(value = "")
	private Date pageDate;

	@ApiModelProperty(value = "角色（0-沟通人，1-发起人）")
	private String role;

	@ApiModelProperty(value = "机构编码")
	private String departmentCode;

	@ApiModelProperty(value = "收单人UM")
	private String receiveVoucherUm;

	@ApiModelProperty(value = "")
	private String dealPersonTaskStatus;


	@ApiModelProperty(value = "问题编码")
	private String problemCode;

	@ApiModelProperty(value = "处理人UM")
	private String dealUm;

	@ApiModelProperty(value = "处理人")
	private String assigner;

	@ApiModelProperty(value = "赔款金额")
	private BigDecimal payAmount;

	@ApiModelProperty(value = "配置默认沟通处理人（TPA接口使用）")
	private String defaultUserId;

    @ApiModelProperty(value = "是否继续沟通（Y-继续，N-不继续）")
	private String isContinueCommunicate;

    @ApiModelProperty(value = "继续沟通人UM")
	private String continueCommunicateUser;

}
