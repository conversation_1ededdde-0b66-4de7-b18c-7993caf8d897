package com.paic.ncbs.claim.model.vo.blacklist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("黑名单记录vo")
public class ClmsBlackListRecordVO {

    @ApiModelProperty("")
    private String id;

    @ApiModelProperty("关联黑名单ID")
    private String blackListId;

    @ApiModelProperty("关联报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Short caseTimes;

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("黑名单类型")
    private String partyType;
    @ApiModelProperty("黑名单类型中文描述")
    private String partyTypeName;

    @ApiModelProperty("个人/企业")
    private String entityType;

    @ApiModelProperty("姓名/名称")
    private String partyName;

    @ApiModelProperty("证件类型")
    private String idType;
    @ApiModelProperty("证件类型中文描述")
    private String idTypeName;

    @ApiModelProperty("证件号码")
    private String idNum;

    @ApiModelProperty("风险类型")
    private String riskType;
    @ApiModelProperty("风险类型中文描述")
    private String riskTypeName;

    @ApiModelProperty("电话号码")
    private String phoneNum;

    @ApiModelProperty("有效标识")
    private String validFlag;

    @ApiModelProperty("来源")
    private String blackSource;

    @ApiModelProperty("审批状态")
    private String auditStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "关联报案号")
    private String relatedReportNo;


}
