package com.paic.ncbs.claim.controller.ahcs;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.vo.ahcs.FeeBigVO;
import com.paic.ncbs.claim.model.vo.ahcs.FeePaySumVO;
import com.paic.ncbs.claim.model.vo.ahcs.PayPrepayDetailVo;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


/**
 * @description 理赔费用保存与删除
 */
@Api(tags = {"理赔费用保存与删除"})
@RestController
@RequestMapping("/who/app/feePayAction")
public class FeePayController{

    @Resource(name = "feePayService")
    private FeePayService feePayService;
    @Autowired
    private PrePayService prePayService;

    /**
     * 根据报案号查出理赔费用信息
     * @param reportNo
     * @param caseTimes
     * @param isModifiedFlag 费用发票修改标记 0:未修改 1:待修改 2:修改完成 3:暂存
     * @return
     * @throws GlobalBusinessException
     */
    @ApiOperation("根据报案号查出理赔费用信息")
    @GetMapping(value = "/getFeePay/{reportNo}/{caseTimes}/{isModifiedFlag}/{claimType}")
    public ResponseResult<FeeBigVO> getFeePays(@PathVariable("reportNo") String reportNo,
                                               @PathVariable("caseTimes") Integer caseTimes,
                                               @PathVariable("isModifiedFlag") String isModifiedFlag,
                                               @PathVariable("claimType") String claimType) throws GlobalBusinessException {
        FeeBigVO vo = feePayService.getFeePays(reportNo, caseTimes, isModifiedFlag,claimType);
        return ResponseResult.success(vo);
    }

    /**
     * @param feeBig
     * @description 将理赔信息插入保存到数据库中
     */
    @ApiOperation("保存理赔信息")
    @PostMapping(value = "/saveFeePay")
    public ResponseResult<FeeBigVO> saveFeePay(@RequestBody FeeBigVO feeBig) throws Exception {
        LogUtil.audit("保存理赔信息saveFeePay报案号{},请求入参{}",feeBig.getReportNo(), JSON.toJSONString(feeBig));
        Optional.ofNullable(feeBig).orElseThrow(() ->  new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "入参"));
        Optional.ofNullable(feeBig.getReportNo()).orElseThrow(() ->  new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "报案号"));
        Optional.ofNullable(feeBig.getCaseTimes()).orElseThrow(() ->  new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "赔付次数"));
        RapeCheckUtil.checkListEmpty(feeBig.getNormalFee(), "费用明细");
        feePayService.saveFeePay(feeBig.getReportNo(),feeBig.getCaseTimes(),feeBig.getNormalFee());
        return ResponseResult.success(feeBig);
    }

	/**
     * @param idAhcsFeePay 理赔id
     * @throws GlobalBusinessException
     * @description 根据理赔id删除理赔信息记录
     */
	@ApiOperation("根据费用id删除理赔信息记录")
    @GetMapping(value = "/removeFeePay/{idAhcsFeePay}/{reportNo}/{caseTimes}")
    public ResponseResult<Object> removeFeePay(@PathVariable("idAhcsFeePay") String idAhcsFeePay,
                                               @PathVariable("reportNo") String reportNo,
                                               @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        FeeInfoDTO dto = feePayService.getFeePayById(idAhcsFeePay);
        //更新理算表的费用信息
        feePayService.removeFeePay(idAhcsFeePay, reportNo, caseTimes, dto);
        return ResponseResult.success();
    }

    /**
     * 根据支付信息id(CLM_PAYMENT_INFO表id)删除理赔信息
     * @param feePayDTO:reportNo(报案号)、caseTimes（赔付次数）、idClmPaymentInfoList（支付信息id集合）
     * @throws GlobalBusinessException
     */
    @ApiOperation("根据支付信息id删除理赔信息")
    @PostMapping(value = "/removeFeePayByIdPayInfo")
    public ResponseResult removeFeePayByIdPayInfo(@RequestBody FeePayDTO feePayDTO) throws Exception {
        this.validRemoveFeePayByIdParams(feePayDTO);
        feePayService.removeFeePayByIdPayinfo(feePayDTO);
        return ResponseResult.success();
    }

    /**
     * 校验根据支付信息id(CLM_PAYMENT_INFO表id)删除理赔信息 参数
     * @param feePayDTO
     * @return void
     */
    private void validRemoveFeePayByIdParams(FeePayDTO feePayDTO) throws GlobalBusinessException {
        if (ListUtils.isEmptyList(feePayDTO.getIdClmPaymentInfoList())) {
            LogUtil.audit("idClmPaymentInfoList(支付信息id)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "支付信息id");
        }
    }

    /**
     * 获取理赔费用总额
     * @param reportNo
     * @param caseTimes
     * @return sumFee 理赔费用总额
     */
    @ApiOperation("获取理赔费用总额")
    @GetMapping(value = "/getSumFeePay/{reportNo}/{caseTimes}")
    public ResponseResult<BigDecimal> getSumFeePay(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(feePayService.getSumFeePay(reportNo, caseTimes));
    }

    /**
     * @param invoiceInfo
     * @return 返回类型  InvoiceInfoDTO
     * @Description: 保存发票详细信息
     */
    @ApiOperation("保存发票详细信息")
    @PostMapping(value = "/saveInvoiceInfo")
    public ResponseResult<InvoiceInfoDTO> saveInvoiceInfo(@RequestBody InvoiceInfoDTO invoiceInfo) {
        feePayService.addInvoiceInfo(invoiceInfo);
        return ResponseResult.success(invoiceInfo);
    }

    /**
     * @param invoiceInfo
     * @return 返回类型  InvoiceInfoDTO
     * @Description: 查询发票信息
     */
    @ApiOperation("查询发票信息")
    @PostMapping(value = "/getInvoiceInfo")
    public ResponseResult<InvoiceInfoDTO> getInvoiceInfo(@RequestBody InvoiceInfoDTO invoiceInfo) {
        return ResponseResult.success(feePayService.getInvoiceInfo(invoiceInfo));
    }

    /**
     * 查询拒赔费用信息
     * @param reportNo
     * @param caseTimes
     * @return SttleBatchInfoVO
     * @throws GlobalBusinessException
     * @Description 方法描述
     */
    @ApiOperation("查询拒赔费用信息")
//    @GetMapping(value = "/getFeePayForSum/{reportNo}/{caseTimes}")
    public ResponseResult<FeePaySumVO> getFeePayForSum(@PathVariable("reportNo") String reportNo,
                                                       @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(feePayService.getFeePayForSum(reportNo, caseTimes));
    }

    /**
     * 根据报案号赔付次数删除理赔信息
     * @param reportNo(报案号)、caseTimes（赔付次数）
     * @throws GlobalBusinessException
     */
    @ApiOperation("根据报案号赔付次数删除理赔信息")
    @GetMapping(value = "/removeFeePayByRnCt/{reportNo}/{caseTimes}")
    public ResponseResult removeFeePayByRnCt(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) throws Exception {
        feePayService.removeFeePayByRnCt(reportNo, caseTimes);
        return ResponseResult.success();
    }

    /**
     * 根据支付信息id(CLM_PAYMENT_INFO表id)删除直接理赔信息记录
     * @param idClmPaymentInfo（支付信息id）
     * @throws GlobalBusinessException

     */
    @ApiOperation("根据支付信息id删除直接理赔信息记录")
    @GetMapping(value = "/removeFeePayByIdPaymentInfo/{reportNo}/{caseTimes}/{idClmPaymentInfo}")
    public ResponseResult removeFeePayByIdPaymentInfo(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes, @PathVariable("idClmPaymentInfo") String idClmPaymentInfo) throws Exception {
        feePayService.removeFeePayByIdPaymentInfo(reportNo, caseTimes, idClmPaymentInfo);
        return ResponseResult.success();
    }

    /**
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @GetMapping(value = "/getBatchByReportNo/{reportNo}/{caseTimes}")
    public ResponseResult<BatchDTO> getBatchByReportNo(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(feePayService.getBatchByReportNo(reportNo, caseTimes));
    }

    /**
     * 根据报案号和赔付次数查询赔付明细
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @ApiOperation("根据报案号和赔付次数查询赔付明细")
    @GetMapping(value = "/getFeeByClaimType")
    @ResponseBody
    public ResponseResult<List<FeeInfoDTO>> getFeeByClaimType(@RequestParam("reportNo") String reportNo,
                                       @RequestParam("caseTimes") Integer caseTimes,
                                       @RequestParam("claimType") String claimType,
                                       @RequestParam(value ="subTimes" ,required = false) Integer subTimes) {
        return ResponseResult.success(feePayService.getFeeByClaimType(reportNo, caseTimes, claimType, subTimes));
    }

    @ApiOperation("根据报案号查出预赔费用信息")
    @GetMapping(value = "/getPrePayFee/{reportNo}/{caseTimes}")
    public ResponseResult<FeeBigVO> getPrePayFee(@PathVariable("reportNo") String reportNo,
                                                 @PathVariable("caseTimes") Integer caseTimes){
        if(StringUtils.isEmptyStr(reportNo) || caseTimes == null){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }

        return ResponseResult.success(prePayService.getPrePayFee(reportNo,caseTimes));
    }

    @ApiOperation("保存预赔费用")
    @PostMapping(value = "/savePrePayFee")
    public ResponseResult savePrePayFee(@RequestBody FeeBigVO feeBig){
        if (null == feeBig || StringUtils.isEmptyStr(feeBig.getReportNo()) || null == feeBig.getCaseTimes()){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        prePayService.savePrePayFee(feeBig);
        return ResponseResult.success();
    }

    @ApiOperation("进项税费用发票修改")
    @PostMapping(value = "/feeInvoiceBackResultUpdate")
    public ResponseResult<Object> feeInvoiceBackResultModify(@RequestBody FeeBigVO feeBigVO) {
        feePayService.feeInvoiceBackResultModify(feeBigVO);
        return ResponseResult.success();
    }
    @ApiOperation("进项税费用发票提交")
    @PostMapping(value = "/feeInvoiceBackResultSubmit")
    public ResponseResult<Object> feeInvoiceBackResultSubmit(@RequestBody FeeBigVO feeBigVO) {
        feePayService.feeInvoiceBackResultSubmit(feeBigVO.getReportNo(),feeBigVO.getCaseTimes());
        return ResponseResult.success();
    }

    /**
     * 根据报案号和赔付次数查询赔付明细以及费用明细
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @ApiOperation("根据报案号和赔付次数查询赔付明细以及费用明细")
    @GetMapping(value = "/getPayPrepayDetail")
    @ResponseBody
    public ResponseResult<PayPrepayDetailVo> getPayPrepayDetail(@RequestParam("reportNo") String reportNo,
                                                                @RequestParam("caseTimes") Integer caseTimes,
                                                                @RequestParam("subTimes") Integer subTimes) {
        return ResponseResult.success(feePayService.getPayPrepayDetail(reportNo, caseTimes, subTimes));
    }
}
