package com.paic.ncbs.claim.dao.mapper.duty;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.openapi.DutyPayByCustomReqDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface DutyPayMapper extends BaseDao<DutyPayDTO> {

    DutyPayDTO getById(@Param("idAhcsDutyPay") String id, @Param("claimType") String claimType);

    public void insertDutyPayInfoList(@Param("list") List<DutyPayDTO> list);

    public void updateDutyPayInfoList(DutyPayDTO dutyPayArr);

    public void updateDutyPayInfoListByAuto(DutyPayDTO dutyPayArr);

    public void updateDutyPayInfoListForTPAReSettle(@Param("list") List<DutyPayDTO> dutyPayArr);

    public BigDecimal getDutyHistoryPay(MaxPayParam maxPayParam);

    public BigDecimal getDutyBaseAmount(MaxPayParam maxPayParam);

    void deleteByBatchId(@Param("idAhcsBatch") String idAhcsBatch, @Param("claimType") String claimType);

    List<DutyPayDTO> getDutyPayInfoByReportNoList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<DutyPayDTO> getDutyPayInfoList(DutyPayDTO dutyPayDTO);

    List<DutyPayDTO> getPreDutyPays(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("claimType") String claimType);

    List<EpcisPlanDTO> getNoEndCasePreDutyPays(MaxPayParam maxPayParam);

    BigDecimal getNoEndCasePreDutyAmount(MaxPayParam maxPayParam);

    List<DutyPayDTO> getPrePay(@Param("reportNo") String reportNo);

    List<HistoryPayInfoDTO> getDutyHistoryPayInfoPolicyCopy(MaxPayParam maxPayParam);

    List<String> getDutyPayInfoByPlan(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes, @Param("planCode") String planCode, @Param("claimType") String claimType);

    List<String> getDutyPayInfoByPlanCode(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes, @Param("planCode") String planCode);

    List<String> getDutyCodeByIdPlan(@Param("idAhcsPlanPay") String idAhcsPlanPay);

    List<DutyPayDTO> getDutyByIdPlanPay(@Param("idAhcsPlanPay") String idAhcsPlanPay);

    List<DutyPayDTO> getPlanDutyPayByPlan(PlanPayDTO plan);

    void updateFxqSettleReason(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("settleReason") String settleReason);

    List<DutyPayDTO> getDutyPrePayAomunt(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<DutyPayDTO> getDutyPayInfos(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getPrePayingAmount(DutyPrepayInfoDTO dutyPre);

    List<DutyPayDTO> getPolicyDutyPayedAmount(@Param("policyNo") String policyNo);

    List<DutyPayDTO> getPolicyDutyPrePayedAmount(@Param("policyNo") String policyNo);

    List<DutyPayDTO> getPolicyDutyPayedAmountByReportNo(@Param("reportNo") String reportNo,
                                                        @Param("policyNo") String policyNo,
                                                        @Param("insuredCode") String insuredCode);

    BigDecimal getDutyHistoryAmount(MaxPayParam maxPayParam);

    BigDecimal getEstimateAmount(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);

    /**
     * 查询责任赔付信息
     * @param caseNo
     * @param caseTimes
     * @param planCode
     * @param dutyCode
     * @return
     */
    List<DutyPayDTO> getDutyPay(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes,
                                @Param("planCode") String planCode, @Param("dutyCode") String dutyCode);

    /**
     * 根据保单号+客户号，获取出险标责任出险次数
     *
     * @param policyNo
     * @param clientNo
     * @param reportNo
     * @return
     */
    List<DutyPayNumDTO> getDutyPayNum(@Param("policyNo") String policyNo, @Param("reportNo") String reportNo);

    /**
     * 根据客户信息获取历史赔付责任
     * @param req
     * @return
     */
    List<DutyPayDTO> listDutyPayByCustom(DutyPayByCustomReqDTO req);

    List<DutyPayInfoDTO> getDutyPayInfoByReportAndCaseTimes(@Param("reportNo") String reportNo);

    List<DutyPayInfoDTO> getDutyPayInfoBy(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("dutyCode") String dutyCode);

    List<DutyPayInfoDTO>  getDutyPayAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    DutyPayDTO getdutyShareInfo(String reportNo, String planCode, String dutyCode);
}
