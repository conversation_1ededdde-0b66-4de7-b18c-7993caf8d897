package com.paic.ncbs.claim.controller.investigate;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.mock.web.MockServletContext;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.doc.PrintController;
import com.paic.ncbs.claim.controller.report.TaskListController;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.file.service.FileCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.ServletContext;

@SpringBootTest
public class InvestigateControllerTransferApprovalTest {


    @Autowired
    private PrintController printController;

    @Autowired
    private TaskListController taskListController;

    private PrintEntrustDTO printEntrustDTO;

    @Autowired
    private FileCommonService fileCommonService;
    @Autowired
    private InvestigateService investigateService;


//    @Test
//    @DisplayName("查询")
//    public void testGetEntrustmentData() {
//
//    }
//
//    @Test
//    @DisplayName("事故场景查询")
//    public void testGetAccidentSceneData() {
//
//    }
//
//    @Test
//    @DisplayName("审核提交 - 同意")
//    public void testSubmitEntrustAuditAgree() {
//
//
//    }

    @Test
    @DisplayName("公估公司")
    public void testGetApprovalUsers1() {
        assertDoesNotThrow(() -> {
            ResponseResult<Object> result = investigateService.getServerInfoList();
            System.out.println("返回结果：" + result);
            assertNotNull(result);
            assertNotNull(result.getCode());
        });
    }


    private static final Logger log = LoggerFactory.getLogger(InvestigateControllerTransferApprovalTest.class);

    @Autowired
    private WebApplicationContext webApplicationContext;
    private ServletContext servletContext;
    private MockHttpServletRequest mockRequest;
    private MockHttpServletResponse mockResponse;
    private MockHttpSession mockSession;


    /**
     * 初始化WebServletContext并设置所有参数
     */
    private void initializeWebServletContext() {
        // 获取ServletContext
        servletContext = webApplicationContext.getServletContext();

        if (servletContext == null) {
            // 如果没有ServletContext，创建MockServletContext
            servletContext = new MockServletContext();
        }

        // 设置应用基本信息
        servletContext.setAttribute("applicationName", "ncbs-claim");
        servletContext.setAttribute("applicationVersion", "1.0.0");
        servletContext.setAttribute("environment", "test");

        // 设置数据库连接信息
        servletContext.setAttribute("datasource.url", "*******************************************");
        servletContext.setAttribute("datasource.username", "test_user");
        servletContext.setAttribute("datasource.driver", "com.mysql.cj.jdbc.Driver");

        // 设置Redis连接信息
        servletContext.setAttribute("redis.host", "localhost");
        servletContext.setAttribute("redis.port", "6379");
        servletContext.setAttribute("redis.database", "0");
        servletContext.setAttribute("redis.timeout", "3000");

        // 设置文件上传配置
        servletContext.setAttribute("file.upload.path", "/tmp/ncbs-claim/uploads");
        servletContext.setAttribute("file.upload.maxSize", "10485760"); // 10MB
        servletContext.setAttribute("file.upload.allowedTypes", "pdf,jpg,jpeg,png,doc,docx,xls,xlsx");

        // 设置业务配置参数
        servletContext.setAttribute("business.replevy.maxAmount", "1000000.00");
        servletContext.setAttribute("business.replevy.timeoutDays", "30");
        servletContext.setAttribute("business.replevy.approveLevel", "3");
        servletContext.setAttribute("business.replevy.autoApproveAmount", "50000.00");

        // 设置系统配置参数
        servletContext.setAttribute("system.pageSize", "20");
        servletContext.setAttribute("system.sessionTimeout", "1800"); // 30分钟
        servletContext.setAttribute("system.logLevel", "INFO");
        servletContext.setAttribute("system.enableCache", "true");

        // 设置安全配置参数
        servletContext.setAttribute("security.tokenExpireTime", "7200"); // 2小时
        servletContext.setAttribute("security.maxLoginAttempts", "5");
        servletContext.setAttribute("security.passwordMinLength", "8");
        servletContext.setAttribute("security.enableEncryption", "true");

        // 设置邮件配置参数
        servletContext.setAttribute("mail.smtp.host", "smtp.test.com");
        servletContext.setAttribute("mail.smtp.port", "587");
        servletContext.setAttribute("mail.smtp.username", "<EMAIL>");
        servletContext.setAttribute("mail.smtp.enableTLS", "true");

        // 设置消息队列配置
        servletContext.setAttribute("mq.host", "localhost");
        servletContext.setAttribute("mq.port", "5672");
        servletContext.setAttribute("mq.username", "guest");
        servletContext.setAttribute("mq.virtualHost", "/");

        // 设置第三方接口配置
        servletContext.setAttribute("api.payment.url", "https://api.test.com/payment");
        servletContext.setAttribute("api.payment.timeout", "30000");
        servletContext.setAttribute("api.payment.retryTimes", "3");
        servletContext.setAttribute("api.payment.appKey", "test_app_key");

        // 设置监控配置
        servletContext.setAttribute("monitor.enable", "true");
        servletContext.setAttribute("monitor.interval", "60"); // 60秒
        servletContext.setAttribute("monitor.alertThreshold", "80");

        // 设置缓存配置
        servletContext.setAttribute("cache.enable", "true");
        servletContext.setAttribute("cache.expireTime", "3600"); // 1小时
        servletContext.setAttribute("cache.maxSize", "1000");

        // 设置日志配置
        servletContext.setAttribute("log.path", "/var/log/ncbs-claim");
        servletContext.setAttribute("log.maxFileSize", "100MB");
        servletContext.setAttribute("log.maxHistory", "30");
        servletContext.setAttribute("log.pattern", "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n");

        // 设置测试专用参数
        servletContext.setAttribute("test.mode", "true");
        servletContext.setAttribute("test.mockData", "true");
        servletContext.setAttribute("test.skipValidation", "false");
        servletContext.setAttribute("test.debugMode", "true");

        // 设置追偿业务专用参数
        servletContext.setAttribute("replevy.workflow.enable", "true");
        servletContext.setAttribute("replevy.notification.enable", "true");
        servletContext.setAttribute("replevy.autoAssign.enable", "true");
        servletContext.setAttribute("replevy.document.required", "true");

        log.info("WebServletContext初始化完成，已设置所有参数");
    }

    /**
     * 初始化Mock对象和用户Session
     */
    private void initializeMockRequestAndUserSession() {
        // 创建Mock对象
        mockRequest = new MockHttpServletRequest();
        mockResponse = new MockHttpServletResponse();
        mockSession = new MockHttpSession();

        // 设置Session到Request
        mockRequest.setSession(mockSession);

        // 创建测试用户对象
        com.paic.ncbs.um.model.dto.UserInfoDTO userInfoDTO = createTestUserInfoDTO();

        // 将用户对象设置到Session中
        mockSession.setAttribute(Constants.CURR_USER, userInfoDTO);
        mockSession.setAttribute(Constants.CURR_COMCODE, userInfoDTO.getComCode());

        // 设置RequestContextHolder，使得静态方法能够获取到Request
        ServletRequestAttributes attributes = new ServletRequestAttributes(mockRequest, mockResponse);
        RequestContextHolder.setRequestAttributes(attributes);

        log.info("Mock Request和用户Session初始化完成，用户: {}, 机构: {}",
                userInfoDTO.getUserName(), userInfoDTO.getComCode());
    }

    /**
     * 创建测试用的UserInfoDTO对象
     * @return UserInfoDTO测试对象
     */
    private com.paic.ncbs.um.model.dto.UserInfoDTO createTestUserInfoDTO() {
        com.paic.ncbs.um.model.dto.UserInfoDTO userInfoDTO = new com.paic.ncbs.um.model.dto.UserInfoDTO();

        // 基本用户信息
        userInfoDTO.setUserCode("blueshen");           // 用户代码
        userInfoDTO.setUserName("测试用户沈文");             // 用户姓名
        userInfoDTO.setComCode("1001");                     // 机构代码
        userInfoDTO.setComName("测试机构");                 // 机构名称

        // 联系信息
        userInfoDTO.setEmail("<EMAIL>");         // 邮箱
        userInfoDTO.setMobile("13800138001");               // 手机号

//        log.debug("创建测试用户对象: {}", userInfoDTO.getUserName());
        return userInfoDTO;
    }


}